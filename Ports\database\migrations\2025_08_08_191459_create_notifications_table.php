<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('type');
            $table->morphs('notifiable');
            $table->text('data');
            $table->timestamp('read_at')->nullable();
            $table->timestamps();
        });

        // جدول إضافي للتنبيهات المخصصة للميناء
        Schema::create('port_alerts', function (Blueprint $table) {
            $table->id();
            $table->string('alert_number')->unique();
            $table->enum('type', ['arrival', 'departure', 'emergency', 'weather', 'maintenance', 'inspection', 'payment', 'system']);
            $table->enum('priority', ['low', 'normal', 'high', 'critical'])->default('normal');
            $table->string('title');
            $table->text('message');
            $table->json('recipients'); // قائمة المستلمين
            $table->json('channels'); // قنوات الإرسال (database, broadcast, email, sms)
            $table->morphs('alertable'); // العنصر المرتبط بالتنبيه
            $table->enum('status', ['pending', 'sent', 'delivered', 'failed'])->default('pending');
            $table->datetime('scheduled_at')->nullable();
            $table->datetime('sent_at')->nullable();
            $table->datetime('expires_at')->nullable();
            $table->json('delivery_status')->nullable(); // حالة التسليم لكل قناة
            $table->boolean('is_urgent')->default(false);
            $table->boolean('requires_acknowledgment')->default(false);
            $table->json('acknowledged_by')->nullable(); // من أقر بالاستلام
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['type', 'priority']);
            $table->index(['status', 'scheduled_at']);
            $table->index('alert_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
