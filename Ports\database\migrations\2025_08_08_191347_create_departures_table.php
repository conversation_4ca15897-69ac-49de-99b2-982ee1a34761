<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('departures', function (Blueprint $table) {
            $table->id();
            $table->foreignId('voyage_id')->constrained()->onDelete('cascade');
            $table->foreignId('arrival_id')->constrained()->onDelete('cascade');
            $table->string('departure_number')->unique();
            $table->datetime('requested_etd');
            $table->datetime('confirmed_etd')->nullable();
            $table->datetime('actual_departure')->nullable();
            $table->datetime('unberthing_time')->nullable();
            $table->datetime('pilot_disembark_time')->nullable();
            $table->enum('status', ['requested', 'clearance_pending', 'cleared', 'unberthed', 'departed'])
                ->default('requested');
            $table->text('departure_reason');
            $table->string('next_destination');
            $table->boolean('all_clearances_obtained')->default(false);
            $table->boolean('all_fees_paid')->default(false);
            $table->boolean('cargo_operations_completed')->default(false);
            $table->boolean('requires_pilot')->default(true);
            $table->boolean('requires_tugboat')->default(false);
            $table->json('clearance_status')->nullable(); // حالة تصاريح الجهات
            $table->json('outstanding_fees')->nullable(); // الرسوم المستحقة
            $table->json('final_documents')->nullable(); // الوثائق النهائية
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['status', 'confirmed_etd']);
            $table->index('departure_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('departures');
    }
};
