<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'department',
        'position',
        'is_active',
        'last_login_at',
        'timezone',
        'language',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'last_login_at' => 'datetime',
            'is_active' => 'boolean',
        ];
    }

    // العلاقات
    public function agent(): HasOne
    {
        return $this->hasOne(Agent::class);
    }

    public function pilot(): HasOne
    {
        return $this->hasOne(Pilot::class);
    }

    public function createdInvoices(): HasMany
    {
        return $this->hasMany(Invoice::class, 'created_by');
    }

    public function approvedInvoices(): HasMany
    {
        return $this->hasMany(Invoice::class, 'approved_by');
    }

    public function processedPayments(): HasMany
    {
        return $this->hasMany(Payment::class, 'processed_by');
    }

    public function inspections(): HasMany
    {
        return $this->hasMany(Inspection::class, 'inspector_id');
    }

    public function supervisedOperations(): HasMany
    {
        return $this->hasMany(Operation::class, 'supervisor_id');
    }

    public function operationLogs(): HasMany
    {
        return $this->hasMany(OperationLog::class);
    }

    public function pilotAssignments(): HasMany
    {
        return $this->hasMany(PilotAssignment::class, 'assigned_by');
    }

    public function requestedMaintenance(): HasMany
    {
        return $this->hasMany(MaintenanceRequest::class, 'requested_by');
    }

    public function assignedMaintenance(): HasMany
    {
        return $this->hasMany(MaintenanceRequest::class, 'assigned_to');
    }

    public function approvedMaintenance(): HasMany
    {
        return $this->hasMany(MaintenanceRequest::class, 'approved_by');
    }

    public function uploadedDocuments(): HasMany
    {
        return $this->hasMany(Document::class, 'uploaded_by');
    }

    public function verifiedDocuments(): HasMany
    {
        return $this->hasMany(Document::class, 'verified_by');
    }

    public function createdAlerts(): HasMany
    {
        return $this->hasMany(PortAlert::class, 'created_by');
    }

    public function approvedBerthBookings(): HasMany
    {
        return $this->hasMany(BerthBooking::class, 'approved_by');
    }

    // النطاقات
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByRole($query, $role)
    {
        return $query->whereHas('roles', function ($q) use ($role) {
            $q->where('name', $role);
        });
    }

    public function scopeByDepartment($query, $department)
    {
        return $query->where('department', $department);
    }

    // الخصائص المحسوبة
    public function getIsActiveAttribute(): bool
    {
        return $this->attributes['is_active'] ?? true;
    }

    public function getIsAgentAttribute(): bool
    {
        return $this->hasRole('agent') || $this->agent()->exists();
    }

    public function getIsPilotAttribute(): bool
    {
        return $this->hasRole('pilot') || $this->pilot()->exists();
    }

    public function getIsInspectorAttribute(): bool
    {
        return $this->hasRole('inspector');
    }

    public function getIsAdminAttribute(): bool
    {
        return $this->hasRole('admin');
    }

    public function getFullNameAttribute(): string
    {
        return $this->name;
    }

    public function getDisplayNameAttribute(): string
    {
        return $this->name . ($this->position ? ' (' . $this->position . ')' : '');
    }

    // دوال مساعدة
    public function canAccessModule(string $module): bool
    {
        return $this->hasPermissionTo("access_{$module}") || $this->hasRole('admin');
    }

    public function canManage(string $resource): bool
    {
        return $this->hasPermissionTo("manage_{$resource}") || $this->hasRole('admin');
    }

    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }

    public function getPreferredTimezone(): string
    {
        return $this->timezone ?? config('app.timezone');
    }

    public function getPreferredLanguage(): string
    {
        return $this->language ?? config('app.locale');
    }
}
