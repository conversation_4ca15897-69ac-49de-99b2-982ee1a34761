<?php

namespace App\Contracts;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;

interface RepositoryInterface
{
    /**
     * Get all records
     */
    public function all(): Collection;

    /**
     * Get paginated records
     */
    public function paginate(int $perPage = 15): LengthAwarePaginator;

    /**
     * Find record by ID
     */
    public function find(int $id): ?Model;

    /**
     * Find record by ID or fail
     */
    public function findOrFail(int $id): Model;

    /**
     * Find record by specific field
     */
    public function findBy(string $field, $value): ?Model;

    /**
     * Find records by specific field
     */
    public function findAllBy(string $field, $value): Collection;

    /**
     * Create new record
     */
    public function create(array $data): Model;

    /**
     * Update record
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete record
     */
    public function delete(int $id): bool;

    /**
     * Get records with relationships
     */
    public function with(array $relations): self;

    /**
     * Apply where condition
     */
    public function where(string $field, $operator, $value = null): self;

    /**
     * Apply where in condition
     */
    public function whereIn(string $field, array $values): self;

    /**
     * Apply order by
     */
    public function orderBy(string $field, string $direction = 'asc'): self;

    /**
     * Get count of records
     */
    public function count(): int;

    /**
     * Check if record exists
     */
    public function exists(): bool;

    /**
     * Get first record
     */
    public function first(): ?Model;

    /**
     * Get latest records
     */
    public function latest(string $field = 'created_at'): self;

    /**
     * Apply scope
     */
    public function scope(string $scope, ...$parameters): self;
}
