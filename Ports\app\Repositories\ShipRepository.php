<?php

namespace App\Repositories;

use App\Models\Ship;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;

class ShipRepository extends BaseRepository
{
    protected function getModel(): Model
    {
        return new Ship();
    }

    /**
     * Find ship by IMO number
     */
    public function findByImo(string $imoNumber): ?Ship
    {
        return $this->findBy('imo_number', $imoNumber);
    }

    /**
     * Find ship by call sign
     */
    public function findByCallSign(string $callSign): ?Ship
    {
        return $this->findBy('call_sign', $callSign);
    }

    /**
     * Get ships by type
     */
    public function getByType(string $type): Collection
    {
        return $this->scope('byType', $type)->all();
    }

    /**
     * Get ships by flag country
     */
    public function getByFlag(string $country): Collection
    {
        return $this->scope('byFlag', $country)->all();
    }

    /**
     * Get active ships
     */
    public function getActive(): Collection
    {
        return $this->scope('active')->all();
    }

    /**
     * Search ships by name
     */
    public function searchByName(string $term): Collection
    {
        return $this->search($term, ['name', 'name_en'])->all();
    }

    /**
     * Get ships with current voyages
     */
    public function getWithCurrentVoyages(): Collection
    {
        return $this->query
            ->with(['voyages' => function ($query) {
                $query->whereIn('status', ['planned', 'arrived', 'berthed', 'working'])
                    ->latest();
            }])
            ->get();
    }

    /**
     * Get ships by size range
     */
    public function getBySizeRange(float $minLength = null, float $maxLength = null): Collection
    {
        $query = $this->query;

        if ($minLength) {
            $query->where('length', '>=', $minLength);
        }

        if ($maxLength) {
            $query->where('length', '<=', $maxLength);
        }

        return $query->get();
    }

    /**
     * Get ships by tonnage range
     */
    public function getByTonnageRange(float $minTonnage = null, float $maxTonnage = null): Collection
    {
        $query = $this->query;

        if ($minTonnage) {
            $query->where('gross_tonnage', '>=', $minTonnage);
        }

        if ($maxTonnage) {
            $query->where('gross_tonnage', '<=', $maxTonnage);
        }

        return $query->get();
    }

    /**
     * Get ships statistics
     */
    public function getStatistics(): array
    {
        $total = $this->count();
        $active = $this->scope('active')->count();
        
        $byType = $this->model
            ->selectRaw('ship_type, COUNT(*) as count')
            ->groupBy('ship_type')
            ->pluck('count', 'ship_type')
            ->toArray();

        $byFlag = $this->model
            ->selectRaw('flag_country, COUNT(*) as count')
            ->groupBy('flag_country')
            ->orderByDesc('count')
            ->limit(10)
            ->pluck('count', 'flag_country')
            ->toArray();

        $averageAge = $this->model
            ->selectRaw('AVG(YEAR(NOW()) - year_built) as avg_age')
            ->value('avg_age');

        return [
            'total' => $total,
            'active' => $active,
            'by_type' => $byType,
            'by_flag' => $byFlag,
            'average_age' => round($averageAge ?? 0, 1),
        ];
    }

    /**
     * Get ships for dropdown/select
     */
    public function getForSelect(): Collection
    {
        return $this->query
            ->select('id', 'name', 'imo_number', 'ship_type', 'status')
            ->where('status', 'active')
            ->orderBy('name')
            ->get()
            ->map(function ($ship) {
                return [
                    'id' => $ship->id,
                    'name' => $ship->name . ' (IMO: ' . $ship->imo_number . ')',
                    'type' => $ship->ship_type,
                    'status' => $ship->status,
                ];
            });
    }

    /**
     * Get ships with voyage history
     */
    public function getWithVoyageHistory(int $limit = 10): Collection
    {
        return $this->query
            ->with(['voyages' => function ($query) use ($limit) {
                $query->latest()->limit($limit);
            }])
            ->get();
    }

    /**
     * Get ships by age range
     */
    public function getByAgeRange(int $minAge = null, int $maxAge = null): Collection
    {
        $query = $this->query;

        if ($minAge) {
            $query->whereRaw('(YEAR(NOW()) - year_built) >= ?', [$minAge]);
        }

        if ($maxAge) {
            $query->whereRaw('(YEAR(NOW()) - year_built) <= ?', [$maxAge]);
        }

        return $query->get();
    }

    /**
     * Get ships that can fit in berth
     */
    public function getCompatibleWithBerth(int $berthId): Collection
    {
        $berth = \App\Models\Berth::findOrFail($berthId);

        return $this->query
            ->where('length', '<=', $berth->max_loa)
            ->where('draft', '<=', $berth->max_draft)
            ->where('deadweight', '<=', $berth->max_dwt)
            ->where('status', 'active')
            ->get();
    }

    /**
     * Get ships currently in port
     */
    public function getCurrentlyInPort(): Collection
    {
        return $this->query
            ->whereHas('voyages', function ($query) {
                $query->whereIn('status', ['arrived', 'berthed', 'working'])
                    ->whereHas('arrival', function ($q) {
                        $q->whereNotNull('actual_arrival');
                    })
                    ->whereDoesntHave('departure', function ($q) {
                        $q->whereNotNull('actual_departure');
                    });
            })
            ->with(['voyages.arrival', 'voyages.departure'])
            ->get();
    }

    /**
     * Get ships expected today
     */
    public function getExpectedToday(): Collection
    {
        return $this->query
            ->whereHas('voyages', function ($query) {
                $query->where('voyage_type', 'arrival')
                    ->whereDate('eta', today())
                    ->whereIn('status', ['planned', 'confirmed']);
            })
            ->with(['voyages' => function ($query) {
                $query->where('voyage_type', 'arrival')
                    ->whereDate('eta', today())
                    ->with(['agent', 'arrival']);
            }])
            ->get();
    }

    /**
     * Get ships departing today
     */
    public function getDepartingToday(): Collection
    {
        return $this->query
            ->whereHas('voyages', function ($query) {
                $query->where('voyage_type', 'departure')
                    ->whereDate('etd', today())
                    ->whereIn('status', ['berthed', 'working']);
            })
            ->with(['voyages' => function ($query) {
                $query->where('voyage_type', 'departure')
                    ->whereDate('etd', today())
                    ->with(['agent', 'departure']);
            }])
            ->get();
    }

    /**
     * Update ship status
     */
    public function updateStatus(int $shipId, string $status): bool
    {
        return $this->update($shipId, ['status' => $status]);
    }

    /**
     * Bulk update ship statuses
     */
    public function bulkUpdateStatus(array $shipIds, string $status): int
    {
        return $this->model->whereIn('id', $shipIds)->update(['status' => $status]);
    }

    /**
     * Get ships requiring inspection
     */
    public function getRequiringInspection(): Collection
    {
        return $this->query
            ->whereHas('voyages', function ($query) {
                $query->whereIn('status', ['arrived', 'berthed'])
                    ->whereDoesntHave('inspections', function ($q) {
                        $q->where('status', 'completed')
                            ->where('created_at', '>=', now()->subDays(30));
                    });
            })
            ->with(['voyages.inspections'])
            ->get();
    }

    /**
     * Get ship performance metrics
     */
    public function getPerformanceMetrics(int $shipId): array
    {
        $ship = $this->findOrFail($shipId);
        
        $voyages = $ship->voyages()
            ->where('status', 'completed')
            ->with(['arrival', 'departure'])
            ->get();

        $totalVoyages = $voyages->count();
        $averagePortStay = $voyages->avg(function ($voyage) {
            if ($voyage->arrival && $voyage->departure && 
                $voyage->arrival->actual_arrival && $voyage->departure->actual_departure) {
                return $voyage->arrival->actual_arrival->diffInHours($voyage->departure->actual_departure);
            }
            return null;
        });

        $onTimeArrivals = $voyages->filter(function ($voyage) {
            return $voyage->arrival && 
                   $voyage->arrival->actual_arrival && 
                   $voyage->arrival->confirmed_eta &&
                   $voyage->arrival->actual_arrival <= $voyage->arrival->confirmed_eta->addHours(2);
        })->count();

        return [
            'total_voyages' => $totalVoyages,
            'average_port_stay_hours' => round($averagePortStay ?? 0, 2),
            'on_time_arrival_rate' => $totalVoyages > 0 ? round(($onTimeArrivals / $totalVoyages) * 100, 2) : 0,
            'last_visit' => $voyages->max('created_at'),
        ];
    }
}
