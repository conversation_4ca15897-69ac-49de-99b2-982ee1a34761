<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { ref } from 'vue';

const props = defineProps({
    agent: Object
});

const getStatusColor = (status) => {
    const colors = {
        'active': 'bg-green-100 text-green-800',
        'inactive': 'bg-gray-100 text-gray-800',
        'suspended': 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
};

const getStatusText = (status) => {
    const texts = {
        'active': 'نشط',
        'inactive': 'غير نشط',
        'suspended': 'معلق'
    };
    return texts[status] || status;
};

const getServiceName = (service) => {
    const names = {
        'ship_agency': 'وكالة ملاحية',
        'cargo_handling': 'مناولة البضائع',
        'customs_clearance': 'تخليص جمركي',
        'crew_services': 'خدمات الطاقم',
        'supply_services': 'خدمات التموين',
        'waste_management': 'إدارة النفايات',
        'bunker_services': 'خدمات الوقود',
        'repair_services': 'خدمات الإصلاح'
    };
    return names[service] || service;
};

const updateStatus = (newStatus) => {
    if (confirm(`هل أنت متأكد من تغيير حالة الوكيل إلى "${getStatusText(newStatus)}"؟`)) {
        router.patch(route('agents.update-status', props.agent.id), {
            status: newStatus
        });
    }
};

const deleteAgent = () => {
    if (confirm('هل أنت متأكد من حذف هذا الوكيل؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        router.delete(route('agents.destroy', props.agent.id));
    }
};
</script>

<template>
    <Head :title="`الوكيل الملاحي - ${agent.company_name}`" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    تفاصيل الوكيل الملاحي
                </h2>
                <div class="flex space-x-2">
                    <Link :href="route('agents.edit', agent.id)" 
                          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        تعديل
                    </Link>
                    <Link :href="route('agents.index')" 
                          class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                        العودة للقائمة
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Agent Header -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="flex justify-between items-start">
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900">{{ agent.company_name }}</h1>
                                <p v-if="agent.company_name_en" class="text-lg text-gray-600">{{ agent.company_name_en }}</p>
                                <p class="text-sm text-gray-500 mt-2">رقم الترخيص: {{ agent.license_number }}</p>
                            </div>
                            <div class="text-right">
                                <span :class="getStatusColor(agent.status)" 
                                      class="inline-flex px-3 py-1 text-sm font-semibold rounded-full">
                                    {{ getStatusText(agent.status) }}
                                </span>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500">انتهاء الترخيص:</p>
                                    <p class="text-sm font-medium" 
                                       :class="new Date(agent.license_expiry) < new Date() ? 'text-red-600' : 'text-gray-900'">
                                        {{ new Date(agent.license_expiry).toLocaleDateString('ar-SA') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Main Information -->
                    <div class="lg:col-span-2 space-y-6">
                        <!-- Contact Information -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات الاتصال</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">الشخص المسؤول</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ agent.contact_person }}</p>
                                        <p v-if="agent.contact_person_en" class="text-sm text-gray-600">{{ agent.contact_person_en }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">البريد الإلكتروني</label>
                                        <p class="mt-1 text-sm text-gray-900">
                                            <a :href="`mailto:${agent.email}`" class="text-blue-600 hover:text-blue-800">
                                                {{ agent.email }}
                                            </a>
                                        </p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">رقم الهاتف</label>
                                        <p class="mt-1 text-sm text-gray-900">
                                            <a :href="`tel:${agent.phone}`" class="text-blue-600 hover:text-blue-800">
                                                {{ agent.phone }}
                                            </a>
                                        </p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">الفاكس</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ agent.fax || 'غير محدد' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Address Information -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات العنوان</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">العنوان (عربي)</label>
                                        <p class="mt-1 text-sm text-gray-900 whitespace-pre-line">{{ agent.address }}</p>
                                    </div>
                                    <div v-if="agent.address_en">
                                        <label class="block text-sm font-medium text-gray-500">العنوان (إنجليزي)</label>
                                        <p class="mt-1 text-sm text-gray-900 whitespace-pre-line">{{ agent.address_en }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Services -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">الخدمات المقدمة</h3>
                                <div v-if="agent.services && agent.services.length > 0" class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                    <div v-for="service in agent.services" :key="service" 
                                         class="flex items-center p-3 bg-blue-50 rounded-lg">
                                        <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-sm font-medium text-blue-900">{{ getServiceName(service) }}</span>
                                    </div>
                                </div>
                                <p v-else class="text-sm text-gray-500">لم يتم تحديد خدمات</p>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div v-if="agent.notes" class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">ملاحظات</h3>
                                <p class="text-sm text-gray-900 whitespace-pre-line">{{ agent.notes }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="space-y-6">
                        <!-- Quick Actions -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">إجراءات سريعة</h3>
                                <div class="space-y-3">
                                    <Link :href="route('agents.dashboard', agent.id)" 
                                          class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center block">
                                        لوحة التحكم
                                    </Link>
                                    
                                    <div class="relative">
                                        <select @change="updateStatus($event.target.value)" 
                                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <option value="">تغيير الحالة</option>
                                            <option v-if="agent.status !== 'active'" value="active">تفعيل</option>
                                            <option v-if="agent.status !== 'inactive'" value="inactive">إلغاء التفعيل</option>
                                            <option v-if="agent.status !== 'suspended'" value="suspended">تعليق</option>
                                        </select>
                                    </div>

                                    <button @click="deleteAgent" 
                                            class="w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                        حذف الوكيل
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Statistics -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">إحصائيات</h3>
                                <div class="space-y-4">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-500">إجمالي الرحلات</span>
                                        <span class="text-sm font-medium text-gray-900">{{ agent.voyages?.length || 0 }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-500">الرحلات النشطة</span>
                                        <span class="text-sm font-medium text-gray-900">
                                            {{ agent.voyages?.filter(v => ['planned', 'arrived', 'berthed', 'working'].includes(v.status)).length || 0 }}
                                        </span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-500">الفواتير المعلقة</span>
                                        <span class="text-sm font-medium text-gray-900">{{ agent.invoices?.filter(i => i.status === 'sent').length || 0 }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-500">تاريخ التسجيل</span>
                                        <span class="text-sm font-medium text-gray-900">
                                            {{ new Date(agent.created_at).toLocaleDateString('ar-SA') }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div v-if="agent.voyages && agent.voyages.length > 0" class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">آخر الرحلات</h3>
                                <div class="space-y-3">
                                    <div v-for="voyage in agent.voyages.slice(0, 5)" :key="voyage.id" 
                                         class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">{{ voyage.ship?.name }}</p>
                                            <p class="text-xs text-gray-500">{{ voyage.voyage_number }}</p>
                                        </div>
                                        <span class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                                            {{ voyage.status }}
                                        </span>
                                    </div>
                                </div>
                                <Link :href="route('voyages.index', { agent_id: agent.id })" 
                                      class="block text-center text-sm text-blue-600 hover:text-blue-800 mt-3">
                                    عرض جميع الرحلات
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
