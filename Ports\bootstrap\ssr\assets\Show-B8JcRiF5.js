import { unref, withCtx, createTextVNode, createVNode, createBlock, createCommentVNode, toDisplayString, openBlock, Fragment, renderList, useSSRContext } from "vue";
import { ssrRenderComponent, ssrInterpolate, ssrRenderClass, ssrRenderAttr, ssrRenderList } from "vue/server-renderer";
import { _ as _sfc_main$1 } from "./AuthenticatedLayout-DMuuhipg.js";
import { Head, Link, router } from "@inertiajs/vue3";
import "./ApplicationLogo-B2173abF.js";
import "./_plugin-vue_export-helper-1tPrXgE0.js";
const _sfc_main = {
  __name: "Show",
  __ssrInlineRender: true,
  props: {
    agent: Object
  },
  setup(__props) {
    const props = __props;
    const getStatusColor = (status) => {
      const colors = {
        "active": "bg-green-100 text-green-800",
        "inactive": "bg-gray-100 text-gray-800",
        "suspended": "bg-red-100 text-red-800"
      };
      return colors[status] || "bg-gray-100 text-gray-800";
    };
    const getStatusText = (status) => {
      const texts = {
        "active": "نشط",
        "inactive": "غير نشط",
        "suspended": "معلق"
      };
      return texts[status] || status;
    };
    const getServiceName = (service) => {
      const names = {
        "ship_agency": "وكالة ملاحية",
        "cargo_handling": "مناولة البضائع",
        "customs_clearance": "تخليص جمركي",
        "crew_services": "خدمات الطاقم",
        "supply_services": "خدمات التموين",
        "waste_management": "إدارة النفايات",
        "bunker_services": "خدمات الوقود",
        "repair_services": "خدمات الإصلاح"
      };
      return names[service] || service;
    };
    const updateStatus = (newStatus) => {
      if (confirm(`هل أنت متأكد من تغيير حالة الوكيل إلى "${getStatusText(newStatus)}"؟`)) {
        router.patch(route("agents.update-status", props.agent.id), {
          status: newStatus
        });
      }
    };
    const deleteAgent = () => {
      if (confirm("هل أنت متأكد من حذف هذا الوكيل؟ هذا الإجراء لا يمكن التراجع عنه.")) {
        router.delete(route("agents.destroy", props.agent.id));
      }
    };
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<!--[-->`);
      _push(ssrRenderComponent(unref(Head), {
        title: `الوكيل الملاحي - ${__props.agent.company_name}`
      }, null, _parent));
      _push(ssrRenderComponent(_sfc_main$1, null, {
        header: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="flex justify-between items-center"${_scopeId}><h2 class="font-semibold text-xl text-gray-800 leading-tight"${_scopeId}> تفاصيل الوكيل الملاحي </h2><div class="flex space-x-2"${_scopeId}>`);
            _push2(ssrRenderComponent(unref(Link), {
              href: _ctx.route("agents.edit", __props.agent.id),
              class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(` تعديل `);
                } else {
                  return [
                    createTextVNode(" تعديل ")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(unref(Link), {
              href: _ctx.route("agents.index"),
              class: "bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(` العودة للقائمة `);
                } else {
                  return [
                    createTextVNode(" العودة للقائمة ")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`</div></div>`);
          } else {
            return [
              createVNode("div", { class: "flex justify-between items-center" }, [
                createVNode("h2", { class: "font-semibold text-xl text-gray-800 leading-tight" }, " تفاصيل الوكيل الملاحي "),
                createVNode("div", { class: "flex space-x-2" }, [
                  createVNode(unref(Link), {
                    href: _ctx.route("agents.edit", __props.agent.id),
                    class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                  }, {
                    default: withCtx(() => [
                      createTextVNode(" تعديل ")
                    ]),
                    _: 1
                  }, 8, ["href"]),
                  createVNode(unref(Link), {
                    href: _ctx.route("agents.index"),
                    class: "bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                  }, {
                    default: withCtx(() => [
                      createTextVNode(" العودة للقائمة ")
                    ]),
                    _: 1
                  }, 8, ["href"])
                ])
              ])
            ];
          }
        }),
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="py-12"${_scopeId}><div class="max-w-7xl mx-auto sm:px-6 lg:px-8"${_scopeId}><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"${_scopeId}><div class="p-6"${_scopeId}><div class="flex justify-between items-start"${_scopeId}><div${_scopeId}><h1 class="text-2xl font-bold text-gray-900"${_scopeId}>${ssrInterpolate(__props.agent.company_name)}</h1>`);
            if (__props.agent.company_name_en) {
              _push2(`<p class="text-lg text-gray-600"${_scopeId}>${ssrInterpolate(__props.agent.company_name_en)}</p>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`<p class="text-sm text-gray-500 mt-2"${_scopeId}>رقم الترخيص: ${ssrInterpolate(__props.agent.license_number)}</p></div><div class="text-right"${_scopeId}><span class="${ssrRenderClass([getStatusColor(__props.agent.status), "inline-flex px-3 py-1 text-sm font-semibold rounded-full"])}"${_scopeId}>${ssrInterpolate(getStatusText(__props.agent.status))}</span><div class="mt-2"${_scopeId}><p class="text-sm text-gray-500"${_scopeId}>انتهاء الترخيص:</p><p class="${ssrRenderClass([new Date(__props.agent.license_expiry) < /* @__PURE__ */ new Date() ? "text-red-600" : "text-gray-900", "text-sm font-medium"])}"${_scopeId}>${ssrInterpolate(new Date(__props.agent.license_expiry).toLocaleDateString("ar-SA"))}</p></div></div></div></div></div><div class="grid grid-cols-1 lg:grid-cols-3 gap-6"${_scopeId}><div class="lg:col-span-2 space-y-6"${_scopeId}><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><h3 class="text-lg font-medium text-gray-900 mb-4"${_scopeId}>معلومات الاتصال</h3><div class="grid grid-cols-1 md:grid-cols-2 gap-6"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-gray-500"${_scopeId}>الشخص المسؤول</label><p class="mt-1 text-sm text-gray-900"${_scopeId}>${ssrInterpolate(__props.agent.contact_person)}</p>`);
            if (__props.agent.contact_person_en) {
              _push2(`<p class="text-sm text-gray-600"${_scopeId}>${ssrInterpolate(__props.agent.contact_person_en)}</p>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-gray-500"${_scopeId}>البريد الإلكتروني</label><p class="mt-1 text-sm text-gray-900"${_scopeId}><a${ssrRenderAttr("href", `mailto:${__props.agent.email}`)} class="text-blue-600 hover:text-blue-800"${_scopeId}>${ssrInterpolate(__props.agent.email)}</a></p></div><div${_scopeId}><label class="block text-sm font-medium text-gray-500"${_scopeId}>رقم الهاتف</label><p class="mt-1 text-sm text-gray-900"${_scopeId}><a${ssrRenderAttr("href", `tel:${__props.agent.phone}`)} class="text-blue-600 hover:text-blue-800"${_scopeId}>${ssrInterpolate(__props.agent.phone)}</a></p></div><div${_scopeId}><label class="block text-sm font-medium text-gray-500"${_scopeId}>الفاكس</label><p class="mt-1 text-sm text-gray-900"${_scopeId}>${ssrInterpolate(__props.agent.fax || "غير محدد")}</p></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><h3 class="text-lg font-medium text-gray-900 mb-4"${_scopeId}>معلومات العنوان</h3><div class="grid grid-cols-1 md:grid-cols-2 gap-6"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-gray-500"${_scopeId}>العنوان (عربي)</label><p class="mt-1 text-sm text-gray-900 whitespace-pre-line"${_scopeId}>${ssrInterpolate(__props.agent.address)}</p></div>`);
            if (__props.agent.address_en) {
              _push2(`<div${_scopeId}><label class="block text-sm font-medium text-gray-500"${_scopeId}>العنوان (إنجليزي)</label><p class="mt-1 text-sm text-gray-900 whitespace-pre-line"${_scopeId}>${ssrInterpolate(__props.agent.address_en)}</p></div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><h3 class="text-lg font-medium text-gray-900 mb-4"${_scopeId}>الخدمات المقدمة</h3>`);
            if (__props.agent.services && __props.agent.services.length > 0) {
              _push2(`<div class="grid grid-cols-2 md:grid-cols-3 gap-3"${_scopeId}><!--[-->`);
              ssrRenderList(__props.agent.services, (service) => {
                _push2(`<div class="flex items-center p-3 bg-blue-50 rounded-lg"${_scopeId}><svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"${_scopeId}></path></svg><span class="text-sm font-medium text-blue-900"${_scopeId}>${ssrInterpolate(getServiceName(service))}</span></div>`);
              });
              _push2(`<!--]--></div>`);
            } else {
              _push2(`<p class="text-sm text-gray-500"${_scopeId}>لم يتم تحديد خدمات</p>`);
            }
            _push2(`</div></div>`);
            if (__props.agent.notes) {
              _push2(`<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><h3 class="text-lg font-medium text-gray-900 mb-4"${_scopeId}>ملاحظات</h3><p class="text-sm text-gray-900 whitespace-pre-line"${_scopeId}>${ssrInterpolate(__props.agent.notes)}</p></div></div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div><div class="space-y-6"${_scopeId}><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><h3 class="text-lg font-medium text-gray-900 mb-4"${_scopeId}>إجراءات سريعة</h3><div class="space-y-3"${_scopeId}>`);
            _push2(ssrRenderComponent(unref(Link), {
              href: _ctx.route("agents.dashboard", __props.agent.id),
              class: "w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center block"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(` لوحة التحكم `);
                } else {
                  return [
                    createTextVNode(" لوحة التحكم ")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`<div class="relative"${_scopeId}><select class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"${_scopeId}><option value=""${_scopeId}>تغيير الحالة</option>`);
            if (__props.agent.status !== "active") {
              _push2(`<option value="active"${_scopeId}>تفعيل</option>`);
            } else {
              _push2(`<!---->`);
            }
            if (__props.agent.status !== "inactive") {
              _push2(`<option value="inactive"${_scopeId}>إلغاء التفعيل</option>`);
            } else {
              _push2(`<!---->`);
            }
            if (__props.agent.status !== "suspended") {
              _push2(`<option value="suspended"${_scopeId}>تعليق</option>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</select></div><button class="w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"${_scopeId}> حذف الوكيل </button></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><h3 class="text-lg font-medium text-gray-900 mb-4"${_scopeId}>إحصائيات</h3><div class="space-y-4"${_scopeId}><div class="flex justify-between"${_scopeId}><span class="text-sm text-gray-500"${_scopeId}>إجمالي الرحلات</span><span class="text-sm font-medium text-gray-900"${_scopeId}>${ssrInterpolate(__props.agent.voyages?.length || 0)}</span></div><div class="flex justify-between"${_scopeId}><span class="text-sm text-gray-500"${_scopeId}>الرحلات النشطة</span><span class="text-sm font-medium text-gray-900"${_scopeId}>${ssrInterpolate(__props.agent.voyages?.filter((v) => ["planned", "arrived", "berthed", "working"].includes(v.status)).length || 0)}</span></div><div class="flex justify-between"${_scopeId}><span class="text-sm text-gray-500"${_scopeId}>الفواتير المعلقة</span><span class="text-sm font-medium text-gray-900"${_scopeId}>${ssrInterpolate(__props.agent.invoices?.filter((i) => i.status === "sent").length || 0)}</span></div><div class="flex justify-between"${_scopeId}><span class="text-sm text-gray-500"${_scopeId}>تاريخ التسجيل</span><span class="text-sm font-medium text-gray-900"${_scopeId}>${ssrInterpolate(new Date(__props.agent.created_at).toLocaleDateString("ar-SA"))}</span></div></div></div></div>`);
            if (__props.agent.voyages && __props.agent.voyages.length > 0) {
              _push2(`<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><h3 class="text-lg font-medium text-gray-900 mb-4"${_scopeId}>آخر الرحلات</h3><div class="space-y-3"${_scopeId}><!--[-->`);
              ssrRenderList(__props.agent.voyages.slice(0, 5), (voyage) => {
                _push2(`<div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg"${_scopeId}><div${_scopeId}><p class="text-sm font-medium text-gray-900"${_scopeId}>${ssrInterpolate(voyage.ship?.name)}</p><p class="text-xs text-gray-500"${_scopeId}>${ssrInterpolate(voyage.voyage_number)}</p></div><span class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full"${_scopeId}>${ssrInterpolate(voyage.status)}</span></div>`);
              });
              _push2(`<!--]--></div>`);
              _push2(ssrRenderComponent(unref(Link), {
                href: _ctx.route("voyages.index", { agent_id: __props.agent.id }),
                class: "block text-center text-sm text-blue-600 hover:text-blue-800 mt-3"
              }, {
                default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(` عرض جميع الرحلات `);
                  } else {
                    return [
                      createTextVNode(" عرض جميع الرحلات ")
                    ];
                  }
                }),
                _: 1
              }, _parent2, _scopeId));
              _push2(`</div></div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div></div></div>`);
          } else {
            return [
              createVNode("div", { class: "py-12" }, [
                createVNode("div", { class: "max-w-7xl mx-auto sm:px-6 lg:px-8" }, [
                  createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6" }, [
                    createVNode("div", { class: "p-6" }, [
                      createVNode("div", { class: "flex justify-between items-start" }, [
                        createVNode("div", null, [
                          createVNode("h1", { class: "text-2xl font-bold text-gray-900" }, toDisplayString(__props.agent.company_name), 1),
                          __props.agent.company_name_en ? (openBlock(), createBlock("p", {
                            key: 0,
                            class: "text-lg text-gray-600"
                          }, toDisplayString(__props.agent.company_name_en), 1)) : createCommentVNode("", true),
                          createVNode("p", { class: "text-sm text-gray-500 mt-2" }, "رقم الترخيص: " + toDisplayString(__props.agent.license_number), 1)
                        ]),
                        createVNode("div", { class: "text-right" }, [
                          createVNode("span", {
                            class: [getStatusColor(__props.agent.status), "inline-flex px-3 py-1 text-sm font-semibold rounded-full"]
                          }, toDisplayString(getStatusText(__props.agent.status)), 3),
                          createVNode("div", { class: "mt-2" }, [
                            createVNode("p", { class: "text-sm text-gray-500" }, "انتهاء الترخيص:"),
                            createVNode("p", {
                              class: ["text-sm font-medium", new Date(__props.agent.license_expiry) < /* @__PURE__ */ new Date() ? "text-red-600" : "text-gray-900"]
                            }, toDisplayString(new Date(__props.agent.license_expiry).toLocaleDateString("ar-SA")), 3)
                          ])
                        ])
                      ])
                    ])
                  ]),
                  createVNode("div", { class: "grid grid-cols-1 lg:grid-cols-3 gap-6" }, [
                    createVNode("div", { class: "lg:col-span-2 space-y-6" }, [
                      createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                        createVNode("div", { class: "p-6" }, [
                          createVNode("h3", { class: "text-lg font-medium text-gray-900 mb-4" }, "معلومات الاتصال"),
                          createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-6" }, [
                            createVNode("div", null, [
                              createVNode("label", { class: "block text-sm font-medium text-gray-500" }, "الشخص المسؤول"),
                              createVNode("p", { class: "mt-1 text-sm text-gray-900" }, toDisplayString(__props.agent.contact_person), 1),
                              __props.agent.contact_person_en ? (openBlock(), createBlock("p", {
                                key: 0,
                                class: "text-sm text-gray-600"
                              }, toDisplayString(__props.agent.contact_person_en), 1)) : createCommentVNode("", true)
                            ]),
                            createVNode("div", null, [
                              createVNode("label", { class: "block text-sm font-medium text-gray-500" }, "البريد الإلكتروني"),
                              createVNode("p", { class: "mt-1 text-sm text-gray-900" }, [
                                createVNode("a", {
                                  href: `mailto:${__props.agent.email}`,
                                  class: "text-blue-600 hover:text-blue-800"
                                }, toDisplayString(__props.agent.email), 9, ["href"])
                              ])
                            ]),
                            createVNode("div", null, [
                              createVNode("label", { class: "block text-sm font-medium text-gray-500" }, "رقم الهاتف"),
                              createVNode("p", { class: "mt-1 text-sm text-gray-900" }, [
                                createVNode("a", {
                                  href: `tel:${__props.agent.phone}`,
                                  class: "text-blue-600 hover:text-blue-800"
                                }, toDisplayString(__props.agent.phone), 9, ["href"])
                              ])
                            ]),
                            createVNode("div", null, [
                              createVNode("label", { class: "block text-sm font-medium text-gray-500" }, "الفاكس"),
                              createVNode("p", { class: "mt-1 text-sm text-gray-900" }, toDisplayString(__props.agent.fax || "غير محدد"), 1)
                            ])
                          ])
                        ])
                      ]),
                      createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                        createVNode("div", { class: "p-6" }, [
                          createVNode("h3", { class: "text-lg font-medium text-gray-900 mb-4" }, "معلومات العنوان"),
                          createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-6" }, [
                            createVNode("div", null, [
                              createVNode("label", { class: "block text-sm font-medium text-gray-500" }, "العنوان (عربي)"),
                              createVNode("p", { class: "mt-1 text-sm text-gray-900 whitespace-pre-line" }, toDisplayString(__props.agent.address), 1)
                            ]),
                            __props.agent.address_en ? (openBlock(), createBlock("div", { key: 0 }, [
                              createVNode("label", { class: "block text-sm font-medium text-gray-500" }, "العنوان (إنجليزي)"),
                              createVNode("p", { class: "mt-1 text-sm text-gray-900 whitespace-pre-line" }, toDisplayString(__props.agent.address_en), 1)
                            ])) : createCommentVNode("", true)
                          ])
                        ])
                      ]),
                      createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                        createVNode("div", { class: "p-6" }, [
                          createVNode("h3", { class: "text-lg font-medium text-gray-900 mb-4" }, "الخدمات المقدمة"),
                          __props.agent.services && __props.agent.services.length > 0 ? (openBlock(), createBlock("div", {
                            key: 0,
                            class: "grid grid-cols-2 md:grid-cols-3 gap-3"
                          }, [
                            (openBlock(true), createBlock(Fragment, null, renderList(__props.agent.services, (service) => {
                              return openBlock(), createBlock("div", {
                                key: service,
                                class: "flex items-center p-3 bg-blue-50 rounded-lg"
                              }, [
                                (openBlock(), createBlock("svg", {
                                  class: "w-5 h-5 text-blue-500 mr-2",
                                  fill: "currentColor",
                                  viewBox: "0 0 20 20"
                                }, [
                                  createVNode("path", {
                                    "fill-rule": "evenodd",
                                    d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
                                    "clip-rule": "evenodd"
                                  })
                                ])),
                                createVNode("span", { class: "text-sm font-medium text-blue-900" }, toDisplayString(getServiceName(service)), 1)
                              ]);
                            }), 128))
                          ])) : (openBlock(), createBlock("p", {
                            key: 1,
                            class: "text-sm text-gray-500"
                          }, "لم يتم تحديد خدمات"))
                        ])
                      ]),
                      __props.agent.notes ? (openBlock(), createBlock("div", {
                        key: 0,
                        class: "bg-white overflow-hidden shadow-sm sm:rounded-lg"
                      }, [
                        createVNode("div", { class: "p-6" }, [
                          createVNode("h3", { class: "text-lg font-medium text-gray-900 mb-4" }, "ملاحظات"),
                          createVNode("p", { class: "text-sm text-gray-900 whitespace-pre-line" }, toDisplayString(__props.agent.notes), 1)
                        ])
                      ])) : createCommentVNode("", true)
                    ]),
                    createVNode("div", { class: "space-y-6" }, [
                      createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                        createVNode("div", { class: "p-6" }, [
                          createVNode("h3", { class: "text-lg font-medium text-gray-900 mb-4" }, "إجراءات سريعة"),
                          createVNode("div", { class: "space-y-3" }, [
                            createVNode(unref(Link), {
                              href: _ctx.route("agents.dashboard", __props.agent.id),
                              class: "w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center block"
                            }, {
                              default: withCtx(() => [
                                createTextVNode(" لوحة التحكم ")
                              ]),
                              _: 1
                            }, 8, ["href"]),
                            createVNode("div", { class: "relative" }, [
                              createVNode("select", {
                                onChange: ($event) => updateStatus($event.target.value),
                                class: "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                              }, [
                                createVNode("option", { value: "" }, "تغيير الحالة"),
                                __props.agent.status !== "active" ? (openBlock(), createBlock("option", {
                                  key: 0,
                                  value: "active"
                                }, "تفعيل")) : createCommentVNode("", true),
                                __props.agent.status !== "inactive" ? (openBlock(), createBlock("option", {
                                  key: 1,
                                  value: "inactive"
                                }, "إلغاء التفعيل")) : createCommentVNode("", true),
                                __props.agent.status !== "suspended" ? (openBlock(), createBlock("option", {
                                  key: 2,
                                  value: "suspended"
                                }, "تعليق")) : createCommentVNode("", true)
                              ], 40, ["onChange"])
                            ]),
                            createVNode("button", {
                              onClick: deleteAgent,
                              class: "w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                            }, " حذف الوكيل ")
                          ])
                        ])
                      ]),
                      createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                        createVNode("div", { class: "p-6" }, [
                          createVNode("h3", { class: "text-lg font-medium text-gray-900 mb-4" }, "إحصائيات"),
                          createVNode("div", { class: "space-y-4" }, [
                            createVNode("div", { class: "flex justify-between" }, [
                              createVNode("span", { class: "text-sm text-gray-500" }, "إجمالي الرحلات"),
                              createVNode("span", { class: "text-sm font-medium text-gray-900" }, toDisplayString(__props.agent.voyages?.length || 0), 1)
                            ]),
                            createVNode("div", { class: "flex justify-between" }, [
                              createVNode("span", { class: "text-sm text-gray-500" }, "الرحلات النشطة"),
                              createVNode("span", { class: "text-sm font-medium text-gray-900" }, toDisplayString(__props.agent.voyages?.filter((v) => ["planned", "arrived", "berthed", "working"].includes(v.status)).length || 0), 1)
                            ]),
                            createVNode("div", { class: "flex justify-between" }, [
                              createVNode("span", { class: "text-sm text-gray-500" }, "الفواتير المعلقة"),
                              createVNode("span", { class: "text-sm font-medium text-gray-900" }, toDisplayString(__props.agent.invoices?.filter((i) => i.status === "sent").length || 0), 1)
                            ]),
                            createVNode("div", { class: "flex justify-between" }, [
                              createVNode("span", { class: "text-sm text-gray-500" }, "تاريخ التسجيل"),
                              createVNode("span", { class: "text-sm font-medium text-gray-900" }, toDisplayString(new Date(__props.agent.created_at).toLocaleDateString("ar-SA")), 1)
                            ])
                          ])
                        ])
                      ]),
                      __props.agent.voyages && __props.agent.voyages.length > 0 ? (openBlock(), createBlock("div", {
                        key: 0,
                        class: "bg-white overflow-hidden shadow-sm sm:rounded-lg"
                      }, [
                        createVNode("div", { class: "p-6" }, [
                          createVNode("h3", { class: "text-lg font-medium text-gray-900 mb-4" }, "آخر الرحلات"),
                          createVNode("div", { class: "space-y-3" }, [
                            (openBlock(true), createBlock(Fragment, null, renderList(__props.agent.voyages.slice(0, 5), (voyage) => {
                              return openBlock(), createBlock("div", {
                                key: voyage.id,
                                class: "flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                              }, [
                                createVNode("div", null, [
                                  createVNode("p", { class: "text-sm font-medium text-gray-900" }, toDisplayString(voyage.ship?.name), 1),
                                  createVNode("p", { class: "text-xs text-gray-500" }, toDisplayString(voyage.voyage_number), 1)
                                ]),
                                createVNode("span", { class: "text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full" }, toDisplayString(voyage.status), 1)
                              ]);
                            }), 128))
                          ]),
                          createVNode(unref(Link), {
                            href: _ctx.route("voyages.index", { agent_id: __props.agent.id }),
                            class: "block text-center text-sm text-blue-600 hover:text-blue-800 mt-3"
                          }, {
                            default: withCtx(() => [
                              createTextVNode(" عرض جميع الرحلات ")
                            ]),
                            _: 1
                          }, 8, ["href"])
                        ])
                      ])) : createCommentVNode("", true)
                    ])
                  ])
                ])
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<!--]-->`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("resources/js/Pages/Agents/Show.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
export {
  _sfc_main as default
};
