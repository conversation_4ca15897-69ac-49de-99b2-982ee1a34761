<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Operation extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'voyage_id',
        'berth_booking_id',
        'operation_number',
        'type',
        'description',
        'planned_start',
        'planned_end',
        'actual_start',
        'actual_end',
        'status',
        'planned_quantity',
        'actual_quantity',
        'unit',
        'equipment_used',
        'crew_assigned',
        'cost',
        'completion_notes',
        'supervisor_id',
        'safety_checks',
        'quality_checks',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'planned_start' => 'datetime',
        'planned_end' => 'datetime',
        'actual_start' => 'datetime',
        'actual_end' => 'datetime',
        'planned_quantity' => 'decimal:2',
        'actual_quantity' => 'decimal:2',
        'cost' => 'decimal:2',
        'equipment_used' => 'array',
        'crew_assigned' => 'array',
        'safety_checks' => 'array',
        'quality_checks' => 'array',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'planned',
    ];

    // العلاقات
    public function voyage(): BelongsTo
    {
        return $this->belongsTo(Voyage::class);
    }

    public function berthBooking(): BelongsTo
    {
        return $this->belongsTo(BerthBooking::class);
    }

    public function supervisor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'supervisor_id');
    }

    public function logs(): HasMany
    {
        return $this->hasMany(OperationLog::class);
    }

    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    // النطاقات
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeActive($query)
    {
        return $query->whereIn('status', ['in_progress', 'paused']);
    }

    public function scopeScheduledToday($query)
    {
        return $query->whereDate('planned_start', today());
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'planned')
            ->where('planned_start', '<', now());
    }

    // الخصائص المحسوبة
    public function getIsActiveAttribute(): bool
    {
        return in_array($this->status, ['in_progress', 'paused']);
    }

    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'completed';
    }

    public function getPlannedDurationAttribute(): int
    {
        return $this->planned_start->diffInHours($this->planned_end);
    }

    public function getActualDurationAttribute(): ?int
    {
        if ($this->actual_start && $this->actual_end) {
            return $this->actual_start->diffInHours($this->actual_end);
        }
        return null;
    }

    public function getDurationVarianceAttribute(): ?int
    {
        $actualDuration = $this->actual_duration;
        if ($actualDuration !== null) {
            return $actualDuration - $this->planned_duration;
        }
        return null;
    }

    public function getQuantityVarianceAttribute(): ?float
    {
        if ($this->actual_quantity !== null && $this->planned_quantity !== null) {
            return $this->actual_quantity - $this->planned_quantity;
        }
        return null;
    }

    public function getProgressPercentageAttribute(): float
    {
        if ($this->status === 'completed') {
            return 100.0;
        }

        if ($this->planned_quantity && $this->actual_quantity) {
            return min(100.0, ($this->actual_quantity / $this->planned_quantity) * 100);
        }

        if ($this->actual_start && $this->status === 'in_progress') {
            $totalDuration = $this->planned_duration;
            $elapsedDuration = $this->actual_start->diffInHours(now());
            return min(100.0, ($elapsedDuration / $totalDuration) * 100);
        }

        return 0.0;
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->status === 'planned' && $this->planned_start < now();
    }

    public function getTimeRemainingAttribute(): ?int
    {
        if ($this->is_active && $this->planned_end) {
            return now()->diffInHours($this->planned_end, false);
        }
        return null;
    }

    // دوال مساعدة
    public function canStart(): bool
    {
        return $this->status === 'planned' && $this->planned_start <= now();
    }

    public function canPause(): bool
    {
        return $this->status === 'in_progress';
    }

    public function canResume(): bool
    {
        return $this->status === 'paused';
    }

    public function canComplete(): bool
    {
        return in_array($this->status, ['in_progress', 'paused']);
    }
}
