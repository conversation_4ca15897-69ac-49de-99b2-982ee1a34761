<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fees', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->string('name');
            $table->string('name_en')->nullable();
            $table->text('description')->nullable();
            $table->enum('category', ['port', 'pilotage', 'tugboat', 'berth', 'cargo', 'service', 'administrative']);
            $table->enum('calculation_method', ['fixed', 'per_ton', 'per_hour', 'per_day', 'per_container', 'percentage']);
            $table->decimal('base_rate', 10, 4);
            $table->decimal('minimum_charge', 10, 2)->nullable();
            $table->decimal('maximum_charge', 10, 2)->nullable();
            $table->string('currency', 3)->default('SYP');
            $table->json('rate_tiers')->nullable(); // شرائح الأسعار
            $table->json('applicable_ship_types')->nullable(); // أنواع السفن المطبقة عليها
            $table->json('applicable_cargo_types')->nullable(); // أنواع البضائع المطبقة عليها
            $table->boolean('is_active')->default(true);
            $table->date('effective_from');
            $table->date('effective_to')->nullable();
            $table->text('terms_conditions')->nullable();
            $table->json('exemptions')->nullable(); // إعفاءات
            $table->json('discounts')->nullable(); // خصومات
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['category', 'is_active']);
            $table->index(['effective_from', 'effective_to']);
            $table->index('code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fees');
    }
};
