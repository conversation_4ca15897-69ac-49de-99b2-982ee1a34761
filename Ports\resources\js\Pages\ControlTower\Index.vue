<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, onMounted, onUnmounted } from 'vue';

const props = defineProps({
    vessels: Array,
    berths: Array,
    weather: Object,
    tides: Object
});

// Reactive data
const selectedVessel = ref(null);
const mapContainer = ref(null);
const map = ref(null);
const vessels = ref(props.vessels || []);
const realTimeData = ref({
    weather: props.weather || {},
    tides: props.tides || {},
    alerts: []
});

// Map configuration
const mapConfig = {
    center: [35.7308, 35.7925], // Lattakia coordinates
    zoom: 12,
    style: 'mapbox://styles/mapbox/satellite-v9'
};

// Initialize map
const initializeMap = () => {
    if (!mapContainer.value) return;

    // For demo purposes, we'll create a simple map representation
    // In production, you would use Mapbox, Google Maps, or OpenLayers
    map.value = {
        center: mapConfig.center,
        zoom: mapConfig.zoom,
        vessels: vessels.value
    };

    renderVessels();
};

// Render vessels on map
const renderVessels = () => {
    // This would integrate with actual mapping library
    console.log('Rendering vessels on map:', vessels.value);
};

// Select vessel
const selectVessel = (vessel) => {
    selectedVessel.value = vessel;
    // Center map on vessel
    if (map.value && vessel.coordinates) {
        map.value.center = vessel.coordinates;
    }
};

// Get vessel status color
const getVesselStatusColor = (status) => {
    const colors = {
        'en_route': 'text-blue-500',
        'arrived': 'text-green-500',
        'in_port': 'text-yellow-500',
        'departed': 'text-gray-500',
        'delayed': 'text-red-500',
        'emergency': 'text-red-600'
    };
    return colors[status] || 'text-gray-400';
};

// Get berth status color
const getBerthStatusColor = (status) => {
    const colors = {
        'available': 'bg-green-500',
        'occupied': 'bg-red-500',
        'maintenance': 'bg-yellow-500',
        'reserved': 'bg-blue-500'
    };
    return colors[status] || 'bg-gray-400';
};

// Format time
const formatTime = (datetime) => {
    if (!datetime) return 'غير محدد';
    return new Date(datetime).toLocaleString('ar-SY', {
        hour: '2-digit',
        minute: '2-digit',
        day: '2-digit',
        month: '2-digit'
    });
};

// WebSocket connection for real-time updates
let websocket = null;

const connectWebSocket = () => {
    // In production, connect to actual WebSocket server
    // websocket = new WebSocket('ws://localhost:6001');
    
    // Simulate real-time updates
    setInterval(() => {
        // Simulate vessel position updates
        vessels.value.forEach(vessel => {
            if (vessel.status === 'en_route' && vessel.coordinates) {
                // Simulate movement
                vessel.coordinates[0] += (Math.random() - 0.5) * 0.001;
                vessel.coordinates[1] += (Math.random() - 0.5) * 0.001;
            }
        });
        
        // Update weather data
        realTimeData.value.weather.temperature = 25 + Math.random() * 10;
        realTimeData.value.weather.windSpeed = 5 + Math.random() * 15;
        
        renderVessels();
    }, 5000);
};

// Lifecycle hooks
onMounted(() => {
    initializeMap();
    connectWebSocket();
});

onUnmounted(() => {
    if (websocket) {
        websocket.close();
    }
});
</script>

<template>
    <Head title="برج المراقبة" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    برج المراقبة
                </h2>
                <div class="flex space-x-2">
                    <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                        متصل
                    </div>
                    <div class="text-sm text-gray-600">
                        آخر تحديث: {{ formatTime(new Date()) }}
                    </div>
                </div>
            </div>
        </template>

        <div class="py-6">
            <div class="max-w-full mx-auto sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <!-- Map Section -->
                    <div class="lg:col-span-3">
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="text-lg font-medium text-gray-900">الخريطة الحية</h3>
                                    <div class="flex space-x-2">
                                        <button class="bg-blue-500 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                                            تحديث
                                        </button>
                                        <button class="bg-gray-500 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm">
                                            ملء الشاشة
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- Map Container -->
                                <div 
                                    ref="mapContainer" 
                                    class="w-full h-96 bg-blue-100 rounded-lg relative overflow-hidden"
                                    style="background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><rect width=\"100\" height=\"100\" fill=\"%23e0f2fe\"/><path d=\"M20,80 Q50,60 80,80\" stroke=\"%234fc3f7\" stroke-width=\"2\" fill=\"none\"/><circle cx=\"30\" cy=\"70\" r=\"2\" fill=\"%23ff5722\"/><circle cx=\"60\" cy=\"65\" r=\"2\" fill=\"%234caf50\"/><circle cx=\"75\" cy=\"75\" r=\"2\" fill=\"%23ffc107\"/></svg>');"
                                >
                                    <!-- Vessel markers -->
                                    <div 
                                        v-for="vessel in vessels" 
                                        :key="vessel.id"
                                        class="absolute cursor-pointer transform -translate-x-1/2 -translate-y-1/2"
                                        :style="{
                                            left: (30 + vessel.id * 15) + '%',
                                            top: (40 + vessel.id * 10) + '%'
                                        }"
                                        @click="selectVessel(vessel)"
                                    >
                                        <div class="relative">
                                            <div 
                                                class="w-4 h-4 rounded-full border-2 border-white shadow-lg"
                                                :class="vessel.status === 'en_route' ? 'bg-blue-500' : 
                                                       vessel.status === 'arrived' ? 'bg-green-500' :
                                                       vessel.status === 'in_port' ? 'bg-yellow-500' : 'bg-gray-500'"
                                            ></div>
                                            <div class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 hover:opacity-100 transition-opacity">
                                                {{ vessel.ship?.name }}
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Port area -->
                                    <div class="absolute bottom-4 left-4 bg-gray-800 text-white p-2 rounded">
                                        <div class="text-xs font-bold">ميناء اللاذقية</div>
                                        <div class="text-xs">{{ berths.length }} رصيف</div>
                                    </div>

                                    <!-- Scale -->
                                    <div class="absolute bottom-4 right-4 bg-white p-2 rounded shadow">
                                        <div class="text-xs text-gray-600">المقياس</div>
                                        <div class="w-16 h-1 bg-black"></div>
                                        <div class="text-xs text-gray-600">1 كم</div>
                                    </div>
                                </div>

                                <!-- Map Legend -->
                                <div class="mt-4 flex flex-wrap gap-4 text-sm">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                                        <span>في الطريق</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                        <span>وصلت</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                                        <span>في الميناء</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-gray-500 rounded-full mr-2"></div>
                                        <span>غادرت</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Control Panel -->
                    <div class="space-y-6">
                        <!-- Weather Information -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">الأحوال الجوية</h3>
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">درجة الحرارة:</span>
                                        <span class="font-medium">{{ Math.round(realTimeData.weather.temperature || 25) }}°م</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">سرعة الرياح:</span>
                                        <span class="font-medium">{{ Math.round(realTimeData.weather.windSpeed || 10) }} عقدة</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">الرؤية:</span>
                                        <span class="font-medium">{{ realTimeData.weather.visibility || 'جيدة' }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">حالة البحر:</span>
                                        <span class="font-medium">{{ realTimeData.weather.seaState || 'هادئ' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Berth Status -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">حالة الأرصفة</h3>
                                <div class="space-y-2">
                                    <div v-for="berth in berths.slice(0, 6)" :key="berth.id" class="flex items-center justify-between">
                                        <span class="text-sm">{{ berth.name }}</span>
                                        <div class="flex items-center">
                                            <div 
                                                class="w-3 h-3 rounded-full mr-2"
                                                :class="getBerthStatusColor(berth.status)"
                                            ></div>
                                            <span class="text-xs text-gray-600">
                                                {{ berth.status === 'available' ? 'متاح' :
                                                   berth.status === 'occupied' ? 'مشغول' :
                                                   berth.status === 'maintenance' ? 'صيانة' : 'محجوز' }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Active Vessels -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">السفن النشطة</h3>
                                <div class="space-y-3 max-h-64 overflow-y-auto">
                                    <div 
                                        v-for="vessel in vessels" 
                                        :key="vessel.id"
                                        class="p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                                        :class="{ 'bg-blue-50 border-blue-200': selectedVessel?.id === vessel.id }"
                                        @click="selectVessel(vessel)"
                                    >
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <div class="font-medium text-sm">{{ vessel.ship?.name }}</div>
                                                <div class="text-xs text-gray-600">{{ vessel.voyage_number }}</div>
                                            </div>
                                            <div 
                                                class="text-xs px-2 py-1 rounded-full"
                                                :class="getVesselStatusColor(vessel.status)"
                                            >
                                                {{ vessel.status === 'en_route' ? 'في الطريق' :
                                                   vessel.status === 'arrived' ? 'وصلت' :
                                                   vessel.status === 'in_port' ? 'في الميناء' :
                                                   vessel.status === 'departed' ? 'غادرت' : vessel.status }}
                                            </div>
                                        </div>
                                        <div class="mt-2 text-xs text-gray-500">
                                            <div>الوصول المتوقع: {{ formatTime(vessel.estimated_arrival) }}</div>
                                            <div v-if="vessel.berth">الرصيف: {{ vessel.berth.name }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Vessel Details Modal -->
                <div 
                    v-if="selectedVessel" 
                    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
                    @click="selectedVessel = null"
                >
                    <div 
                        class="bg-white rounded-lg p-6 max-w-md w-full mx-4"
                        @click.stop
                    >
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium">{{ selectedVessel.ship?.name }}</h3>
                            <button 
                                @click="selectedVessel = null"
                                class="text-gray-400 hover:text-gray-600"
                            >
                                ✕
                            </button>
                        </div>
                        
                        <div class="space-y-3">
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-600">رقم الرحلة:</span>
                                    <div class="font-medium">{{ selectedVessel.voyage_number }}</div>
                                </div>
                                <div>
                                    <span class="text-gray-600">الحالة:</span>
                                    <div class="font-medium">{{ selectedVessel.status }}</div>
                                </div>
                                <div>
                                    <span class="text-gray-600">الوصول المتوقع:</span>
                                    <div class="font-medium">{{ formatTime(selectedVessel.estimated_arrival) }}</div>
                                </div>
                                <div>
                                    <span class="text-gray-600">المغادرة المتوقعة:</span>
                                    <div class="font-medium">{{ formatTime(selectedVessel.estimated_departure) }}</div>
                                </div>
                                <div>
                                    <span class="text-gray-600">نوع البضاعة:</span>
                                    <div class="font-medium">{{ selectedVessel.cargo_type }}</div>
                                </div>
                                <div>
                                    <span class="text-gray-600">الوكيل:</span>
                                    <div class="font-medium">{{ selectedVessel.agent?.name }}</div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6 flex justify-end space-x-3">
                            <button 
                                @click="selectedVessel = null"
                                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                            >
                                إغلاق
                            </button>
                            <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                عرض التفاصيل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>

<style scoped>
/* Custom styles for the control tower interface */
.vessel-marker {
    transition: all 0.3s ease;
}

.vessel-marker:hover {
    transform: scale(1.2);
}
</style>
