import{_ as S}from"./AuthenticatedLayout-CBV1ADW9.js";import{c as o,o as l,a as g,b as m,h as j,w as r,d as t,g as i,t as n,n as v,F as x,i as y,q as c,l as u,x as b}from"./app-CdA4UbU6.js";import"./ApplicationLogo-WdDnJ3OK.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const C={class:"flex justify-between items-center"},D={class:"flex space-x-2"},N={class:"py-12"},$={class:"max-w-7xl mx-auto sm:px-6 lg:px-8"},B={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},L={class:"p-6"},V={class:"flex justify-between items-start"},z={class:"text-2xl font-bold text-gray-900"},A={key:0,class:"text-lg text-gray-600"},F={class:"text-sm text-gray-500 mt-2"},T={class:"text-right"},q={class:"mt-2"},E={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},M={class:"lg:col-span-2 space-y-6"},O={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},G={class:"p-6"},H={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},I={class:"mt-1 text-sm text-gray-900"},J={key:0,class:"text-sm text-gray-600"},K={class:"mt-1 text-sm text-gray-900"},P=["href"],Q={class:"mt-1 text-sm text-gray-900"},R=["href"],U={class:"mt-1 text-sm text-gray-900"},W={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},X={class:"p-6"},Y={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Z={class:"mt-1 text-sm text-gray-900 whitespace-pre-line"},tt={key:0},et={class:"mt-1 text-sm text-gray-900 whitespace-pre-line"},st={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},nt={class:"p-6"},at={key:0,class:"grid grid-cols-2 md:grid-cols-3 gap-3"},ot={class:"text-sm font-medium text-blue-900"},lt={key:1,class:"text-sm text-gray-500"},dt={key:0,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},it={class:"p-6"},gt={class:"text-sm text-gray-900 whitespace-pre-line"},rt={class:"space-y-6"},mt={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},ct={class:"p-6"},ut={class:"space-y-3"},xt={class:"relative"},ht={key:0,value:"active"},ft={key:1,value:"inactive"},vt={key:2,value:"suspended"},yt={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},bt={class:"p-6"},wt={class:"space-y-4"},_t={class:"flex justify-between"},pt={class:"text-sm font-medium text-gray-900"},kt={class:"flex justify-between"},St={class:"text-sm font-medium text-gray-900"},jt={class:"flex justify-between"},Ct={class:"text-sm font-medium text-gray-900"},Dt={class:"flex justify-between"},Nt={class:"text-sm font-medium text-gray-900"},$t={key:0,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},Bt={class:"p-6"},Lt={class:"space-y-3"},Vt={class:"text-sm font-medium text-gray-900"},zt={class:"text-xs text-gray-500"},At={class:"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full"},Mt={__name:"Show",props:{agent:Object},setup(s){const h=s,w=a=>({active:"bg-green-100 text-green-800",inactive:"bg-gray-100 text-gray-800",suspended:"bg-red-100 text-red-800"})[a]||"bg-gray-100 text-gray-800",f=a=>({active:"نشط",inactive:"غير نشط",suspended:"معلق"})[a]||a,_=a=>({ship_agency:"وكالة ملاحية",cargo_handling:"مناولة البضائع",customs_clearance:"تخليص جمركي",crew_services:"خدمات الطاقم",supply_services:"خدمات التموين",waste_management:"إدارة النفايات",bunker_services:"خدمات الوقود",repair_services:"خدمات الإصلاح"})[a]||a,p=a=>{confirm(`هل أنت متأكد من تغيير حالة الوكيل إلى "${f(a)}"؟`)&&b.patch(route("agents.update-status",h.agent.id),{status:a})},k=()=>{confirm("هل أنت متأكد من حذف هذا الوكيل؟ هذا الإجراء لا يمكن التراجع عنه.")&&b.delete(route("agents.destroy",h.agent.id))};return(a,e)=>(l(),o(x,null,[g(m(j),{title:`الوكيل الملاحي - ${s.agent.company_name}`},null,8,["title"]),g(S,null,{header:r(()=>[t("div",C,[e[3]||(e[3]=t("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"}," تفاصيل الوكيل الملاحي ",-1)),t("div",D,[g(m(c),{href:a.route("agents.edit",s.agent.id),class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"},{default:r(()=>e[1]||(e[1]=[u(" تعديل ",-1)])),_:1,__:[1]},8,["href"]),g(m(c),{href:a.route("agents.index"),class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:r(()=>e[2]||(e[2]=[u(" العودة للقائمة ",-1)])),_:1,__:[2]},8,["href"])])])]),default:r(()=>[t("div",N,[t("div",$,[t("div",B,[t("div",L,[t("div",V,[t("div",null,[t("h1",z,n(s.agent.company_name),1),s.agent.company_name_en?(l(),o("p",A,n(s.agent.company_name_en),1)):i("",!0),t("p",F,"رقم الترخيص: "+n(s.agent.license_number),1)]),t("div",T,[t("span",{class:v([w(s.agent.status),"inline-flex px-3 py-1 text-sm font-semibold rounded-full"])},n(f(s.agent.status)),3),t("div",q,[e[4]||(e[4]=t("p",{class:"text-sm text-gray-500"},"انتهاء الترخيص:",-1)),t("p",{class:v(["text-sm font-medium",new Date(s.agent.license_expiry)<new Date?"text-red-600":"text-gray-900"])},n(new Date(s.agent.license_expiry).toLocaleDateString("ar-SA")),3)])])])])]),t("div",E,[t("div",M,[t("div",O,[t("div",G,[e[9]||(e[9]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"معلومات الاتصال",-1)),t("div",H,[t("div",null,[e[5]||(e[5]=t("label",{class:"block text-sm font-medium text-gray-500"},"الشخص المسؤول",-1)),t("p",I,n(s.agent.contact_person),1),s.agent.contact_person_en?(l(),o("p",J,n(s.agent.contact_person_en),1)):i("",!0)]),t("div",null,[e[6]||(e[6]=t("label",{class:"block text-sm font-medium text-gray-500"},"البريد الإلكتروني",-1)),t("p",K,[t("a",{href:`mailto:${s.agent.email}`,class:"text-blue-600 hover:text-blue-800"},n(s.agent.email),9,P)])]),t("div",null,[e[7]||(e[7]=t("label",{class:"block text-sm font-medium text-gray-500"},"رقم الهاتف",-1)),t("p",Q,[t("a",{href:`tel:${s.agent.phone}`,class:"text-blue-600 hover:text-blue-800"},n(s.agent.phone),9,R)])]),t("div",null,[e[8]||(e[8]=t("label",{class:"block text-sm font-medium text-gray-500"},"الفاكس",-1)),t("p",U,n(s.agent.fax||"غير محدد"),1)])])])]),t("div",W,[t("div",X,[e[12]||(e[12]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"معلومات العنوان",-1)),t("div",Y,[t("div",null,[e[10]||(e[10]=t("label",{class:"block text-sm font-medium text-gray-500"},"العنوان (عربي)",-1)),t("p",Z,n(s.agent.address),1)]),s.agent.address_en?(l(),o("div",tt,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-500"},"العنوان (إنجليزي)",-1)),t("p",et,n(s.agent.address_en),1)])):i("",!0)])])]),t("div",st,[t("div",nt,[e[14]||(e[14]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"الخدمات المقدمة",-1)),s.agent.services&&s.agent.services.length>0?(l(),o("div",at,[(l(!0),o(x,null,y(s.agent.services,d=>(l(),o("div",{key:d,class:"flex items-center p-3 bg-blue-50 rounded-lg"},[e[13]||(e[13]=t("svg",{class:"w-5 h-5 text-blue-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})],-1)),t("span",ot,n(_(d)),1)]))),128))])):(l(),o("p",lt,"لم يتم تحديد خدمات"))])]),s.agent.notes?(l(),o("div",dt,[t("div",it,[e[15]||(e[15]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"ملاحظات",-1)),t("p",gt,n(s.agent.notes),1)])])):i("",!0)]),t("div",rt,[t("div",mt,[t("div",ct,[e[18]||(e[18]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"إجراءات سريعة",-1)),t("div",ut,[g(m(c),{href:a.route("agents.dashboard",s.agent.id),class:"w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center block"},{default:r(()=>e[16]||(e[16]=[u(" لوحة التحكم ",-1)])),_:1,__:[16]},8,["href"]),t("div",xt,[t("select",{onChange:e[0]||(e[0]=d=>p(d.target.value)),class:"w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[e[17]||(e[17]=t("option",{value:""},"تغيير الحالة",-1)),s.agent.status!=="active"?(l(),o("option",ht,"تفعيل")):i("",!0),s.agent.status!=="inactive"?(l(),o("option",ft,"إلغاء التفعيل")):i("",!0),s.agent.status!=="suspended"?(l(),o("option",vt,"تعليق")):i("",!0)],32)]),t("button",{onClick:k,class:"w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"}," حذف الوكيل ")])])]),t("div",yt,[t("div",bt,[e[23]||(e[23]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"إحصائيات",-1)),t("div",wt,[t("div",_t,[e[19]||(e[19]=t("span",{class:"text-sm text-gray-500"},"إجمالي الرحلات",-1)),t("span",pt,n(s.agent.voyages?.length||0),1)]),t("div",kt,[e[20]||(e[20]=t("span",{class:"text-sm text-gray-500"},"الرحلات النشطة",-1)),t("span",St,n(s.agent.voyages?.filter(d=>["planned","arrived","berthed","working"].includes(d.status)).length||0),1)]),t("div",jt,[e[21]||(e[21]=t("span",{class:"text-sm text-gray-500"},"الفواتير المعلقة",-1)),t("span",Ct,n(s.agent.invoices?.filter(d=>d.status==="sent").length||0),1)]),t("div",Dt,[e[22]||(e[22]=t("span",{class:"text-sm text-gray-500"},"تاريخ التسجيل",-1)),t("span",Nt,n(new Date(s.agent.created_at).toLocaleDateString("ar-SA")),1)])])])]),s.agent.voyages&&s.agent.voyages.length>0?(l(),o("div",$t,[t("div",Bt,[e[25]||(e[25]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"آخر الرحلات",-1)),t("div",Lt,[(l(!0),o(x,null,y(s.agent.voyages.slice(0,5),d=>(l(),o("div",{key:d.id,class:"flex justify-between items-center p-3 bg-gray-50 rounded-lg"},[t("div",null,[t("p",Vt,n(d.ship?.name),1),t("p",zt,n(d.voyage_number),1)]),t("span",At,n(d.status),1)]))),128))]),g(m(c),{href:a.route("voyages.index",{agent_id:s.agent.id}),class:"block text-center text-sm text-blue-600 hover:text-blue-800 mt-3"},{default:r(()=>e[24]||(e[24]=[u(" عرض جميع الرحلات ",-1)])),_:1,__:[24]},8,["href"])])])):i("",!0)])])])])]),_:1})],64))}};export{Mt as default};
