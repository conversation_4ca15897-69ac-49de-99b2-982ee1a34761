import{m as g,c as d,o as i,a as n,b as u,h as T,w as c,d as t,t as o,f as p,p as k,v as M,k as z,g as B,F as m,i as C,n as w,q as v,l as _,s as S,x as L}from"./app-CdA4UbU6.js";import{_ as O}from"./AuthenticatedLayout-CBV1ADW9.js";import"./ApplicationLogo-WdDnJ3OK.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const N={class:"flex justify-between items-center"},U={class:"py-12"},q={class:"max-w-7xl mx-auto sm:px-6 lg:px-8"},I={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"},K={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},D={class:"p-6"},F={class:"flex items-center"},A={class:"ml-4"},E={class:"text-2xl font-semibold text-gray-900"},$={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},G={class:"p-6"},J={class:"flex items-center"},P={class:"ml-4"},Q={class:"text-2xl font-semibold text-gray-900"},R={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},W={class:"p-6"},X={class:"flex items-center"},Y={class:"ml-4"},Z={class:"text-2xl font-semibold text-gray-900"},tt={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},et={class:"p-6"},st={class:"flex items-center"},ot={class:"ml-4"},lt={class:"text-2xl font-semibold text-gray-900"},at={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},it={class:"p-6"},dt={class:"grid grid-cols-1 md:grid-cols-5 gap-4"},rt={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},nt={class:"overflow-x-auto"},ut={class:"min-w-full divide-y divide-gray-200"},ct={class:"bg-white divide-y divide-gray-200"},xt={class:"px-6 py-4 whitespace-nowrap"},gt={class:"text-sm font-medium text-gray-900"},pt={class:"text-sm text-gray-500"},mt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},vt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ht={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ft={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},yt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},bt={class:"px-6 py-4 whitespace-nowrap"},wt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},_t={class:"flex space-x-2"},kt={key:0,class:"px-6 py-3 border-t border-gray-200"},Mt={class:"flex justify-between items-center"},zt={class:"text-sm text-gray-700"},Ct={class:"flex space-x-1"},Vt=["innerHTML"],St={__name:"Index",props:{ships:Object,statistics:Object,filters:Object},setup(l){const x=l,h=g(x.filters.search||""),f=g(x.filters.type||""),y=g(x.filters.flag||""),b=g(x.filters.status||""),r=()=>{L.get(route("ships.index"),{search:h.value,type:f.value,flag:y.value,status:b.value},{preserveState:!0,replace:!0})},V=a=>({active:"bg-green-100 text-green-800",inactive:"bg-gray-100 text-gray-800",blacklisted:"bg-red-100 text-red-800"})[a]||"bg-gray-100 text-gray-800",j=a=>({active:"نشطة",inactive:"غير نشطة",blacklisted:"محظورة"})[a]||a,H=a=>({container:"حاويات",bulk:"بضائع سائبة",general:"بضائع عامة",liquid:"سوائل",passenger:"ركاب",ro_ro:"رو رو"})[a]||a;return(a,e)=>(i(),d(m,null,[n(u(T),{title:"السفن"}),n(O,null,{header:c(()=>[t("div",N,[e[5]||(e[5]=t("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"}," السفن ",-1)),n(u(v),{href:a.route("ships.create"),class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"},{default:c(()=>e[4]||(e[4]=[_(" إضافة سفينة جديدة ",-1)])),_:1,__:[4]},8,["href"])])]),default:c(()=>[t("div",U,[t("div",q,[t("div",I,[t("div",K,[t("div",D,[t("div",F,[e[7]||(e[7]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"})])])],-1)),t("div",A,[e[6]||(e[6]=t("p",{class:"text-sm font-medium text-gray-500"},"إجمالي السفن",-1)),t("p",E,o(l.statistics.total),1)])])])]),t("div",$,[t("div",G,[t("div",J,[e[9]||(e[9]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])])],-1)),t("div",P,[e[8]||(e[8]=t("p",{class:"text-sm font-medium text-gray-500"},"نشطة",-1)),t("p",Q,o(l.statistics.active),1)])])])]),t("div",R,[t("div",W,[t("div",X,[e[11]||(e[11]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"}),t("path",{d:"M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"})])])],-1)),t("div",Y,[e[10]||(e[10]=t("p",{class:"text-sm font-medium text-gray-500"},"متوسط العمر",-1)),t("p",Z,o(l.statistics.average_age)+" سنة",1)])])])]),t("div",tt,[t("div",et,[t("div",st,[e[13]||(e[13]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1zM3 7a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1V7zM3 12a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1z","clip-rule":"evenodd"})])])],-1)),t("div",ot,[e[12]||(e[12]=t("p",{class:"text-sm font-medium text-gray-500"},"أنواع السفن",-1)),t("p",lt,o(Object.keys(l.statistics.by_type||{}).length),1)])])])])]),t("div",at,[t("div",it,[t("div",dt,[t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"البحث",-1)),p(t("input",{"onUpdate:modelValue":e[0]||(e[0]=s=>h.value=s),onKeyup:k(r,["enter"]),type:"text",placeholder:"البحث بالاسم أو رقم IMO...",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},null,544),[[M,h.value]])]),t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"نوع السفينة",-1)),p(t("select",{"onUpdate:modelValue":e[1]||(e[1]=s=>f.value=s),onChange:r,class:"w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},e[15]||(e[15]=[t("option",{value:""},"جميع الأنواع",-1),t("option",{value:"container"},"حاويات",-1),t("option",{value:"bulk"},"بضائع سائبة",-1),t("option",{value:"general"},"بضائع عامة",-1),t("option",{value:"liquid"},"سوائل",-1),t("option",{value:"passenger"},"ركاب",-1),t("option",{value:"ro_ro"},"رو رو",-1)]),544),[[z,f.value]])]),t("div",null,[e[17]||(e[17]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"دولة العلم",-1)),p(t("input",{"onUpdate:modelValue":e[2]||(e[2]=s=>y.value=s),onKeyup:k(r,["enter"]),type:"text",placeholder:"دولة العلم...",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},null,544),[[M,y.value]])]),t("div",null,[e[19]||(e[19]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"الحالة",-1)),p(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>b.value=s),onChange:r,class:"w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},e[18]||(e[18]=[t("option",{value:""},"جميع الحالات",-1),t("option",{value:"active"},"نشطة",-1),t("option",{value:"inactive"},"غير نشطة",-1),t("option",{value:"blacklisted"},"محظورة",-1)]),544),[[z,b.value]])]),t("div",{class:"flex items-end"},[t("button",{onClick:r,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full"}," بحث ")])])])]),t("div",rt,[t("div",nt,[t("table",ut,[e[22]||(e[22]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," اسم السفينة "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," رقم IMO "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," النوع "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," دولة العلم "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," الحمولة الإجمالية "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," سنة البناء "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," الحالة "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," الإجراءات ")])],-1)),t("tbody",ct,[(i(!0),d(m,null,C(l.ships.data,s=>(i(),d("tr",{key:s.id,class:"hover:bg-gray-50"},[t("td",xt,[t("div",gt,o(s.name),1),t("div",pt,o(s.name_en),1)]),t("td",mt,o(s.imo_number),1),t("td",vt,o(H(s.ship_type)),1),t("td",ht,o(s.flag_country),1),t("td",ft,o(s.gross_tonnage?.toLocaleString())+" طن ",1),t("td",yt,o(s.year_built),1),t("td",bt,[t("span",{class:w([V(s.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(j(s.status)),3)]),t("td",wt,[t("div",_t,[n(u(v),{href:a.route("ships.show",s.id),class:"text-indigo-600 hover:text-indigo-900"},{default:c(()=>e[20]||(e[20]=[_(" عرض ",-1)])),_:2,__:[20]},1032,["href"]),n(u(v),{href:a.route("ships.edit",s.id),class:"text-green-600 hover:text-green-900"},{default:c(()=>e[21]||(e[21]=[_(" تعديل ",-1)])),_:2,__:[21]},1032,["href"])])])]))),128))])])]),l.ships.links?(i(),d("div",kt,[t("div",Mt,[t("div",zt," عرض "+o(l.ships.from)+" إلى "+o(l.ships.to)+" من "+o(l.ships.total)+" نتيجة ",1),t("div",Ct,[(i(!0),d(m,null,C(l.ships.links,s=>(i(),d(m,{key:s.label},[s.url?(i(),S(u(v),{key:0,href:s.url,class:w(["px-3 py-2 text-sm rounded-md",s.active?"bg-blue-500 text-white":"bg-white text-gray-700 hover:bg-gray-50 border border-gray-300"]),innerHTML:s.label},null,8,["href","class","innerHTML"])):(i(),d("span",{key:1,class:w(["px-3 py-2 text-sm rounded-md","bg-gray-100 text-gray-400 cursor-not-allowed"]),innerHTML:s.label},null,8,Vt))],64))),128))])])])):B("",!0)])])])]),_:1})],64))}};export{St as default};
