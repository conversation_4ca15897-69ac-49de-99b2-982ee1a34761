<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Services\AgentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class AgentController extends Controller
{
    public function __construct(
        private AgentService $agentService
    ) {
        // Middleware will be handled in routes
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 15);
        $search = $request->get('search');
        $status = $request->get('status');

        $agents = $this->agentService->getAllAgents($perPage);

        if ($search) {
            $agents = $this->agentService->searchAgents($search);
        }

        $statistics = $this->agentService->getStatistics();

        return Inertia::render('Agents/Index', [
            'agents' => $agents,
            'statistics' => $statistics,
            'filters' => [
                'search' => $search,
                'status' => $status,
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Agents/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $agent = $this->agentService->createAgent($request->all());

            return redirect()->route('agents.show', $agent->id)
                ->with('success', 'تم إنشاء الوكيل الملاحي بنجاح');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Agent $agent)
    {
        $agent = $this->agentService->findAgent($agent->id);

        if (!$agent) {
            return redirect()->route('agents.index')
                ->with('error', 'الوكيل الملاحي غير موجود');
        }

        return Inertia::render('Agents/Show', [
            'agent' => $agent,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Agent $agent)
    {
        return Inertia::render('Agents/Edit', [
            'agent' => $agent,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Agent $agent)
    {
        try {
            $this->agentService->updateAgent($agent->id, $request->all());

            return redirect()->route('agents.show', $agent->id)
                ->with('success', 'تم تحديث بيانات الوكيل الملاحي بنجاح');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Agent $agent)
    {
        try {
            $this->agentService->deleteAgent($agent->id);

            return redirect()->route('agents.index')
                ->with('success', 'تم حذف الوكيل الملاحي بنجاح');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Update agent status
     */
    public function updateStatus(Request $request, Agent $agent)
    {
        $request->validate([
            'status' => 'required|in:active,inactive,suspended'
        ]);

        try {
            $this->agentService->updateAgentStatus($agent->id, $request->status);

            return back()->with('success', 'تم تحديث حالة الوكيل الملاحي بنجاح');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Get agents for select dropdown
     */
    public function getForSelect()
    {
        $agents = $this->agentService->getAgentsForSelect();

        return response()->json($agents);
    }

    /**
     * Get agent dashboard data
     */
    public function dashboard(Agent $agent)
    {
        // Check if user can access this agent's dashboard
        /** @var \App\Models\User $user */
        $user = Auth::user();
        if (!$user->hasRole('admin') && optional($user->agent)->id !== $agent->id) {
            abort(403, 'غير مصرح لك بالوصول إلى هذه البيانات');
        }

        $dashboardData = $this->agentService->getAgentDashboard($agent->id);

        return Inertia::render('Agents/Dashboard', $dashboardData);
    }

    /**
     * Bulk operations
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,suspend',
            'agent_ids' => 'required|array',
            'agent_ids.*' => 'exists:agents,id'
        ]);

        try {
            $statusMap = [
                'activate' => 'active',
                'deactivate' => 'inactive',
                'suspend' => 'suspended'
            ];

            $count = $this->agentService->bulkUpdateStatus(
                $request->agent_ids,
                $statusMap[$request->action]
            );

            return back()->with('success', "تم تحديث حالة {$count} وكيل ملاحي بنجاح");
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Export agents data
     */
    public function export(Request $request)
    {
        $filters = $request->only(['status', 'license_expiry_from', 'license_expiry_to']);
        $report = $this->agentService->generateReport($filters);

        // Here you would implement the actual export logic
        // For now, return the data as JSON
        return response()->json($report);
    }
}
