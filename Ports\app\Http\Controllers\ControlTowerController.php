<?php

namespace App\Http\Controllers;

use App\Services\VoyageService;
use App\Services\BerthService;
use App\Services\WeatherService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ControlTowerController extends Controller
{
    public function __construct(
        private VoyageService $voyageService,
        private BerthService $berthService,
        private WeatherService $weatherService
    ) {}

    /**
     * Display the control tower dashboard
     */
    public function index(Request $request)
    {
        // Get active vessels (voyages in progress)
        $vessels = $this->voyageService->getActiveVoyagesForControlTower();
        
        // Get berth status
        $berths = $this->berthService->getAllBerths();
        
        // Get weather information
        $weather = $this->weatherService->getCurrentWeather();
        
        // Get tide information
        $tides = $this->weatherService->getTideInformation();

        return Inertia::render('ControlTower/Index', [
            'vessels' => $vessels,
            'berths' => $berths,
            'weather' => $weather,
            'tides' => $tides,
        ]);
    }

    /**
     * Get real-time vessel positions
     */
    public function getVesselPositions(Request $request)
    {
        $vessels = $this->voyageService->getActiveVoyagesWithPositions();
        
        return response()->json([
            'vessels' => $vessels,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Update vessel position
     */
    public function updateVesselPosition(Request $request, $voyageId)
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'heading' => 'nullable|numeric|between:0,360',
            'speed' => 'nullable|numeric|min:0',
        ]);

        $this->voyageService->updateVesselPosition($voyageId, [
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'heading' => $request->heading,
            'speed' => $request->speed,
            'updated_at' => now(),
        ]);

        return response()->json(['success' => true]);
    }

    /**
     * Get berth occupancy status
     */
    public function getBerthStatus(Request $request)
    {
        $berths = $this->berthService->getWithCurrentBookings();
        
        return response()->json([
            'berths' => $berths,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get weather updates
     */
    public function getWeatherUpdate(Request $request)
    {
        $weather = $this->weatherService->getCurrentWeather();
        $forecast = $this->weatherService->getWeatherForecast(24); // 24 hours
        
        return response()->json([
            'current' => $weather,
            'forecast' => $forecast,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get traffic alerts and notifications
     */
    public function getTrafficAlerts(Request $request)
    {
        $alerts = $this->voyageService->getTrafficAlerts();
        
        return response()->json([
            'alerts' => $alerts,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Create a new traffic alert
     */
    public function createAlert(Request $request)
    {
        $request->validate([
            'type' => 'required|in:weather,traffic,emergency,maintenance',
            'severity' => 'required|in:low,medium,high,critical',
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'area' => 'nullable|string',
            'expires_at' => 'nullable|date|after:now',
        ]);

        $alert = $this->voyageService->createTrafficAlert($request->all());

        // Broadcast alert to all connected clients
        // broadcast(new TrafficAlertCreated($alert));

        return response()->json([
            'alert' => $alert,
            'success' => true,
        ]);
    }

    /**
     * Get port statistics for control tower
     */
    public function getPortStatistics(Request $request)
    {
        $voyageStats = $this->voyageService->getStatistics();
        $berthStats = $this->berthService->getStatistics();
        
        return response()->json([
            'voyages' => $voyageStats,
            'berths' => $berthStats,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Get vessel tracking history
     */
    public function getVesselHistory(Request $request, $voyageId)
    {
        $history = $this->voyageService->getVesselTrackingHistory($voyageId);
        
        return response()->json([
            'history' => $history,
            'voyage_id' => $voyageId,
        ]);
    }

    /**
     * Export control tower data
     */
    public function exportData(Request $request)
    {
        $request->validate([
            'format' => 'required|in:pdf,excel,csv',
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
            'include' => 'array|in:vessels,berths,weather,alerts',
        ]);

        $data = [
            'vessels' => in_array('vessels', $request->include ?? []) 
                ? $this->voyageService->getVoyagesForPeriod($request->date_from, $request->date_to)
                : null,
            'berths' => in_array('berths', $request->include ?? [])
                ? $this->berthService->getBerthUtilizationForPeriod($request->date_from, $request->date_to)
                : null,
            'weather' => in_array('weather', $request->include ?? [])
                ? $this->weatherService->getWeatherHistoryForPeriod($request->date_from, $request->date_to)
                : null,
            'alerts' => in_array('alerts', $request->include ?? [])
                ? $this->voyageService->getAlertsForPeriod($request->date_from, $request->date_to)
                : null,
        ];

        // Generate export file based on format
        switch ($request->format) {
            case 'pdf':
                return $this->generatePdfReport($data, $request->date_from, $request->date_to);
            case 'excel':
                return $this->generateExcelReport($data, $request->date_from, $request->date_to);
            case 'csv':
                return $this->generateCsvReport($data, $request->date_from, $request->date_to);
        }
    }

    /**
     * Generate PDF report
     */
    private function generatePdfReport($data, $dateFrom, $dateTo)
    {
        // Implementation would use a PDF library like DomPDF or TCPDF
        return response()->json(['message' => 'PDF export not implemented yet']);
    }

    /**
     * Generate Excel report
     */
    private function generateExcelReport($data, $dateFrom, $dateTo)
    {
        // Implementation would use Laravel Excel package
        return response()->json(['message' => 'Excel export not implemented yet']);
    }

    /**
     * Generate CSV report
     */
    private function generateCsvReport($data, $dateFrom, $dateTo)
    {
        // Implementation would generate CSV file
        return response()->json(['message' => 'CSV export not implemented yet']);
    }
}
