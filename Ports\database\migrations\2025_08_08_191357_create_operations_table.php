<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('operations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('voyage_id')->constrained()->onDelete('cascade');
            $table->foreignId('berth_booking_id')->nullable()->constrained()->onDelete('set null');
            $table->string('operation_number')->unique();
            $table->enum('type', ['loading', 'unloading', 'bunkering', 'maintenance', 'inspection', 'cleaning', 'other']);
            $table->string('description');
            $table->datetime('planned_start');
            $table->datetime('planned_end');
            $table->datetime('actual_start')->nullable();
            $table->datetime('actual_end')->nullable();
            $table->enum('status', ['planned', 'in_progress', 'paused', 'completed', 'cancelled'])
                ->default('planned');
            $table->decimal('planned_quantity', 12, 2)->nullable();
            $table->decimal('actual_quantity', 12, 2)->nullable();
            $table->string('unit')->nullable(); // طن، حاوية، متر مكعب
            $table->json('equipment_used')->nullable(); // معدات مستخدمة
            $table->json('crew_assigned')->nullable(); // طاقم مكلف
            $table->decimal('cost', 10, 2)->nullable();
            $table->text('completion_notes')->nullable();
            $table->foreignId('supervisor_id')->nullable()->constrained('users')->onDelete('set null');
            $table->json('safety_checks')->nullable(); // فحوصات السلامة
            $table->json('quality_checks')->nullable(); // فحوصات الجودة
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['status', 'type']);
            $table->index(['planned_start', 'planned_end']);
            $table->index('operation_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('operations');
    }
};
