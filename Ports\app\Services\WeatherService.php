<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WeatherService
{
    private string $apiKey;
    private string $baseUrl;
    private array $portCoordinates;

    public function __construct()
    {
        $this->apiKey = config('services.weather.api_key', '');
        $this->baseUrl = config('services.weather.base_url', 'https://api.openweathermap.org/data/2.5');
        $this->portCoordinates = [
            'lat' => config('port.coordinates.latitude', 35.7308),
            'lon' => config('port.coordinates.longitude', 35.7925)
        ];
    }

    /**
     * Get current weather conditions
     */
    public function getCurrentWeather(): array
    {
        $cacheKey = 'weather_current';
        
        return Cache::remember($cacheKey, 600, function () { // Cache for 10 minutes
            try {
                if (empty($this->apiKey)) {
                    return $this->getMockWeatherData();
                }

                $response = Http::get("{$this->baseUrl}/weather", [
                    'lat' => $this->portCoordinates['lat'],
                    'lon' => $this->portCoordinates['lon'],
                    'appid' => $this->apiKey,
                    'units' => 'metric',
                    'lang' => 'ar'
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    return $this->formatWeatherData($data);
                }

                Log::warning('Weather API request failed', ['response' => $response->body()]);
                return $this->getMockWeatherData();

            } catch (\Exception $e) {
                Log::error('Weather service error', ['error' => $e->getMessage()]);
                return $this->getMockWeatherData();
            }
        });
    }

    /**
     * Get weather forecast
     */
    public function getWeatherForecast(int $hours = 24): array
    {
        $cacheKey = "weather_forecast_{$hours}h";
        
        return Cache::remember($cacheKey, 1800, function () use ($hours) { // Cache for 30 minutes
            try {
                if (empty($this->apiKey)) {
                    return $this->getMockForecastData($hours);
                }

                $response = Http::get("{$this->baseUrl}/forecast", [
                    'lat' => $this->portCoordinates['lat'],
                    'lon' => $this->portCoordinates['lon'],
                    'appid' => $this->apiKey,
                    'units' => 'metric',
                    'lang' => 'ar',
                    'cnt' => ceil($hours / 3) // API returns 3-hour intervals
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    return $this->formatForecastData($data);
                }

                Log::warning('Weather forecast API request failed', ['response' => $response->body()]);
                return $this->getMockForecastData($hours);

            } catch (\Exception $e) {
                Log::error('Weather forecast service error', ['error' => $e->getMessage()]);
                return $this->getMockForecastData($hours);
            }
        });
    }

    /**
     * Get tide information
     */
    public function getTideInformation(): array
    {
        $cacheKey = 'tide_information';
        
        return Cache::remember($cacheKey, 3600, function () { // Cache for 1 hour
            // In a real implementation, this would call a tide API
            // For now, we'll return mock data
            return $this->getMockTideData();
        });
    }

    /**
     * Get weather history for a period
     */
    public function getWeatherHistoryForPeriod(string $dateFrom, string $dateTo): array
    {
        // In a real implementation, this would query historical weather data
        // For now, return mock data
        return [
            'period' => ['from' => $dateFrom, 'to' => $dateTo],
            'average_temperature' => 25.5,
            'max_temperature' => 32.1,
            'min_temperature' => 18.3,
            'average_wind_speed' => 12.5,
            'max_wind_speed' => 28.7,
            'precipitation_days' => 3,
            'clear_days' => 12,
        ];
    }

    /**
     * Check if weather conditions are suitable for port operations
     */
    public function isWeatherSuitableForOperations(): array
    {
        $weather = $this->getCurrentWeather();
        
        $windSpeedLimit = 25; // knots
        $visibilityLimit = 1000; // meters
        $waveHeightLimit = 2; // meters

        $suitable = true;
        $warnings = [];

        if ($weather['wind_speed'] > $windSpeedLimit) {
            $suitable = false;
            $warnings[] = 'سرعة الرياح عالية جداً';
        }

        if ($weather['visibility'] < $visibilityLimit) {
            $suitable = false;
            $warnings[] = 'الرؤية ضعيفة';
        }

        if ($weather['wave_height'] > $waveHeightLimit) {
            $suitable = false;
            $warnings[] = 'ارتفاع الأمواج خطير';
        }

        return [
            'suitable' => $suitable,
            'warnings' => $warnings,
            'conditions' => $weather,
            'limits' => [
                'wind_speed' => $windSpeedLimit,
                'visibility' => $visibilityLimit,
                'wave_height' => $waveHeightLimit,
            ]
        ];
    }

    /**
     * Format weather data from API response
     */
    private function formatWeatherData(array $data): array
    {
        return [
            'temperature' => round($data['main']['temp'], 1),
            'feels_like' => round($data['main']['feels_like'], 1),
            'humidity' => $data['main']['humidity'],
            'pressure' => $data['main']['pressure'],
            'wind_speed' => round($data['wind']['speed'] * 1.944, 1), // Convert m/s to knots
            'wind_direction' => $data['wind']['deg'] ?? 0,
            'visibility' => ($data['visibility'] ?? 10000) / 1000, // Convert to km
            'weather_condition' => $data['weather'][0]['description'] ?? 'غير محدد',
            'weather_icon' => $data['weather'][0]['icon'] ?? '01d',
            'clouds' => $data['clouds']['all'] ?? 0,
            'sunrise' => date('H:i', $data['sys']['sunrise']),
            'sunset' => date('H:i', $data['sys']['sunset']),
            'wave_height' => rand(5, 20) / 10, // Mock data - would come from marine API
            'sea_state' => $this->getSeaStateDescription(rand(5, 20) / 10),
            'updated_at' => now()->toISOString(),
        ];
    }

    /**
     * Format forecast data from API response
     */
    private function formatForecastData(array $data): array
    {
        $forecast = [];
        
        foreach ($data['list'] as $item) {
            $forecast[] = [
                'datetime' => $item['dt_txt'],
                'temperature' => round($item['main']['temp'], 1),
                'wind_speed' => round($item['wind']['speed'] * 1.944, 1),
                'wind_direction' => $item['wind']['deg'] ?? 0,
                'weather_condition' => $item['weather'][0]['description'],
                'weather_icon' => $item['weather'][0]['icon'],
                'precipitation' => $item['rain']['3h'] ?? 0,
            ];
        }

        return $forecast;
    }

    /**
     * Get mock weather data for development/fallback
     */
    private function getMockWeatherData(): array
    {
        return [
            'temperature' => 25.5 + (rand(-50, 50) / 10),
            'feels_like' => 27.2 + (rand(-50, 50) / 10),
            'humidity' => 65 + rand(-20, 20),
            'pressure' => 1013 + rand(-10, 10),
            'wind_speed' => 12.5 + (rand(-50, 50) / 10),
            'wind_direction' => rand(0, 360),
            'visibility' => 10 + (rand(-30, 20) / 10),
            'weather_condition' => 'صافي جزئياً',
            'weather_icon' => '02d',
            'clouds' => rand(10, 40),
            'sunrise' => '06:15',
            'sunset' => '19:30',
            'wave_height' => rand(5, 15) / 10,
            'sea_state' => $this->getSeaStateDescription(rand(5, 15) / 10),
            'updated_at' => now()->toISOString(),
        ];
    }

    /**
     * Get mock forecast data
     */
    private function getMockForecastData(int $hours): array
    {
        $forecast = [];
        $baseTemp = 25;
        
        for ($i = 0; $i < ceil($hours / 3); $i++) {
            $datetime = now()->addHours($i * 3);
            $tempVariation = sin($i * 0.5) * 5; // Simulate daily temperature cycle
            
            $forecast[] = [
                'datetime' => $datetime->format('Y-m-d H:i:s'),
                'temperature' => round($baseTemp + $tempVariation + (rand(-20, 20) / 10), 1),
                'wind_speed' => 10 + (rand(-30, 30) / 10),
                'wind_direction' => rand(0, 360),
                'weather_condition' => ['صافي', 'غائم جزئياً', 'غائم', 'ممطر'][rand(0, 3)],
                'weather_icon' => ['01d', '02d', '03d', '10d'][rand(0, 3)],
                'precipitation' => rand(0, 5),
            ];
        }

        return $forecast;
    }

    /**
     * Get mock tide data
     */
    private function getMockTideData(): array
    {
        $now = now();
        
        return [
            'current_level' => 1.2 + (sin($now->hour * 0.26) * 0.8), // Simulate tidal cycle
            'next_high_tide' => [
                'time' => $now->addHours(6)->format('H:i'),
                'level' => 1.8,
            ],
            'next_low_tide' => [
                'time' => $now->addHours(12)->format('H:i'),
                'level' => 0.4,
            ],
            'tidal_range' => 1.4,
            'updated_at' => now()->toISOString(),
        ];
    }

    /**
     * Get sea state description based on wave height
     */
    private function getSeaStateDescription(float $waveHeight): string
    {
        if ($waveHeight < 0.5) return 'هادئ';
        if ($waveHeight < 1.0) return 'هادئ نسبياً';
        if ($waveHeight < 2.0) return 'متوسط';
        if ($waveHeight < 3.0) return 'مضطرب';
        return 'عاصف';
    }
}
