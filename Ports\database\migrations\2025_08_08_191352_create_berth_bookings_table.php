<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('berth_bookings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('berth_id')->constrained()->onDelete('cascade');
            $table->foreignId('voyage_id')->constrained()->onDelete('cascade');
            $table->foreignId('arrival_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('booking_number')->unique();
            $table->datetime('start_time');
            $table->datetime('end_time');
            $table->datetime('actual_start_time')->nullable();
            $table->datetime('actual_end_time')->nullable();
            $table->enum('status', ['requested', 'confirmed', 'active', 'completed', 'cancelled'])
                ->default('requested');
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
            $table->text('purpose');
            $table->json('required_services')->nullable(); // خدمات مطلوبة
            $table->json('equipment_needed')->nullable(); // معدات مطلوبة
            $table->decimal('estimated_cost', 10, 2)->nullable();
            $table->decimal('actual_cost', 10, 2)->nullable();
            $table->text('special_instructions')->nullable();
            $table->text('cancellation_reason')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->datetime('approved_at')->nullable();
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['berth_id', 'start_time', 'end_time']);
            $table->index(['status', 'priority']);
            $table->index('booking_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('berth_bookings');
    }
};
