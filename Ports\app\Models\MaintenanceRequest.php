<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class MaintenanceRequest extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'request_number',
        'type',
        'category',
        'asset_name',
        'asset_code',
        'location',
        'description',
        'priority',
        'status',
        'requested_by',
        'requested_at',
        'required_by',
        'assigned_to',
        'assigned_at',
        'started_at',
        'completed_at',
        'estimated_cost',
        'actual_cost',
        'estimated_hours',
        'actual_hours',
        'required_parts',
        'used_parts',
        'required_tools',
        'work_performed',
        'completion_notes',
        'requires_shutdown',
        'shutdown_start',
        'shutdown_end',
        'safety_measures',
        'quality_checks',
        'approved_by',
        'approved_at',
        'notes',
        'attachments',
        'metadata',
    ];

    protected $casts = [
        'requested_at' => 'datetime',
        'required_by' => 'datetime',
        'assigned_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'approved_at' => 'datetime',
        'shutdown_start' => 'datetime',
        'shutdown_end' => 'datetime',
        'estimated_cost' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'estimated_hours' => 'decimal:2',
        'actual_hours' => 'decimal:2',
        'requires_shutdown' => 'boolean',
        'required_parts' => 'array',
        'used_parts' => 'array',
        'required_tools' => 'array',
        'safety_measures' => 'array',
        'quality_checks' => 'array',
        'attachments' => 'array',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'requested',
        'priority' => 'normal',
        'requires_shutdown' => false,
    ];

    // العلاقات
    public function requestedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    // النطاقات
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeActive($query)
    {
        return $query->whereIn('status', ['approved', 'assigned', 'in_progress']);
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', '!=', 'completed')
            ->whereNotNull('required_by')
            ->where('required_by', '<', now());
    }

    public function scopeUrgent($query)
    {
        return $query->whereIn('priority', ['high', 'critical']);
    }

    public function scopeRequiringShutdown($query)
    {
        return $query->where('requires_shutdown', true);
    }

    // الخصائص المحسوبة
    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'completed';
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->status !== 'completed' &&
               $this->required_by &&
               $this->required_by < now();
    }

    public function getIsCriticalAttribute(): bool
    {
        return $this->priority === 'critical';
    }

    public function getIsUrgentAttribute(): bool
    {
        return in_array($this->priority, ['high', 'critical']);
    }

    public function getDaysOverdueAttribute(): int
    {
        if ($this->is_overdue) {
            return $this->required_by->diffInDays(now());
        }
        return 0;
    }

    public function getDaysUntilDueAttribute(): int
    {
        if (!$this->is_overdue && $this->required_by) {
            return now()->diffInDays($this->required_by);
        }
        return 0;
    }

    public function getActualDurationAttribute(): ?float
    {
        if ($this->started_at && $this->completed_at) {
            return $this->started_at->diffInHours($this->completed_at, true);
        }
        return null;
    }

    public function getDurationVarianceAttribute(): ?float
    {
        $actualDuration = $this->actual_duration;
        if ($actualDuration && $this->estimated_hours) {
            return $actualDuration - $this->estimated_hours;
        }
        return null;
    }

    public function getCostVarianceAttribute(): ?float
    {
        if ($this->actual_cost && $this->estimated_cost) {
            return $this->actual_cost - $this->estimated_cost;
        }
        return null;
    }

    public function getShutdownDurationAttribute(): ?float
    {
        if ($this->shutdown_start && $this->shutdown_end) {
            return $this->shutdown_start->diffInHours($this->shutdown_end, true);
        }
        return null;
    }

    public function getHasAttachmentsAttribute(): bool
    {
        return !empty($this->attachments);
    }

    public function getProgressPercentageAttribute(): float
    {
        return match($this->status) {
            'requested' => 0,
            'approved' => 25,
            'assigned' => 50,
            'in_progress' => 75,
            'completed' => 100,
            'cancelled' => 0,
            default => 0
        };
    }

    // دوال مساعدة
    public function canBeApproved(): bool
    {
        return $this->status === 'requested';
    }

    public function canBeAssigned(): bool
    {
        return $this->status === 'approved';
    }

    public function canBeStarted(): bool
    {
        return $this->status === 'assigned';
    }

    public function canBeCompleted(): bool
    {
        return $this->status === 'in_progress';
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['requested', 'approved', 'assigned']);
    }

    public function approve(?User $approver = null): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        return $this->update([
            'status' => 'approved',
            'approved_by' => $approver?->id,
            'approved_at' => now(),
        ]);
    }

    public function assignTo(User $technician): bool
    {
        if (!$this->canBeAssigned()) {
            return false;
        }

        return $this->update([
            'status' => 'assigned',
            'assigned_to' => $technician->id,
            'assigned_at' => now(),
        ]);
    }

    public function start(): bool
    {
        if (!$this->canBeStarted()) {
            return false;
        }

        return $this->update([
            'status' => 'in_progress',
            'started_at' => now(),
        ]);
    }

    public function complete(array $completionData = []): bool
    {
        if (!$this->canBeCompleted()) {
            return false;
        }

        $data = array_merge([
            'status' => 'completed',
            'completed_at' => now(),
        ], $completionData);

        return $this->update($data);
    }

    public function cancel(?string $reason = null): bool
    {
        if (!$this->canBeCancelled()) {
            return false;
        }

        return $this->update([
            'status' => 'cancelled',
            'notes' => $reason ? $this->notes . "\nCancellation reason: " . $reason : $this->notes,
        ]);
    }

    public static function generateRequestNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');

        $lastRequest = self::whereYear('created_at', $year)
            ->whereMonth('created_at', now()->month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastRequest ?
            (int) substr($lastRequest->request_number, -4) + 1 :
            1;

        return sprintf('MR-%s%s-%04d', $year, $month, $sequence);
    }

    public function getPriorityColorAttribute(): string
    {
        return match($this->priority) {
            'critical' => 'red',
            'high' => 'orange',
            'normal' => 'blue',
            'low' => 'green',
            default => 'gray'
        };
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'requested' => 'yellow',
            'approved' => 'blue',
            'assigned' => 'purple',
            'in_progress' => 'orange',
            'completed' => 'green',
            'cancelled' => 'red',
            default => 'gray'
        };
    }
}
