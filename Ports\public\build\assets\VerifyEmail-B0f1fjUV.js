import{u as c,y as g,s as p,o as n,w as o,a as i,d as a,c as y,g as _,b as t,h as v,e as b,n as h,l as r,q as k}from"./app-CdA4UbU6.js";import{_ as x}from"./GuestLayout-CuRaoidN.js";import{P as w}from"./PrimaryButton-BzUTxj76.js";import"./ApplicationLogo-WdDnJ3OK.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const V={key:0,class:"mb-4 text-sm font-medium text-green-600"},B={class:"mt-4 flex items-center justify-between"},L={__name:"VerifyEmail",props:{status:{type:String}},setup(d){const l=d,s=c({}),u=()=>{s.post(route("verification.send"))},m=g(()=>l.status==="verification-link-sent");return(f,e)=>(n(),p(x,null,{default:o(()=>[i(t(v),{title:"Email Verification"}),e[2]||(e[2]=a("div",{class:"mb-4 text-sm text-gray-600"}," Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another. ",-1)),m.value?(n(),y("div",V," A new verification link has been sent to the email address you provided during registration. ")):_("",!0),a("form",{onSubmit:b(u,["prevent"])},[a("div",B,[i(w,{class:h({"opacity-25":t(s).processing}),disabled:t(s).processing},{default:o(()=>e[0]||(e[0]=[r(" Resend Verification Email ",-1)])),_:1,__:[0]},8,["class","disabled"]),i(t(k),{href:f.route("logout"),method:"post",as:"button",class:"rounded-md text-sm text-gray-600 underline hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"},{default:o(()=>e[1]||(e[1]=[r("Log Out",-1)])),_:1,__:[1]},8,["href"])])],32)]),_:1,__:[2]}))}};export{L as default};
