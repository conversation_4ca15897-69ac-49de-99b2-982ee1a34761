<?php

namespace App\Services;

use App\Models\Ship;
use App\Repositories\ShipRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class ShipService
{
    public function __construct(
        private ShipRepository $shipRepository
    ) {}

    /**
     * Get all ships with pagination
     */
    public function getAllShips(int $perPage = 15): LengthAwarePaginator
    {
        return $this->shipRepository
            ->with(['voyages'])
            ->latest()
            ->paginate($perPage);
    }

    /**
     * Get active ships
     */
    public function getActiveShips(): Collection
    {
        return $this->shipRepository->getActive();
    }

    /**
     * Find ship by ID
     */
    public function findShip(int $id): ?Ship
    {
        return $this->shipRepository
            ->with(['voyages.agent', 'documents'])
            ->find($id);
    }

    /**
     * Find ship by IMO number
     */
    public function findByImo(string $imoNumber): ?Ship
    {
        return $this->shipRepository->findByImo($imoNumber);
    }

    /**
     * Create new ship
     */
    public function createShip(array $data): Ship
    {
        $this->validateShipData($data);
        return $this->shipRepository->create($data);
    }

    /**
     * Update ship
     */
    public function updateShip(int $id, array $data): bool
    {
        $this->validateShipData($data, $id);
        return $this->shipRepository->update($id, $data);
    }

    /**
     * Delete ship
     */
    public function deleteShip(int $id): bool
    {
        $ship = $this->shipRepository->findOrFail($id);
        
        // Check if ship has active voyages
        if ($ship->voyages()->whereIn('status', ['planned', 'arrived', 'berthed', 'working'])->exists()) {
            throw new \Exception('لا يمكن حذف السفينة لوجود رحلات نشطة');
        }

        return $this->shipRepository->delete($id);
    }

    /**
     * Update ship status
     */
    public function updateShipStatus(int $id, string $status): bool
    {
        $validStatuses = ['active', 'inactive', 'blacklisted'];
        
        if (!in_array($status, $validStatuses)) {
            throw new \InvalidArgumentException('حالة غير صحيحة');
        }

        return $this->shipRepository->updateStatus($id, $status);
    }

    /**
     * Search ships
     */
    public function searchShips(string $term): Collection
    {
        return $this->shipRepository->searchByName($term);
    }

    /**
     * Get ships statistics
     */
    public function getStatistics(): array
    {
        return $this->shipRepository->getStatistics();
    }

    /**
     * Get ships by type
     */
    public function getShipsByType(string $type): Collection
    {
        return $this->shipRepository->getByType($type);
    }

    /**
     * Get ships by flag
     */
    public function getShipsByFlag(string $country): Collection
    {
        return $this->shipRepository->getByFlag($country);
    }

    /**
     * Get ships for dropdown
     */
    public function getShipsForSelect(): Collection
    {
        return $this->shipRepository->getForSelect();
    }

    /**
     * Get ships currently in port
     */
    public function getCurrentlyInPort(): Collection
    {
        return $this->shipRepository->getCurrentlyInPort();
    }

    /**
     * Get ships expected today
     */
    public function getExpectedToday(): Collection
    {
        return $this->shipRepository->getExpectedToday();
    }

    /**
     * Get ships departing today
     */
    public function getDepartingToday(): Collection
    {
        return $this->shipRepository->getDepartingToday();
    }

    /**
     * Get ships compatible with berth
     */
    public function getCompatibleWithBerth(int $berthId): Collection
    {
        return $this->shipRepository->getCompatibleWithBerth($berthId);
    }

    /**
     * Get ship performance metrics
     */
    public function getPerformanceMetrics(int $shipId): array
    {
        return $this->shipRepository->getPerformanceMetrics($shipId);
    }

    /**
     * Get ships requiring inspection
     */
    public function getRequiringInspection(): Collection
    {
        return $this->shipRepository->getRequiringInspection();
    }

    /**
     * Validate ship data
     */
    private function validateShipData(array $data, ?int $shipId = null): void
    {
        $rules = [
            'name' => 'required|string|max:255',
            'imo_number' => 'required|string|unique:ships,imo_number' . ($shipId ? ",$shipId" : ''),
            'call_sign' => 'required|string|unique:ships,call_sign' . ($shipId ? ",$shipId" : ''),
            'flag_country' => 'required|string|max:100',
            'ship_type' => 'required|in:container,bulk,general,liquid,passenger,ro_ro',
            'length' => 'required|numeric|min:0',
            'width' => 'required|numeric|min:0',
            'draft' => 'required|numeric|min:0',
            'gross_tonnage' => 'required|numeric|min:0',
            'net_tonnage' => 'required|numeric|min:0',
            'deadweight' => 'required|numeric|min:0',
            'year_built' => 'required|integer|min:1900|max:' . date('Y'),
            'owner_name' => 'required|string|max:255',
            'status' => 'in:active,inactive,blacklisted',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    /**
     * Bulk operations
     */
    public function bulkUpdateStatus(array $shipIds, string $status): int
    {
        $validStatuses = ['active', 'inactive', 'blacklisted'];
        
        if (!in_array($status, $validStatuses)) {
            throw new \InvalidArgumentException('حالة غير صحيحة');
        }

        return $this->shipRepository->bulkUpdateStatus($shipIds, $status);
    }

    /**
     * Generate ship report
     */
    public function generateReport(array $filters = []): array
    {
        $query = $this->shipRepository->query;

        // Apply filters
        if (isset($filters['ship_type'])) {
            $query->where('ship_type', $filters['ship_type']);
        }

        if (isset($filters['flag_country'])) {
            $query->where('flag_country', $filters['flag_country']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['min_tonnage'])) {
            $query->where('gross_tonnage', '>=', $filters['min_tonnage']);
        }

        if (isset($filters['max_tonnage'])) {
            $query->where('gross_tonnage', '<=', $filters['max_tonnage']);
        }

        if (isset($filters['min_year'])) {
            $query->where('year_built', '>=', $filters['min_year']);
        }

        if (isset($filters['max_year'])) {
            $query->where('year_built', '<=', $filters['max_year']);
        }

        $ships = $query->with(['voyages'])->get();

        return [
            'ships' => $ships,
            'total_count' => $ships->count(),
            'active_count' => $ships->where('status', 'active')->count(),
            'inactive_count' => $ships->where('status', 'inactive')->count(),
            'blacklisted_count' => $ships->where('status', 'blacklisted')->count(),
            'average_age' => $ships->avg('age'),
            'average_tonnage' => $ships->avg('gross_tonnage'),
            'total_voyages' => $ships->sum(fn($ship) => $ship->voyages->count()),
        ];
    }

    /**
     * Get ship dashboard data
     */
    public function getShipDashboard(): array
    {
        $totalShips = $this->shipRepository->count();
        $activeShips = $this->shipRepository->scope('active')->count();
        $currentlyInPort = $this->shipRepository->getCurrentlyInPort()->count();
        $expectedToday = $this->shipRepository->getExpectedToday()->count();
        $departingToday = $this->shipRepository->getDepartingToday()->count();

        $recentArrivals = $this->shipRepository->query
            ->whereHas('voyages', function ($query) {
                $query->whereHas('arrival', function ($q) {
                    $q->whereDate('actual_arrival', '>=', now()->subDays(7));
                });
            })
            ->with(['voyages.arrival', 'voyages.agent'])
            ->latest()
            ->limit(10)
            ->get();

        return [
            'total_ships' => $totalShips,
            'active_ships' => $activeShips,
            'currently_in_port' => $currentlyInPort,
            'expected_today' => $expectedToday,
            'departing_today' => $departingToday,
            'recent_arrivals' => $recentArrivals,
        ];
    }

    /**
     * Import ships from CSV/Excel
     */
    public function importShips(array $shipsData): array
    {
        $imported = 0;
        $errors = [];

        foreach ($shipsData as $index => $shipData) {
            try {
                $this->createShip($shipData);
                $imported++;
            } catch (\Exception $e) {
                $errors[] = [
                    'row' => $index + 1,
                    'error' => $e->getMessage(),
                    'data' => $shipData
                ];
            }
        }

        return [
            'imported' => $imported,
            'errors' => $errors,
            'total' => count($shipsData)
        ];
    }
}
