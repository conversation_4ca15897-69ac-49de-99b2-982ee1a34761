<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pilot_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pilot_id')->constrained()->onDelete('cascade');
            $table->foreignId('voyage_id')->constrained()->onDelete('cascade');
            $table->foreignId('arrival_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('departure_id')->nullable()->constrained()->onDelete('set null');
            $table->string('assignment_number')->unique();
            $table->enum('type', ['arrival', 'departure', 'shifting', 'emergency']);
            $table->datetime('scheduled_time');
            $table->datetime('boarding_time')->nullable();
            $table->datetime('disembark_time')->nullable();
            $table->string('boarding_location')->nullable();
            $table->string('disembark_location')->nullable();
            $table->enum('status', ['assigned', 'confirmed', 'boarding', 'piloting', 'completed', 'cancelled'])
                ->default('assigned');
            $table->enum('priority', ['normal', 'high', 'urgent', 'emergency'])->default('normal');
            $table->boolean('night_operation')->default(false);
            $table->boolean('requires_tugboat')->default(false);
            $table->json('weather_conditions')->nullable();
            $table->json('special_requirements')->nullable();
            $table->text('route_plan')->nullable();
            $table->decimal('estimated_duration', 5, 2)->nullable(); // ساعات
            $table->decimal('actual_duration', 5, 2)->nullable();
            $table->decimal('pilot_fee', 8, 2)->nullable();
            $table->text('completion_report')->nullable();
            $table->json('incidents')->nullable(); // حوادث أو مشاكل
            $table->foreignId('assigned_by')->constrained('users')->onDelete('cascade');
            $table->datetime('assigned_at');
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['pilot_id', 'scheduled_time']);
            $table->index(['status', 'type']);
            $table->index('assignment_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pilot_assignments');
    }
};
