<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class InspectionItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'inspection_id',
        'item_code',
        'category',
        'description',
        'requirement',
        'result',
        'findings',
        'deficiency_description',
        'severity',
        'corrective_action',
        'correction_deadline',
        'corrected',
        'corrected_at',
        'correction_evidence',
        'photos',
        'documents',
        'inspector_notes',
        'metadata',
    ];

    protected $casts = [
        'correction_deadline' => 'datetime',
        'corrected' => 'boolean',
        'corrected_at' => 'datetime',
        'photos' => 'array',
        'documents' => 'array',
        'metadata' => 'array',
    ];

    protected $attributes = [
        'corrected' => false,
    ];

    // العلاقات
    public function inspection(): BelongsTo
    {
        return $this->belongsTo(Inspection::class);
    }

    // النطاقات
    public function scopeByResult($query, $result)
    {
        return $query->where('result', $result);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeBySeverity($query, $severity)
    {
        return $query->where('severity', $severity);
    }

    public function scopeNonCompliant($query)
    {
        return $query->where('result', 'non_compliant');
    }

    public function scopeCompliant($query)
    {
        return $query->where('result', 'compliant');
    }

    public function scopeCorrected($query)
    {
        return $query->where('corrected', true);
    }

    public function scopeUncorrected($query)
    {
        return $query->where('corrected', false)
            ->where('result', 'non_compliant');
    }

    public function scopeOverdue($query)
    {
        return $query->where('corrected', false)
            ->where('result', 'non_compliant')
            ->whereNotNull('correction_deadline')
            ->where('correction_deadline', '<', now());
    }

    // الخصائص المحسوبة
    public function getIsCompliantAttribute(): bool
    {
        return $this->result === 'compliant';
    }

    public function getIsNonCompliantAttribute(): bool
    {
        return $this->result === 'non_compliant';
    }

    public function getIsCorrectedAttribute(): bool
    {
        return $this->corrected;
    }

    public function getIsOverdueAttribute(): bool
    {
        return !$this->corrected &&
               $this->result === 'non_compliant' &&
               $this->correction_deadline &&
               $this->correction_deadline < now();
    }

    public function getIsCriticalAttribute(): bool
    {
        return $this->severity === 'critical';
    }

    public function getIsMajorAttribute(): bool
    {
        return $this->severity === 'major';
    }

    public function getIsMinorAttribute(): bool
    {
        return $this->severity === 'minor';
    }

    public function getDaysUntilDeadlineAttribute(): ?int
    {
        if ($this->correction_deadline) {
            return now()->diffInDays($this->correction_deadline, false);
        }
        return null;
    }

    public function getHasPhotosAttribute(): bool
    {
        return !empty($this->photos);
    }

    public function getHasDocumentsAttribute(): bool
    {
        return !empty($this->documents);
    }

    public function getCorrectionTimeAttribute(): ?int
    {
        if ($this->corrected && $this->corrected_at && $this->correction_deadline) {
            return $this->correction_deadline->diffInDays($this->corrected_at, false);
        }
        return null;
    }

    // دوال مساعدة
    public function markAsCorrected(?string $evidence = null): bool
    {
        return $this->update([
            'corrected' => true,
            'corrected_at' => now(),
            'correction_evidence' => $evidence,
        ]);
    }

    public function requiresCorrection(): bool
    {
        return $this->result === 'non_compliant' && !$this->corrected;
    }

    public function canBeMarkedAsCorrected(): bool
    {
        return $this->result === 'non_compliant' && !$this->corrected;
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->result) {
            'compliant' => 'green',
            'non_compliant' => $this->corrected ? 'blue' : 'red',
            'observation' => 'yellow',
            'not_applicable' => 'gray',
            default => 'gray'
        };
    }

    public function getSeverityColorAttribute(): string
    {
        return match($this->severity) {
            'critical' => 'red',
            'major' => 'orange',
            'minor' => 'yellow',
            default => 'gray'
        };
    }
}
