<?php

namespace App\Repositories;

use App\Contracts\RepositoryInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;

abstract class BaseRepository implements RepositoryInterface
{
    protected Model $model;
    protected Builder $query;

    public function __construct()
    {
        $this->model = $this->getModel();
        $this->resetQuery();
    }

    /**
     * Get the model instance
     */
    abstract protected function getModel(): Model;

    /**
     * Reset query builder
     */
    protected function resetQuery(): void
    {
        $this->query = $this->model->newQuery();
    }

    public function all(): Collection
    {
        $result = $this->query->get();
        $this->resetQuery();
        return $result;
    }

    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        $result = $this->query->paginate($perPage);
        $this->resetQuery();
        return $result;
    }

    public function find(int $id): ?Model
    {
        $result = $this->query->find($id);
        $this->resetQuery();
        return $result;
    }

    public function findOrFail(int $id): Model
    {
        $result = $this->query->findOrFail($id);
        $this->resetQuery();
        return $result;
    }

    public function findBy(string $field, $value): ?Model
    {
        $result = $this->query->where($field, $value)->first();
        $this->resetQuery();
        return $result;
    }

    public function findAllBy(string $field, $value): Collection
    {
        $result = $this->query->where($field, $value)->get();
        $this->resetQuery();
        return $result;
    }

    public function create(array $data): Model
    {
        return $this->model->create($data);
    }

    public function update(int $id, array $data): bool
    {
        $model = $this->findOrFail($id);
        return $model->update($data);
    }

    public function delete(int $id): bool
    {
        $model = $this->findOrFail($id);
        return $model->delete();
    }

    public function with(array $relations): self
    {
        $this->query->with($relations);
        return $this;
    }

    public function where(string $field, $operator, $value = null): self
    {
        if ($value === null) {
            $this->query->where($field, $operator);
        } else {
            $this->query->where($field, $operator, $value);
        }
        return $this;
    }

    public function whereIn(string $field, array $values): self
    {
        $this->query->whereIn($field, $values);
        return $this;
    }

    public function orderBy(string $field, string $direction = 'asc'): self
    {
        $this->query->orderBy($field, $direction);
        return $this;
    }

    public function count(): int
    {
        $result = $this->query->count();
        $this->resetQuery();
        return $result;
    }

    public function exists(): bool
    {
        $result = $this->query->exists();
        $this->resetQuery();
        return $result;
    }

    public function first(): ?Model
    {
        $result = $this->query->first();
        $this->resetQuery();
        return $result;
    }

    public function latest(string $field = 'created_at'): self
    {
        $this->query->latest($field);
        return $this;
    }

    public function scope(string $scope, ...$parameters): self
    {
        $this->query->{$scope}(...$parameters);
        return $this;
    }

    /**
     * Apply multiple where conditions
     */
    public function whereMultiple(array $conditions): self
    {
        foreach ($conditions as $condition) {
            if (count($condition) === 2) {
                $this->query->where($condition[0], $condition[1]);
            } elseif (count($condition) === 3) {
                $this->query->where($condition[0], $condition[1], $condition[2]);
            }
        }
        return $this;
    }

    /**
     * Apply date range filter
     */
    public function whereDateBetween(string $field, $startDate, $endDate): self
    {
        $this->query->whereBetween($field, [$startDate, $endDate]);
        return $this;
    }

    /**
     * Search in multiple fields
     */
    public function search(string $term, array $fields): self
    {
        $this->query->where(function ($query) use ($term, $fields) {
            foreach ($fields as $field) {
                $query->orWhere($field, 'LIKE', "%{$term}%");
            }
        });
        return $this;
    }

    /**
     * Get fresh model instance
     */
    public function fresh(): Model
    {
        return $this->model->newInstance();
    }

    /**
     * Begin database transaction
     */
    public function beginTransaction(): void
    {
        $this->model->getConnection()->beginTransaction();
    }

    /**
     * Commit database transaction
     */
    public function commit(): void
    {
        $this->model->getConnection()->commit();
    }

    /**
     * Rollback database transaction
     */
    public function rollback(): void
    {
        $this->model->getConnection()->rollback();
    }

    /**
     * Execute callback within transaction
     */
    public function transaction(callable $callback)
    {
        return $this->model->getConnection()->transaction($callback);
    }
}
