import { ref, unref, withCtx, createTextVNode, createVNode, createBlock, openBlock, toDisplayString, withDirectives, withKeys, vModelText, vModelSelect, createCommentVNode, Fragment, renderList, useSSRContext } from "vue";
import { ssrRenderComponent, ssrInterpolate, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual, ssrRenderList, ssrRenderClass } from "vue/server-renderer";
import { _ as _sfc_main$1 } from "./AuthenticatedLayout-DMuuhipg.js";
import { Head, Link, router } from "@inertiajs/vue3";
import "./ApplicationLogo-B2173abF.js";
import "./_plugin-vue_export-helper-1tPrXgE0.js";
const _sfc_main = {
  __name: "Index",
  __ssrInlineRender: true,
  props: {
    agents: Object,
    statistics: Object,
    filters: Object
  },
  setup(__props) {
    const props = __props;
    const search = ref(props.filters.search || "");
    const status = ref(props.filters.status || "");
    const performSearch = () => {
      router.get(route("agents.index"), {
        search: search.value,
        status: status.value
      }, {
        preserveState: true,
        replace: true
      });
    };
    const getStatusColor = (status2) => {
      const colors = {
        "active": "bg-green-100 text-green-800",
        "inactive": "bg-gray-100 text-gray-800",
        "suspended": "bg-red-100 text-red-800"
      };
      return colors[status2] || "bg-gray-100 text-gray-800";
    };
    const getStatusText = (status2) => {
      const texts = {
        "active": "نشط",
        "inactive": "غير نشط",
        "suspended": "معلق"
      };
      return texts[status2] || status2;
    };
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<!--[-->`);
      _push(ssrRenderComponent(unref(Head), { title: "الوكلاء الملاحيين" }, null, _parent));
      _push(ssrRenderComponent(_sfc_main$1, null, {
        header: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="flex justify-between items-center"${_scopeId}><h2 class="font-semibold text-xl text-gray-800 leading-tight"${_scopeId}> الوكلاء الملاحيين </h2>`);
            _push2(ssrRenderComponent(unref(Link), {
              href: _ctx.route("agents.create"),
              class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(` إضافة وكيل جديد `);
                } else {
                  return [
                    createTextVNode(" إضافة وكيل جديد ")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`</div>`);
          } else {
            return [
              createVNode("div", { class: "flex justify-between items-center" }, [
                createVNode("h2", { class: "font-semibold text-xl text-gray-800 leading-tight" }, " الوكلاء الملاحيين "),
                createVNode(unref(Link), {
                  href: _ctx.route("agents.create"),
                  class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                }, {
                  default: withCtx(() => [
                    createTextVNode(" إضافة وكيل جديد ")
                  ]),
                  _: 1
                }, 8, ["href"])
              ])
            ];
          }
        }),
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="py-12"${_scopeId}><div class="max-w-7xl mx-auto sm:px-6 lg:px-8"${_scopeId}><div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"${_scopeId}><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"${_scopeId}><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"${_scopeId}></path></svg></div></div><div class="ml-4"${_scopeId}><p class="text-sm font-medium text-gray-500"${_scopeId}>إجمالي الوكلاء</p><p class="text-2xl font-semibold text-gray-900"${_scopeId}>${ssrInterpolate(__props.statistics.total)}</p></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"${_scopeId}><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"${_scopeId}></path></svg></div></div><div class="ml-4"${_scopeId}><p class="text-sm font-medium text-gray-500"${_scopeId}>نشط</p><p class="text-2xl font-semibold text-gray-900"${_scopeId}>${ssrInterpolate(__props.statistics.active)}</p></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center"${_scopeId}><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"${_scopeId}></path></svg></div></div><div class="ml-4"${_scopeId}><p class="text-sm font-medium text-gray-500"${_scopeId}>تنتهي قريباً</p><p class="text-2xl font-semibold text-gray-900"${_scopeId}>${ssrInterpolate(__props.statistics.expiring_soon)}</p></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center"${_scopeId}><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"${_scopeId}></path><path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"${_scopeId}></path></svg></div></div><div class="ml-4"${_scopeId}><p class="text-sm font-medium text-gray-500"${_scopeId}>معدل النشاط</p><p class="text-2xl font-semibold text-gray-900"${_scopeId}>${ssrInterpolate(__props.statistics.active_percentage)}%</p></div></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"${_scopeId}><div class="p-6"${_scopeId}><div class="grid grid-cols-1 md:grid-cols-3 gap-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}>البحث</label><input${ssrRenderAttr("value", search.value)} type="text" placeholder="البحث بالاسم أو البريد الإلكتروني..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"${_scopeId}></div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}>الحالة</label><select class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"${_scopeId}><option value=""${ssrIncludeBooleanAttr(Array.isArray(status.value) ? ssrLooseContain(status.value, "") : ssrLooseEqual(status.value, "")) ? " selected" : ""}${_scopeId}>جميع الحالات</option><option value="active"${ssrIncludeBooleanAttr(Array.isArray(status.value) ? ssrLooseContain(status.value, "active") : ssrLooseEqual(status.value, "active")) ? " selected" : ""}${_scopeId}>نشط</option><option value="inactive"${ssrIncludeBooleanAttr(Array.isArray(status.value) ? ssrLooseContain(status.value, "inactive") : ssrLooseEqual(status.value, "inactive")) ? " selected" : ""}${_scopeId}>غير نشط</option><option value="suspended"${ssrIncludeBooleanAttr(Array.isArray(status.value) ? ssrLooseContain(status.value, "suspended") : ssrLooseEqual(status.value, "suspended")) ? " selected" : ""}${_scopeId}>معلق</option></select></div><div class="flex items-end"${_scopeId}><button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full"${_scopeId}> بحث </button></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="overflow-x-auto"${_scopeId}><table class="min-w-full divide-y divide-gray-200"${_scopeId}><thead class="bg-gray-50"${_scopeId}><tr${_scopeId}><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> اسم الشركة </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> رقم الترخيص </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> الشخص المسؤول </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> البريد الإلكتروني </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> الحالة </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> انتهاء الترخيص </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> الإجراءات </th></tr></thead><tbody class="bg-white divide-y divide-gray-200"${_scopeId}><!--[-->`);
            ssrRenderList(__props.agents.data, (agent) => {
              _push2(`<tr class="hover:bg-gray-50"${_scopeId}><td class="px-6 py-4 whitespace-nowrap"${_scopeId}><div class="text-sm font-medium text-gray-900"${_scopeId}>${ssrInterpolate(agent.company_name)}</div><div class="text-sm text-gray-500"${_scopeId}>${ssrInterpolate(agent.company_name_en)}</div></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"${_scopeId}>${ssrInterpolate(agent.license_number)}</td><td class="px-6 py-4 whitespace-nowrap"${_scopeId}><div class="text-sm text-gray-900"${_scopeId}>${ssrInterpolate(agent.contact_person)}</div><div class="text-sm text-gray-500"${_scopeId}>${ssrInterpolate(agent.phone)}</div></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"${_scopeId}>${ssrInterpolate(agent.email)}</td><td class="px-6 py-4 whitespace-nowrap"${_scopeId}><span class="${ssrRenderClass([getStatusColor(agent.status), "inline-flex px-2 py-1 text-xs font-semibold rounded-full"])}"${_scopeId}>${ssrInterpolate(getStatusText(agent.status))}</span></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"${_scopeId}>${ssrInterpolate(new Date(agent.license_expiry).toLocaleDateString("ar-SA"))}</td><td class="px-6 py-4 whitespace-nowrap text-sm font-medium"${_scopeId}><div class="flex space-x-2"${_scopeId}>`);
              _push2(ssrRenderComponent(unref(Link), {
                href: _ctx.route("agents.show", agent.id),
                class: "text-indigo-600 hover:text-indigo-900"
              }, {
                default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(` عرض `);
                  } else {
                    return [
                      createTextVNode(" عرض ")
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
              _push2(ssrRenderComponent(unref(Link), {
                href: _ctx.route("agents.edit", agent.id),
                class: "text-green-600 hover:text-green-900"
              }, {
                default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(` تعديل `);
                  } else {
                    return [
                      createTextVNode(" تعديل ")
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
              _push2(`</div></td></tr>`);
            });
            _push2(`<!--]--></tbody></table></div>`);
            if (__props.agents.links) {
              _push2(`<div class="px-6 py-3 border-t border-gray-200"${_scopeId}><div class="flex justify-between items-center"${_scopeId}><div class="text-sm text-gray-700"${_scopeId}> عرض ${ssrInterpolate(__props.agents.from)} إلى ${ssrInterpolate(__props.agents.to)} من ${ssrInterpolate(__props.agents.total)} نتيجة </div><div class="flex space-x-1"${_scopeId}><!--[-->`);
              ssrRenderList(__props.agents.links, (link) => {
                _push2(`<!--[-->`);
                if (link.url) {
                  _push2(ssrRenderComponent(unref(Link), {
                    href: link.url,
                    class: [
                      "px-3 py-2 text-sm rounded-md",
                      link.active ? "bg-blue-500 text-white" : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-300"
                    ]
                  }, null, _parent2, _scopeId));
                } else {
                  _push2(`<span class="${ssrRenderClass([
                    "px-3 py-2 text-sm rounded-md",
                    "bg-gray-100 text-gray-400 cursor-not-allowed"
                  ])}"${_scopeId}>${link.label ?? ""}</span>`);
                }
                _push2(`<!--]-->`);
              });
              _push2(`<!--]--></div></div></div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div></div>`);
          } else {
            return [
              createVNode("div", { class: "py-12" }, [
                createVNode("div", { class: "max-w-7xl mx-auto sm:px-6 lg:px-8" }, [
                  createVNode("div", { class: "grid grid-cols-1 md:grid-cols-4 gap-6 mb-8" }, [
                    createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                      createVNode("div", { class: "p-6" }, [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", { class: "flex-shrink-0" }, [
                            createVNode("div", { class: "w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center" }, [
                              (openBlock(), createBlock("svg", {
                                class: "w-4 h-4 text-white",
                                fill: "currentColor",
                                viewBox: "0 0 20 20"
                              }, [
                                createVNode("path", { d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" })
                              ]))
                            ])
                          ]),
                          createVNode("div", { class: "ml-4" }, [
                            createVNode("p", { class: "text-sm font-medium text-gray-500" }, "إجمالي الوكلاء"),
                            createVNode("p", { class: "text-2xl font-semibold text-gray-900" }, toDisplayString(__props.statistics.total), 1)
                          ])
                        ])
                      ])
                    ]),
                    createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                      createVNode("div", { class: "p-6" }, [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", { class: "flex-shrink-0" }, [
                            createVNode("div", { class: "w-8 h-8 bg-green-500 rounded-full flex items-center justify-center" }, [
                              (openBlock(), createBlock("svg", {
                                class: "w-4 h-4 text-white",
                                fill: "currentColor",
                                viewBox: "0 0 20 20"
                              }, [
                                createVNode("path", {
                                  "fill-rule": "evenodd",
                                  d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
                                  "clip-rule": "evenodd"
                                })
                              ]))
                            ])
                          ]),
                          createVNode("div", { class: "ml-4" }, [
                            createVNode("p", { class: "text-sm font-medium text-gray-500" }, "نشط"),
                            createVNode("p", { class: "text-2xl font-semibold text-gray-900" }, toDisplayString(__props.statistics.active), 1)
                          ])
                        ])
                      ])
                    ]),
                    createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                      createVNode("div", { class: "p-6" }, [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", { class: "flex-shrink-0" }, [
                            createVNode("div", { class: "w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center" }, [
                              (openBlock(), createBlock("svg", {
                                class: "w-4 h-4 text-white",
                                fill: "currentColor",
                                viewBox: "0 0 20 20"
                              }, [
                                createVNode("path", {
                                  "fill-rule": "evenodd",
                                  d: "M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",
                                  "clip-rule": "evenodd"
                                })
                              ]))
                            ])
                          ]),
                          createVNode("div", { class: "ml-4" }, [
                            createVNode("p", { class: "text-sm font-medium text-gray-500" }, "تنتهي قريباً"),
                            createVNode("p", { class: "text-2xl font-semibold text-gray-900" }, toDisplayString(__props.statistics.expiring_soon), 1)
                          ])
                        ])
                      ])
                    ]),
                    createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                      createVNode("div", { class: "p-6" }, [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", { class: "flex-shrink-0" }, [
                            createVNode("div", { class: "w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center" }, [
                              (openBlock(), createBlock("svg", {
                                class: "w-4 h-4 text-white",
                                fill: "currentColor",
                                viewBox: "0 0 20 20"
                              }, [
                                createVNode("path", { d: "M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z" }),
                                createVNode("path", { d: "M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z" })
                              ]))
                            ])
                          ]),
                          createVNode("div", { class: "ml-4" }, [
                            createVNode("p", { class: "text-sm font-medium text-gray-500" }, "معدل النشاط"),
                            createVNode("p", { class: "text-2xl font-semibold text-gray-900" }, toDisplayString(__props.statistics.active_percentage) + "%", 1)
                          ])
                        ])
                      ])
                    ])
                  ]),
                  createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6" }, [
                    createVNode("div", { class: "p-6" }, [
                      createVNode("div", { class: "grid grid-cols-1 md:grid-cols-3 gap-4" }, [
                        createVNode("div", null, [
                          createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, "البحث"),
                          withDirectives(createVNode("input", {
                            "onUpdate:modelValue": ($event) => search.value = $event,
                            onKeyup: withKeys(performSearch, ["enter"]),
                            type: "text",
                            placeholder: "البحث بالاسم أو البريد الإلكتروني...",
                            class: "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                          }, null, 40, ["onUpdate:modelValue"]), [
                            [vModelText, search.value]
                          ])
                        ]),
                        createVNode("div", null, [
                          createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, "الحالة"),
                          withDirectives(createVNode("select", {
                            "onUpdate:modelValue": ($event) => status.value = $event,
                            onChange: performSearch,
                            class: "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                          }, [
                            createVNode("option", { value: "" }, "جميع الحالات"),
                            createVNode("option", { value: "active" }, "نشط"),
                            createVNode("option", { value: "inactive" }, "غير نشط"),
                            createVNode("option", { value: "suspended" }, "معلق")
                          ], 40, ["onUpdate:modelValue"]), [
                            [vModelSelect, status.value]
                          ])
                        ]),
                        createVNode("div", { class: "flex items-end" }, [
                          createVNode("button", {
                            onClick: performSearch,
                            class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full"
                          }, " بحث ")
                        ])
                      ])
                    ])
                  ]),
                  createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                    createVNode("div", { class: "overflow-x-auto" }, [
                      createVNode("table", { class: "min-w-full divide-y divide-gray-200" }, [
                        createVNode("thead", { class: "bg-gray-50" }, [
                          createVNode("tr", null, [
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " اسم الشركة "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " رقم الترخيص "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " الشخص المسؤول "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " البريد الإلكتروني "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " الحالة "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " انتهاء الترخيص "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " الإجراءات ")
                          ])
                        ]),
                        createVNode("tbody", { class: "bg-white divide-y divide-gray-200" }, [
                          (openBlock(true), createBlock(Fragment, null, renderList(__props.agents.data, (agent) => {
                            return openBlock(), createBlock("tr", {
                              key: agent.id,
                              class: "hover:bg-gray-50"
                            }, [
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap" }, [
                                createVNode("div", { class: "text-sm font-medium text-gray-900" }, toDisplayString(agent.company_name), 1),
                                createVNode("div", { class: "text-sm text-gray-500" }, toDisplayString(agent.company_name_en), 1)
                              ]),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap text-sm text-gray-900" }, toDisplayString(agent.license_number), 1),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap" }, [
                                createVNode("div", { class: "text-sm text-gray-900" }, toDisplayString(agent.contact_person), 1),
                                createVNode("div", { class: "text-sm text-gray-500" }, toDisplayString(agent.phone), 1)
                              ]),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap text-sm text-gray-900" }, toDisplayString(agent.email), 1),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap" }, [
                                createVNode("span", {
                                  class: [getStatusColor(agent.status), "inline-flex px-2 py-1 text-xs font-semibold rounded-full"]
                                }, toDisplayString(getStatusText(agent.status)), 3)
                              ]),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap text-sm text-gray-900" }, toDisplayString(new Date(agent.license_expiry).toLocaleDateString("ar-SA")), 1),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap text-sm font-medium" }, [
                                createVNode("div", { class: "flex space-x-2" }, [
                                  createVNode(unref(Link), {
                                    href: _ctx.route("agents.show", agent.id),
                                    class: "text-indigo-600 hover:text-indigo-900"
                                  }, {
                                    default: withCtx(() => [
                                      createTextVNode(" عرض ")
                                    ]),
                                    _: 2
                                  }, 1032, ["href"]),
                                  createVNode(unref(Link), {
                                    href: _ctx.route("agents.edit", agent.id),
                                    class: "text-green-600 hover:text-green-900"
                                  }, {
                                    default: withCtx(() => [
                                      createTextVNode(" تعديل ")
                                    ]),
                                    _: 2
                                  }, 1032, ["href"])
                                ])
                              ])
                            ]);
                          }), 128))
                        ])
                      ])
                    ]),
                    __props.agents.links ? (openBlock(), createBlock("div", {
                      key: 0,
                      class: "px-6 py-3 border-t border-gray-200"
                    }, [
                      createVNode("div", { class: "flex justify-between items-center" }, [
                        createVNode("div", { class: "text-sm text-gray-700" }, " عرض " + toDisplayString(__props.agents.from) + " إلى " + toDisplayString(__props.agents.to) + " من " + toDisplayString(__props.agents.total) + " نتيجة ", 1),
                        createVNode("div", { class: "flex space-x-1" }, [
                          (openBlock(true), createBlock(Fragment, null, renderList(__props.agents.links, (link) => {
                            return openBlock(), createBlock(Fragment, {
                              key: link.label
                            }, [
                              link.url ? (openBlock(), createBlock(unref(Link), {
                                key: 0,
                                href: link.url,
                                class: [
                                  "px-3 py-2 text-sm rounded-md",
                                  link.active ? "bg-blue-500 text-white" : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-300"
                                ],
                                innerHTML: link.label
                              }, null, 8, ["href", "class", "innerHTML"])) : (openBlock(), createBlock("span", {
                                key: 1,
                                class: [
                                  "px-3 py-2 text-sm rounded-md",
                                  "bg-gray-100 text-gray-400 cursor-not-allowed"
                                ],
                                innerHTML: link.label
                              }, null, 8, ["innerHTML"]))
                            ], 64);
                          }), 128))
                        ])
                      ])
                    ])) : createCommentVNode("", true)
                  ])
                ])
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<!--]-->`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("resources/js/Pages/Agents/Index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
export {
  _sfc_main as default
};
