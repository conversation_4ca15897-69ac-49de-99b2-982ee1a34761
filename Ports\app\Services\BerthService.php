<?php

namespace App\Services;

use App\Models\Berth;
use App\Repositories\BerthRepository;
use Illuminate\Support\Collection;

class BerthService
{
    public function __construct(
        private BerthRepository $berthRepository
    ) {}

    /**
     * Get all berths with pagination
     */
    public function getAllBerths(int $perPage = 15)
    {
        return $this->berthRepository->paginate($perPage);
    }

    /**
     * Get available berths for selection
     */
    public function getAvailableBerths(): Collection
    {
        return $this->berthRepository->newQuery()
            ->where('status', 'available')
            ->orderBy('name')
            ->get(['id', 'name', 'type', 'max_length', 'max_width', 'max_draft', 'status']);
    }

    /**
     * Get berths for select dropdown
     */
    public function getBerthsForSelect(): Collection
    {
        return $this->berthRepository->newQuery()
            ->orderBy('name')
            ->get(['id', 'name', 'type', 'status']);
    }

    /**
     * Find a specific berth
     */
    public function findBerth(int $id): ?Berth
    {
        return $this->berthRepository->find($id);
    }

    /**
     * Create a new berth
     */
    public function createBerth(array $data): Berth
    {
        return $this->berthRepository->create($data);
    }

    /**
     * Update a berth
     */
    public function updateBerth(int $id, array $data): bool
    {
        return $this->berthRepository->update($id, $data);
    }

    /**
     * Delete a berth
     */
    public function deleteBerth(int $id): bool
    {
        return $this->berthRepository->delete($id);
    }

    /**
     * Get berth statistics
     */
    public function getStatistics(): array
    {
        $total = $this->berthRepository->count();
        $available = $this->berthRepository->newQuery()->where('status', 'available')->count();
        $occupied = $this->berthRepository->newQuery()->where('status', 'occupied')->count();
        $maintenance = $this->berthRepository->newQuery()->where('status', 'maintenance')->count();

        return [
            'total' => $total,
            'available' => $available,
            'occupied' => $occupied,
            'maintenance' => $maintenance,
            'occupancy_rate' => $total > 0 ? round(($occupied / $total) * 100, 2) : 0,
        ];
    }

    /**
     * Check if berth is suitable for ship
     */
    public function isSuitableForShip(int $berthId, array $shipDimensions): bool
    {
        $berth = $this->findBerth($berthId);
        
        if (!$berth) {
            return false;
        }

        return $berth->max_length >= $shipDimensions['length'] &&
               $berth->max_width >= $shipDimensions['width'] &&
               $berth->max_draft >= $shipDimensions['draft'];
    }

    /**
     * Get suitable berths for a ship
     */
    public function getSuitableBerths(array $shipDimensions): Collection
    {
        return $this->berthRepository->newQuery()
            ->where('status', 'available')
            ->where('max_length', '>=', $shipDimensions['length'])
            ->where('max_width', '>=', $shipDimensions['width'])
            ->where('max_draft', '>=', $shipDimensions['draft'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Update berth status
     */
    public function updateStatus(int $id, string $status): bool
    {
        return $this->berthRepository->update($id, ['status' => $status]);
    }

    /**
     * Get berth occupancy schedule
     */
    public function getOccupancySchedule(int $berthId, ?\DateTime $startDate = null, ?\DateTime $endDate = null): Collection
    {
        $startDate = $startDate ?: now();
        $endDate = $endDate ?: now()->addDays(30);

        return $this->berthRepository->newQuery()
            ->with(['berthBookings.voyage.ship'])
            ->where('id', $berthId)
            ->first()
            ->berthBookings()
            ->whereBetween('start_time', [$startDate, $endDate])
            ->orWhereBetween('end_time', [$startDate, $endDate])
            ->orderBy('start_time')
            ->get();
    }

    /**
     * Get berth utilization metrics
     */
    public function getUtilizationMetrics(int $berthId, ?\DateTime $startDate = null, ?\DateTime $endDate = null): array
    {
        $startDate = $startDate ?: now()->subDays(30);
        $endDate = $endDate ?: now();

        $berth = $this->findBerth($berthId);
        if (!$berth) {
            return [];
        }

        $totalHours = $startDate->diff($endDate)->h + ($startDate->diff($endDate)->days * 24);
        
        $occupiedHours = $berth->berthBookings()
            ->whereBetween('start_time', [$startDate, $endDate])
            ->get()
            ->sum(function ($booking) use ($startDate, $endDate) {
                $bookingStart = max($booking->start_time, $startDate);
                $bookingEnd = min($booking->end_time, $endDate);
                return $bookingStart->diffInHours($bookingEnd);
            });

        $utilizationRate = $totalHours > 0 ? round(($occupiedHours / $totalHours) * 100, 2) : 0;

        return [
            'total_hours' => $totalHours,
            'occupied_hours' => $occupiedHours,
            'utilization_rate' => $utilizationRate,
            'available_hours' => $totalHours - $occupiedHours,
        ];
    }

    /**
     * Get berths with current bookings
     */
    public function getWithCurrentBookings()
    {
        return $this->berthRepository->getWithCurrentBookings();
    }

    /**
     * Get berth utilization for period
     */
    public function getBerthUtilizationForPeriod(string $dateFrom, string $dateTo): array
    {
        $berths = $this->berthRepository->getUtilizationData(
            new \DateTime($dateFrom),
            new \DateTime($dateTo)
        );

        $utilization = [];
        foreach ($berths as $berth) {
            $totalHours = (new \DateTime($dateFrom))->diff(new \DateTime($dateTo))->h;
            $occupiedHours = $berth->berthBookings->sum(function ($booking) use ($dateFrom, $dateTo) {
                $start = max($booking->start_time, new \DateTime($dateFrom));
                $end = min($booking->end_time, new \DateTime($dateTo));
                return $start->diff($end)->h;
            });

            $utilization[] = [
                'berth' => $berth,
                'total_hours' => $totalHours,
                'occupied_hours' => $occupiedHours,
                'utilization_rate' => $totalHours > 0 ? ($occupiedHours / $totalHours) * 100 : 0,
            ];
        }

        return $utilization;
    }
}
