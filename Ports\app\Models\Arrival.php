<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Arrival extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'voyage_id',
        'berth_id',
        'arrival_number',
        'reported_eta',
        'confirmed_eta',
        'actual_arrival',
        'pilot_boarding_time',
        'berthing_time',
        'status',
        'arrival_reason',
        'requires_pilot',
        'requires_tugboat',
        'requires_quarantine',
        'has_dangerous_cargo',
        'has_waste',
        'special_requirements',
        'port_clearances',
        'documents_submitted',
        'services_requested',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'reported_eta' => 'datetime',
        'confirmed_eta' => 'datetime',
        'actual_arrival' => 'datetime',
        'pilot_boarding_time' => 'datetime',
        'berthing_time' => 'datetime',
        'requires_pilot' => 'boolean',
        'requires_tugboat' => 'boolean',
        'requires_quarantine' => 'boolean',
        'has_dangerous_cargo' => 'boolean',
        'has_waste' => 'boolean',
        'port_clearances' => 'array',
        'documents_submitted' => 'array',
        'services_requested' => 'array',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'reported',
        'requires_pilot' => true,
        'requires_tugboat' => false,
        'requires_quarantine' => false,
        'has_dangerous_cargo' => false,
        'has_waste' => false,
    ];

    // العلاقات
    public function voyage(): BelongsTo
    {
        return $this->belongsTo(Voyage::class);
    }

    public function berth(): BelongsTo
    {
        return $this->belongsTo(Berth::class);
    }

    public function departure(): HasOne
    {
        return $this->hasOne(Departure::class);
    }

    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    // النطاقات
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopePending($query)
    {
        return $query->whereIn('status', ['reported', 'confirmed', 'pilot_assigned', 'approaching']);
    }

    public function scopeArrivedToday($query)
    {
        return $query->whereDate('actual_arrival', today());
    }

    public function scopeExpectedToday($query)
    {
        return $query->whereDate('confirmed_eta', today())
            ->whereIn('status', ['confirmed', 'pilot_assigned', 'approaching']);
    }

    // الخصائص المحسوبة
    public function getIsArrivedAttribute(): bool
    {
        return in_array($this->status, ['arrived', 'berthed']);
    }

    public function getIsBerthedAttribute(): bool
    {
        return $this->status === 'berthed';
    }

    public function getDelayInHoursAttribute(): ?int
    {
        if ($this->actual_arrival && $this->confirmed_eta) {
            return $this->confirmed_eta->diffInHours($this->actual_arrival, false);
        }
        return null;
    }

    public function getIsDelayedAttribute(): bool
    {
        $delay = $this->delay_in_hours;
        return $delay !== null && $delay > 0;
    }

    public function getRequiresSpecialHandlingAttribute(): bool
    {
        return $this->has_dangerous_cargo ||
               $this->requires_quarantine ||
               !empty($this->special_requirements);
    }

    public function getClearanceStatusAttribute(): array
    {
        $clearances = $this->port_clearances ?? [];
        $status = [];

        $requiredClearances = ['customs', 'health', 'security', 'immigration'];

        foreach ($requiredClearances as $clearance) {
            $status[$clearance] = $clearances[$clearance] ?? 'pending';
        }

        return $status;
    }

    public function getAllClearancesObtainedAttribute(): bool
    {
        $clearanceStatus = $this->clearance_status;
        return !in_array('pending', $clearanceStatus) && !in_array('rejected', $clearanceStatus);
    }
}
