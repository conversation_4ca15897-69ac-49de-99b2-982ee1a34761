import{u as w,r as k,c as d,o as n,a as p,b as s,h as V,w as b,d as t,e as U,f as i,g as a,n as m,v as u,t as l,F as c,i as h,j as q,k as C,l as N}from"./app-CdA4UbU6.js";import{_ as S}from"./AuthenticatedLayout-CBV1ADW9.js";import"./ApplicationLogo-WdDnJ3OK.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const M={class:"py-12"},B={class:"max-w-4xl mx-auto sm:px-6 lg:px-8"},F={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},L={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},j={key:0,class:"text-red-500 text-sm mt-1"},D={key:0,class:"text-red-500 text-sm mt-1"},T={key:0,class:"text-red-500 text-sm mt-1"},z={key:0,class:"text-red-500 text-sm mt-1"},E={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},$={key:0,class:"text-red-500 text-sm mt-1"},A={key:0,class:"text-red-500 text-sm mt-1"},G={key:0,class:"text-red-500 text-sm mt-1"},H={key:0,class:"text-red-500 text-sm mt-1"},I={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},J={key:0,class:"text-red-500 text-sm mt-1"},K={key:0,class:"text-red-500 text-sm mt-1"},O={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},P=["id","value"],Q=["for"],R={key:0,class:"text-red-500 text-sm mt-1"},W={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},X={key:0,class:"text-red-500 text-sm mt-1"},Y={key:0,class:"text-red-500 text-sm mt-1"},Z={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200"},ee=["disabled"],se={key:0},re={key:1},ie={__name:"Create",setup(te){const e=w({company_name:"",company_name_en:"",license_number:"",email:"",phone:"",address:"",address_en:"",contact_person:"",contact_person_en:"",license_expiry:"",services:[],notes:"",status:"active"}),x=["ship_agency","cargo_handling","customs_clearance","crew_services","supply_services","waste_management","bunker_services","repair_services"],y=g=>({ship_agency:"وكالة ملاحية",cargo_handling:"مناولة البضائع",customs_clearance:"تخليص جمركي",crew_services:"خدمات الطاقم",supply_services:"خدمات التموين",waste_management:"إدارة النفايات",bunker_services:"خدمات الوقود",repair_services:"خدمات الإصلاح"})[g]||g,f=()=>{e.post(route("agents.store"))};return(g,r)=>{const _=k("Link");return n(),d(c,null,[p(s(V),{title:"إضافة وكيل ملاحي جديد"}),p(S,null,{header:b(()=>r[13]||(r[13]=[t("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"}," إضافة وكيل ملاحي جديد ",-1)])),default:b(()=>[t("div",M,[t("div",B,[t("div",F,[t("form",{onSubmit:U(f,["prevent"]),class:"p-6 space-y-6"},[t("div",null,[r[18]||(r[18]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"معلومات الشركة",-1)),t("div",L,[t("div",null,[r[14]||(r[14]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," اسم الشركة (عربي) * ",-1)),i(t("input",{"onUpdate:modelValue":r[0]||(r[0]=o=>s(e).company_name=o),type:"text",required:"",class:m(["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",{"border-red-500":s(e).errors.company_name}])},null,2),[[u,s(e).company_name]]),s(e).errors.company_name?(n(),d("div",j,l(s(e).errors.company_name),1)):a("",!0)]),t("div",null,[r[15]||(r[15]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," اسم الشركة (إنجليزي) ",-1)),i(t("input",{"onUpdate:modelValue":r[1]||(r[1]=o=>s(e).company_name_en=o),type:"text",class:m(["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",{"border-red-500":s(e).errors.company_name_en}])},null,2),[[u,s(e).company_name_en]]),s(e).errors.company_name_en?(n(),d("div",D,l(s(e).errors.company_name_en),1)):a("",!0)]),t("div",null,[r[16]||(r[16]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," رقم الترخيص * ",-1)),i(t("input",{"onUpdate:modelValue":r[2]||(r[2]=o=>s(e).license_number=o),type:"text",required:"",class:m(["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",{"border-red-500":s(e).errors.license_number}])},null,2),[[u,s(e).license_number]]),s(e).errors.license_number?(n(),d("div",T,l(s(e).errors.license_number),1)):a("",!0)]),t("div",null,[r[17]||(r[17]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," تاريخ انتهاء الترخيص * ",-1)),i(t("input",{"onUpdate:modelValue":r[3]||(r[3]=o=>s(e).license_expiry=o),type:"date",required:"",class:m(["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",{"border-red-500":s(e).errors.license_expiry}])},null,2),[[u,s(e).license_expiry]]),s(e).errors.license_expiry?(n(),d("div",z,l(s(e).errors.license_expiry),1)):a("",!0)])])]),t("div",null,[r[23]||(r[23]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"معلومات الاتصال",-1)),t("div",E,[t("div",null,[r[19]||(r[19]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," الشخص المسؤول (عربي) * ",-1)),i(t("input",{"onUpdate:modelValue":r[4]||(r[4]=o=>s(e).contact_person=o),type:"text",required:"",class:m(["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",{"border-red-500":s(e).errors.contact_person}])},null,2),[[u,s(e).contact_person]]),s(e).errors.contact_person?(n(),d("div",$,l(s(e).errors.contact_person),1)):a("",!0)]),t("div",null,[r[20]||(r[20]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," الشخص المسؤول (إنجليزي) ",-1)),i(t("input",{"onUpdate:modelValue":r[5]||(r[5]=o=>s(e).contact_person_en=o),type:"text",class:m(["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",{"border-red-500":s(e).errors.contact_person_en}])},null,2),[[u,s(e).contact_person_en]]),s(e).errors.contact_person_en?(n(),d("div",A,l(s(e).errors.contact_person_en),1)):a("",!0)]),t("div",null,[r[21]||(r[21]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," البريد الإلكتروني * ",-1)),i(t("input",{"onUpdate:modelValue":r[6]||(r[6]=o=>s(e).email=o),type:"email",required:"",class:m(["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",{"border-red-500":s(e).errors.email}])},null,2),[[u,s(e).email]]),s(e).errors.email?(n(),d("div",G,l(s(e).errors.email),1)):a("",!0)]),t("div",null,[r[22]||(r[22]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," رقم الهاتف * ",-1)),i(t("input",{"onUpdate:modelValue":r[7]||(r[7]=o=>s(e).phone=o),type:"tel",required:"",class:m(["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",{"border-red-500":s(e).errors.phone}])},null,2),[[u,s(e).phone]]),s(e).errors.phone?(n(),d("div",H,l(s(e).errors.phone),1)):a("",!0)])])]),t("div",null,[r[26]||(r[26]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"معلومات العنوان",-1)),t("div",I,[t("div",null,[r[24]||(r[24]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," العنوان (عربي) * ",-1)),i(t("textarea",{"onUpdate:modelValue":r[8]||(r[8]=o=>s(e).address=o),rows:"3",required:"",class:m(["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",{"border-red-500":s(e).errors.address}])},null,2),[[u,s(e).address]]),s(e).errors.address?(n(),d("div",J,l(s(e).errors.address),1)):a("",!0)]),t("div",null,[r[25]||(r[25]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," العنوان (إنجليزي) ",-1)),i(t("textarea",{"onUpdate:modelValue":r[9]||(r[9]=o=>s(e).address_en=o),rows:"3",class:m(["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",{"border-red-500":s(e).errors.address_en}])},null,2),[[u,s(e).address_en]]),s(e).errors.address_en?(n(),d("div",K,l(s(e).errors.address_en),1)):a("",!0)])])]),t("div",null,[r[27]||(r[27]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"الخدمات المقدمة",-1)),t("div",O,[(n(),d(c,null,h(x,o=>t("div",{key:o,class:"flex items-center"},[i(t("input",{id:o,"onUpdate:modelValue":r[10]||(r[10]=v=>s(e).services=v),value:o,type:"checkbox",class:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"},null,8,P),[[q,s(e).services]]),t("label",{for:o,class:"mr-2 text-sm text-gray-700"},l(y(o)),9,Q)])),64))]),s(e).errors.services?(n(),d("div",R,l(s(e).errors.services),1)):a("",!0)]),t("div",W,[t("div",null,[r[29]||(r[29]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," الحالة ",-1)),i(t("select",{"onUpdate:modelValue":r[11]||(r[11]=o=>s(e).status=o),class:m(["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",{"border-red-500":s(e).errors.status}])},r[28]||(r[28]=[t("option",{value:"active"},"نشط",-1),t("option",{value:"inactive"},"غير نشط",-1),t("option",{value:"suspended"},"معلق",-1)]),2),[[C,s(e).status]]),s(e).errors.status?(n(),d("div",X,l(s(e).errors.status),1)):a("",!0)]),t("div",null,[r[30]||(r[30]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," ملاحظات ",-1)),i(t("textarea",{"onUpdate:modelValue":r[12]||(r[12]=o=>s(e).notes=o),rows:"3",class:m(["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500",{"border-red-500":s(e).errors.notes}])},null,2),[[u,s(e).notes]]),s(e).errors.notes?(n(),d("div",Y,l(s(e).errors.notes),1)):a("",!0)])]),t("div",Z,[p(_,{href:g.route("agents.index"),class:"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"},{default:b(()=>r[31]||(r[31]=[N(" إلغاء ",-1)])),_:1,__:[31]},8,["href"]),t("button",{type:"submit",disabled:s(e).processing,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"},[s(e).processing?(n(),d("span",se,"جاري الحفظ...")):(n(),d("span",re,"حفظ"))],8,ee)])],32)])])])]),_:1})],64)}}};export{ie as default};
