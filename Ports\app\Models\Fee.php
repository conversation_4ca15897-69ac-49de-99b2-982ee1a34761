<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Fee extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'code',
        'name',
        'name_en',
        'description',
        'category',
        'calculation_method',
        'base_rate',
        'minimum_charge',
        'maximum_charge',
        'currency',
        'rate_tiers',
        'applicable_ship_types',
        'applicable_cargo_types',
        'is_active',
        'effective_from',
        'effective_to',
        'terms_conditions',
        'exemptions',
        'discounts',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'base_rate' => 'decimal:4',
        'minimum_charge' => 'decimal:2',
        'maximum_charge' => 'decimal:2',
        'rate_tiers' => 'array',
        'applicable_ship_types' => 'array',
        'applicable_cargo_types' => 'array',
        'is_active' => 'boolean',
        'effective_from' => 'date',
        'effective_to' => 'date',
        'exemptions' => 'array',
        'discounts' => 'array',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'is_active' => true,
        'currency' => 'SYP',
    ];

    // النطاقات
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where('effective_from', '<=', now())
            ->where(function ($q) {
                $q->whereNull('effective_to')
                  ->orWhere('effective_to', '>=', now());
            });
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeApplicableToShip($query, $shipType)
    {
        return $query->where(function ($q) use ($shipType) {
            $q->whereNull('applicable_ship_types')
              ->orWhereJsonContains('applicable_ship_types', $shipType);
        });
    }

    public function scopeApplicableToCargo($query, $cargoType)
    {
        return $query->where(function ($q) use ($cargoType) {
            $q->whereNull('applicable_cargo_types')
              ->orWhereJsonContains('applicable_cargo_types', $cargoType);
        });
    }

    // الخصائص المحسوبة
    public function getIsActiveNowAttribute(): bool
    {
        return $this->is_active &&
               $this->effective_from <= now() &&
               ($this->effective_to === null || $this->effective_to >= now());
    }

    public function getFullNameAttribute(): string
    {
        return $this->name_en ?
            $this->name . ' / ' . $this->name_en :
            $this->name;
    }

    public function getIsExpiredAttribute(): bool
    {
        return $this->effective_to && $this->effective_to < now();
    }

    public function getIsExpiringSoonAttribute(): bool
    {
        return $this->effective_to &&
               $this->effective_to <= now()->addDays(30) &&
               $this->effective_to > now();
    }

    // دوال حساب الرسوم
    public function calculateFee(float $quantity, ?string $shipType = null, ?string $cargoType = null): float
    {
        if (!$this->isApplicable($shipType, $cargoType)) {
            return 0;
        }

        $amount = match($this->calculation_method) {
            'fixed' => $this->base_rate,
            'per_ton' => $this->base_rate * $quantity,
            'per_hour' => $this->base_rate * $quantity,
            'per_day' => $this->base_rate * $quantity,
            'per_container' => $this->base_rate * $quantity,
            'percentage' => $quantity * ($this->base_rate / 100),
            default => $this->base_rate
        };

        // تطبيق الشرائح إذا كانت موجودة
        if ($this->rate_tiers) {
            $amount = $this->applyTiers($quantity);
        }

        // تطبيق الحد الأدنى والأقصى
        if ($this->minimum_charge) {
            $amount = max($amount, $this->minimum_charge);
        }

        if ($this->maximum_charge) {
            $amount = min($amount, $this->maximum_charge);
        }

        return round($amount, 2);
    }

    public function isApplicable(?string $shipType = null, ?string $cargoType = null): bool
    {
        if (!$this->is_active_now) {
            return false;
        }

        if ($shipType && $this->applicable_ship_types &&
            !in_array($shipType, $this->applicable_ship_types)) {
            return false;
        }

        if ($cargoType && $this->applicable_cargo_types &&
            !in_array($cargoType, $this->applicable_cargo_types)) {
            return false;
        }

        return true;
    }

    private function applyTiers(float $quantity): float
    {
        $totalAmount = 0;
        $remainingQuantity = $quantity;

        foreach ($this->rate_tiers as $tier) {
            $tierMin = $tier['min'] ?? 0;
            $tierMax = $tier['max'] ?? null;
            $tierRate = $tier['rate'] ?? $this->base_rate;

            if ($remainingQuantity <= 0) {
                break;
            }

            $tierQuantity = $tierMax ?
                min($remainingQuantity, $tierMax - $tierMin) :
                $remainingQuantity;

            $totalAmount += $tierQuantity * $tierRate;
            $remainingQuantity -= $tierQuantity;
        }

        return $totalAmount;
    }

    public function applyDiscount(float $amount, ?array $discountCriteria = null): float
    {
        if (!$this->discounts) {
            return $amount;
        }

        $discountAmount = 0;

        foreach ($this->discounts as $discount) {
            if ($this->isDiscountApplicable($discount, $discountCriteria)) {
                $discountValue = $discount['type'] === 'percentage' ?
                    $amount * ($discount['value'] / 100) :
                    $discount['value'];

                $discountAmount += $discountValue;
            }
        }

        return max(0, $amount - $discountAmount);
    }

    private function isDiscountApplicable(array $discount, ?array $criteria = null): bool
    {
        if (!$criteria) {
            return true;
        }

        foreach ($discount['conditions'] ?? [] as $condition => $value) {
            if (!isset($criteria[$condition]) || $criteria[$condition] !== $value) {
                return false;
            }
        }

        return true;
    }
}
