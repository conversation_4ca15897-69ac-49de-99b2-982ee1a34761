import { ref, unref, withCtx, createTextVNode, createVNode, createBlock, openBlock, toDisplayString, withDirectives, withK<PERSON>s, vModelText, vModelSelect, createCommentVNode, Fragment, renderList, useSSRContext } from "vue";
import { ssrRenderComponent, ssrInterpolate, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual, ssrRenderList, ssrRenderClass } from "vue/server-renderer";
import { _ as _sfc_main$1 } from "./AuthenticatedLayout-DMuuhipg.js";
import { Head, Link, router } from "@inertiajs/vue3";
import "./ApplicationLogo-B2173abF.js";
import "./_plugin-vue_export-helper-1tPrXgE0.js";
const _sfc_main = {
  __name: "Index",
  __ssrInlineRender: true,
  props: {
    ships: Object,
    statistics: Object,
    filters: Object
  },
  setup(__props) {
    const props = __props;
    const search = ref(props.filters.search || "");
    const type = ref(props.filters.type || "");
    const flag = ref(props.filters.flag || "");
    const status = ref(props.filters.status || "");
    const performSearch = () => {
      router.get(route("ships.index"), {
        search: search.value,
        type: type.value,
        flag: flag.value,
        status: status.value
      }, {
        preserveState: true,
        replace: true
      });
    };
    const getStatusColor = (status2) => {
      const colors = {
        "active": "bg-green-100 text-green-800",
        "inactive": "bg-gray-100 text-gray-800",
        "blacklisted": "bg-red-100 text-red-800"
      };
      return colors[status2] || "bg-gray-100 text-gray-800";
    };
    const getStatusText = (status2) => {
      const texts = {
        "active": "نشطة",
        "inactive": "غير نشطة",
        "blacklisted": "محظورة"
      };
      return texts[status2] || status2;
    };
    const getShipTypeText = (type2) => {
      const types = {
        "container": "حاويات",
        "bulk": "بضائع سائبة",
        "general": "بضائع عامة",
        "liquid": "سوائل",
        "passenger": "ركاب",
        "ro_ro": "رو رو"
      };
      return types[type2] || type2;
    };
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<!--[-->`);
      _push(ssrRenderComponent(unref(Head), { title: "السفن" }, null, _parent));
      _push(ssrRenderComponent(_sfc_main$1, null, {
        header: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="flex justify-between items-center"${_scopeId}><h2 class="font-semibold text-xl text-gray-800 leading-tight"${_scopeId}> السفن </h2>`);
            _push2(ssrRenderComponent(unref(Link), {
              href: _ctx.route("ships.create"),
              class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(` إضافة سفينة جديدة `);
                } else {
                  return [
                    createTextVNode(" إضافة سفينة جديدة ")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`</div>`);
          } else {
            return [
              createVNode("div", { class: "flex justify-between items-center" }, [
                createVNode("h2", { class: "font-semibold text-xl text-gray-800 leading-tight" }, " السفن "),
                createVNode(unref(Link), {
                  href: _ctx.route("ships.create"),
                  class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                }, {
                  default: withCtx(() => [
                    createTextVNode(" إضافة سفينة جديدة ")
                  ]),
                  _: 1
                }, 8, ["href"])
              ])
            ];
          }
        }),
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="py-12"${_scopeId}><div class="max-w-7xl mx-auto sm:px-6 lg:px-8"${_scopeId}><div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"${_scopeId}><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"${_scopeId}><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"${_scopeId}></path></svg></div></div><div class="ml-4"${_scopeId}><p class="text-sm font-medium text-gray-500"${_scopeId}>إجمالي السفن</p><p class="text-2xl font-semibold text-gray-900"${_scopeId}>${ssrInterpolate(__props.statistics.total)}</p></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"${_scopeId}><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"${_scopeId}></path></svg></div></div><div class="ml-4"${_scopeId}><p class="text-sm font-medium text-gray-500"${_scopeId}>نشطة</p><p class="text-2xl font-semibold text-gray-900"${_scopeId}>${ssrInterpolate(__props.statistics.active)}</p></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center"${_scopeId}><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"${_scopeId}></path><path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"${_scopeId}></path></svg></div></div><div class="ml-4"${_scopeId}><p class="text-sm font-medium text-gray-500"${_scopeId}>متوسط العمر</p><p class="text-2xl font-semibold text-gray-900"${_scopeId}>${ssrInterpolate(__props.statistics.average_age)} سنة</p></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center"${_scopeId}><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1zM3 7a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1V7zM3 12a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1z" clip-rule="evenodd"${_scopeId}></path></svg></div></div><div class="ml-4"${_scopeId}><p class="text-sm font-medium text-gray-500"${_scopeId}>أنواع السفن</p><p class="text-2xl font-semibold text-gray-900"${_scopeId}>${ssrInterpolate(Object.keys(__props.statistics.by_type || {}).length)}</p></div></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"${_scopeId}><div class="p-6"${_scopeId}><div class="grid grid-cols-1 md:grid-cols-5 gap-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}>البحث</label><input${ssrRenderAttr("value", search.value)} type="text" placeholder="البحث بالاسم أو رقم IMO..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"${_scopeId}></div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}>نوع السفينة</label><select class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"${_scopeId}><option value=""${ssrIncludeBooleanAttr(Array.isArray(type.value) ? ssrLooseContain(type.value, "") : ssrLooseEqual(type.value, "")) ? " selected" : ""}${_scopeId}>جميع الأنواع</option><option value="container"${ssrIncludeBooleanAttr(Array.isArray(type.value) ? ssrLooseContain(type.value, "container") : ssrLooseEqual(type.value, "container")) ? " selected" : ""}${_scopeId}>حاويات</option><option value="bulk"${ssrIncludeBooleanAttr(Array.isArray(type.value) ? ssrLooseContain(type.value, "bulk") : ssrLooseEqual(type.value, "bulk")) ? " selected" : ""}${_scopeId}>بضائع سائبة</option><option value="general"${ssrIncludeBooleanAttr(Array.isArray(type.value) ? ssrLooseContain(type.value, "general") : ssrLooseEqual(type.value, "general")) ? " selected" : ""}${_scopeId}>بضائع عامة</option><option value="liquid"${ssrIncludeBooleanAttr(Array.isArray(type.value) ? ssrLooseContain(type.value, "liquid") : ssrLooseEqual(type.value, "liquid")) ? " selected" : ""}${_scopeId}>سوائل</option><option value="passenger"${ssrIncludeBooleanAttr(Array.isArray(type.value) ? ssrLooseContain(type.value, "passenger") : ssrLooseEqual(type.value, "passenger")) ? " selected" : ""}${_scopeId}>ركاب</option><option value="ro_ro"${ssrIncludeBooleanAttr(Array.isArray(type.value) ? ssrLooseContain(type.value, "ro_ro") : ssrLooseEqual(type.value, "ro_ro")) ? " selected" : ""}${_scopeId}>رو رو</option></select></div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}>دولة العلم</label><input${ssrRenderAttr("value", flag.value)} type="text" placeholder="دولة العلم..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"${_scopeId}></div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}>الحالة</label><select class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"${_scopeId}><option value=""${ssrIncludeBooleanAttr(Array.isArray(status.value) ? ssrLooseContain(status.value, "") : ssrLooseEqual(status.value, "")) ? " selected" : ""}${_scopeId}>جميع الحالات</option><option value="active"${ssrIncludeBooleanAttr(Array.isArray(status.value) ? ssrLooseContain(status.value, "active") : ssrLooseEqual(status.value, "active")) ? " selected" : ""}${_scopeId}>نشطة</option><option value="inactive"${ssrIncludeBooleanAttr(Array.isArray(status.value) ? ssrLooseContain(status.value, "inactive") : ssrLooseEqual(status.value, "inactive")) ? " selected" : ""}${_scopeId}>غير نشطة</option><option value="blacklisted"${ssrIncludeBooleanAttr(Array.isArray(status.value) ? ssrLooseContain(status.value, "blacklisted") : ssrLooseEqual(status.value, "blacklisted")) ? " selected" : ""}${_scopeId}>محظورة</option></select></div><div class="flex items-end"${_scopeId}><button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full"${_scopeId}> بحث </button></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="overflow-x-auto"${_scopeId}><table class="min-w-full divide-y divide-gray-200"${_scopeId}><thead class="bg-gray-50"${_scopeId}><tr${_scopeId}><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> اسم السفينة </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> رقم IMO </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> النوع </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> دولة العلم </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> الحمولة الإجمالية </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> سنة البناء </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> الحالة </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> الإجراءات </th></tr></thead><tbody class="bg-white divide-y divide-gray-200"${_scopeId}><!--[-->`);
            ssrRenderList(__props.ships.data, (ship) => {
              _push2(`<tr class="hover:bg-gray-50"${_scopeId}><td class="px-6 py-4 whitespace-nowrap"${_scopeId}><div class="text-sm font-medium text-gray-900"${_scopeId}>${ssrInterpolate(ship.name)}</div><div class="text-sm text-gray-500"${_scopeId}>${ssrInterpolate(ship.name_en)}</div></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"${_scopeId}>${ssrInterpolate(ship.imo_number)}</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"${_scopeId}>${ssrInterpolate(getShipTypeText(ship.ship_type))}</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"${_scopeId}>${ssrInterpolate(ship.flag_country)}</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"${_scopeId}>${ssrInterpolate(ship.gross_tonnage?.toLocaleString())} طن </td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"${_scopeId}>${ssrInterpolate(ship.year_built)}</td><td class="px-6 py-4 whitespace-nowrap"${_scopeId}><span class="${ssrRenderClass([getStatusColor(ship.status), "inline-flex px-2 py-1 text-xs font-semibold rounded-full"])}"${_scopeId}>${ssrInterpolate(getStatusText(ship.status))}</span></td><td class="px-6 py-4 whitespace-nowrap text-sm font-medium"${_scopeId}><div class="flex space-x-2"${_scopeId}>`);
              _push2(ssrRenderComponent(unref(Link), {
                href: _ctx.route("ships.show", ship.id),
                class: "text-indigo-600 hover:text-indigo-900"
              }, {
                default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(` عرض `);
                  } else {
                    return [
                      createTextVNode(" عرض ")
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
              _push2(ssrRenderComponent(unref(Link), {
                href: _ctx.route("ships.edit", ship.id),
                class: "text-green-600 hover:text-green-900"
              }, {
                default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(` تعديل `);
                  } else {
                    return [
                      createTextVNode(" تعديل ")
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
              _push2(`</div></td></tr>`);
            });
            _push2(`<!--]--></tbody></table></div>`);
            if (__props.ships.links) {
              _push2(`<div class="px-6 py-3 border-t border-gray-200"${_scopeId}><div class="flex justify-between items-center"${_scopeId}><div class="text-sm text-gray-700"${_scopeId}> عرض ${ssrInterpolate(__props.ships.from)} إلى ${ssrInterpolate(__props.ships.to)} من ${ssrInterpolate(__props.ships.total)} نتيجة </div><div class="flex space-x-1"${_scopeId}><!--[-->`);
              ssrRenderList(__props.ships.links, (link) => {
                _push2(`<!--[-->`);
                if (link.url) {
                  _push2(ssrRenderComponent(unref(Link), {
                    href: link.url,
                    class: [
                      "px-3 py-2 text-sm rounded-md",
                      link.active ? "bg-blue-500 text-white" : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-300"
                    ]
                  }, null, _parent2, _scopeId));
                } else {
                  _push2(`<span class="${ssrRenderClass([
                    "px-3 py-2 text-sm rounded-md",
                    "bg-gray-100 text-gray-400 cursor-not-allowed"
                  ])}"${_scopeId}>${link.label ?? ""}</span>`);
                }
                _push2(`<!--]-->`);
              });
              _push2(`<!--]--></div></div></div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div></div>`);
          } else {
            return [
              createVNode("div", { class: "py-12" }, [
                createVNode("div", { class: "max-w-7xl mx-auto sm:px-6 lg:px-8" }, [
                  createVNode("div", { class: "grid grid-cols-1 md:grid-cols-4 gap-6 mb-8" }, [
                    createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                      createVNode("div", { class: "p-6" }, [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", { class: "flex-shrink-0" }, [
                            createVNode("div", { class: "w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center" }, [
                              (openBlock(), createBlock("svg", {
                                class: "w-4 h-4 text-white",
                                fill: "currentColor",
                                viewBox: "0 0 20 20"
                              }, [
                                createVNode("path", { d: "M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" })
                              ]))
                            ])
                          ]),
                          createVNode("div", { class: "ml-4" }, [
                            createVNode("p", { class: "text-sm font-medium text-gray-500" }, "إجمالي السفن"),
                            createVNode("p", { class: "text-2xl font-semibold text-gray-900" }, toDisplayString(__props.statistics.total), 1)
                          ])
                        ])
                      ])
                    ]),
                    createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                      createVNode("div", { class: "p-6" }, [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", { class: "flex-shrink-0" }, [
                            createVNode("div", { class: "w-8 h-8 bg-green-500 rounded-full flex items-center justify-center" }, [
                              (openBlock(), createBlock("svg", {
                                class: "w-4 h-4 text-white",
                                fill: "currentColor",
                                viewBox: "0 0 20 20"
                              }, [
                                createVNode("path", {
                                  "fill-rule": "evenodd",
                                  d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
                                  "clip-rule": "evenodd"
                                })
                              ]))
                            ])
                          ]),
                          createVNode("div", { class: "ml-4" }, [
                            createVNode("p", { class: "text-sm font-medium text-gray-500" }, "نشطة"),
                            createVNode("p", { class: "text-2xl font-semibold text-gray-900" }, toDisplayString(__props.statistics.active), 1)
                          ])
                        ])
                      ])
                    ]),
                    createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                      createVNode("div", { class: "p-6" }, [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", { class: "flex-shrink-0" }, [
                            createVNode("div", { class: "w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center" }, [
                              (openBlock(), createBlock("svg", {
                                class: "w-4 h-4 text-white",
                                fill: "currentColor",
                                viewBox: "0 0 20 20"
                              }, [
                                createVNode("path", { d: "M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z" }),
                                createVNode("path", { d: "M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z" })
                              ]))
                            ])
                          ]),
                          createVNode("div", { class: "ml-4" }, [
                            createVNode("p", { class: "text-sm font-medium text-gray-500" }, "متوسط العمر"),
                            createVNode("p", { class: "text-2xl font-semibold text-gray-900" }, toDisplayString(__props.statistics.average_age) + " سنة", 1)
                          ])
                        ])
                      ])
                    ]),
                    createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                      createVNode("div", { class: "p-6" }, [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", { class: "flex-shrink-0" }, [
                            createVNode("div", { class: "w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center" }, [
                              (openBlock(), createBlock("svg", {
                                class: "w-4 h-4 text-white",
                                fill: "currentColor",
                                viewBox: "0 0 20 20"
                              }, [
                                createVNode("path", {
                                  "fill-rule": "evenodd",
                                  d: "M3 17a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1zM3 7a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1V7zM3 12a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1z",
                                  "clip-rule": "evenodd"
                                })
                              ]))
                            ])
                          ]),
                          createVNode("div", { class: "ml-4" }, [
                            createVNode("p", { class: "text-sm font-medium text-gray-500" }, "أنواع السفن"),
                            createVNode("p", { class: "text-2xl font-semibold text-gray-900" }, toDisplayString(Object.keys(__props.statistics.by_type || {}).length), 1)
                          ])
                        ])
                      ])
                    ])
                  ]),
                  createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6" }, [
                    createVNode("div", { class: "p-6" }, [
                      createVNode("div", { class: "grid grid-cols-1 md:grid-cols-5 gap-4" }, [
                        createVNode("div", null, [
                          createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, "البحث"),
                          withDirectives(createVNode("input", {
                            "onUpdate:modelValue": ($event) => search.value = $event,
                            onKeyup: withKeys(performSearch, ["enter"]),
                            type: "text",
                            placeholder: "البحث بالاسم أو رقم IMO...",
                            class: "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                          }, null, 40, ["onUpdate:modelValue"]), [
                            [vModelText, search.value]
                          ])
                        ]),
                        createVNode("div", null, [
                          createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, "نوع السفينة"),
                          withDirectives(createVNode("select", {
                            "onUpdate:modelValue": ($event) => type.value = $event,
                            onChange: performSearch,
                            class: "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                          }, [
                            createVNode("option", { value: "" }, "جميع الأنواع"),
                            createVNode("option", { value: "container" }, "حاويات"),
                            createVNode("option", { value: "bulk" }, "بضائع سائبة"),
                            createVNode("option", { value: "general" }, "بضائع عامة"),
                            createVNode("option", { value: "liquid" }, "سوائل"),
                            createVNode("option", { value: "passenger" }, "ركاب"),
                            createVNode("option", { value: "ro_ro" }, "رو رو")
                          ], 40, ["onUpdate:modelValue"]), [
                            [vModelSelect, type.value]
                          ])
                        ]),
                        createVNode("div", null, [
                          createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, "دولة العلم"),
                          withDirectives(createVNode("input", {
                            "onUpdate:modelValue": ($event) => flag.value = $event,
                            onKeyup: withKeys(performSearch, ["enter"]),
                            type: "text",
                            placeholder: "دولة العلم...",
                            class: "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                          }, null, 40, ["onUpdate:modelValue"]), [
                            [vModelText, flag.value]
                          ])
                        ]),
                        createVNode("div", null, [
                          createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, "الحالة"),
                          withDirectives(createVNode("select", {
                            "onUpdate:modelValue": ($event) => status.value = $event,
                            onChange: performSearch,
                            class: "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                          }, [
                            createVNode("option", { value: "" }, "جميع الحالات"),
                            createVNode("option", { value: "active" }, "نشطة"),
                            createVNode("option", { value: "inactive" }, "غير نشطة"),
                            createVNode("option", { value: "blacklisted" }, "محظورة")
                          ], 40, ["onUpdate:modelValue"]), [
                            [vModelSelect, status.value]
                          ])
                        ]),
                        createVNode("div", { class: "flex items-end" }, [
                          createVNode("button", {
                            onClick: performSearch,
                            class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full"
                          }, " بحث ")
                        ])
                      ])
                    ])
                  ]),
                  createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                    createVNode("div", { class: "overflow-x-auto" }, [
                      createVNode("table", { class: "min-w-full divide-y divide-gray-200" }, [
                        createVNode("thead", { class: "bg-gray-50" }, [
                          createVNode("tr", null, [
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " اسم السفينة "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " رقم IMO "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " النوع "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " دولة العلم "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " الحمولة الإجمالية "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " سنة البناء "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " الحالة "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " الإجراءات ")
                          ])
                        ]),
                        createVNode("tbody", { class: "bg-white divide-y divide-gray-200" }, [
                          (openBlock(true), createBlock(Fragment, null, renderList(__props.ships.data, (ship) => {
                            return openBlock(), createBlock("tr", {
                              key: ship.id,
                              class: "hover:bg-gray-50"
                            }, [
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap" }, [
                                createVNode("div", { class: "text-sm font-medium text-gray-900" }, toDisplayString(ship.name), 1),
                                createVNode("div", { class: "text-sm text-gray-500" }, toDisplayString(ship.name_en), 1)
                              ]),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap text-sm text-gray-900" }, toDisplayString(ship.imo_number), 1),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap text-sm text-gray-900" }, toDisplayString(getShipTypeText(ship.ship_type)), 1),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap text-sm text-gray-900" }, toDisplayString(ship.flag_country), 1),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap text-sm text-gray-900" }, toDisplayString(ship.gross_tonnage?.toLocaleString()) + " طن ", 1),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap text-sm text-gray-900" }, toDisplayString(ship.year_built), 1),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap" }, [
                                createVNode("span", {
                                  class: [getStatusColor(ship.status), "inline-flex px-2 py-1 text-xs font-semibold rounded-full"]
                                }, toDisplayString(getStatusText(ship.status)), 3)
                              ]),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap text-sm font-medium" }, [
                                createVNode("div", { class: "flex space-x-2" }, [
                                  createVNode(unref(Link), {
                                    href: _ctx.route("ships.show", ship.id),
                                    class: "text-indigo-600 hover:text-indigo-900"
                                  }, {
                                    default: withCtx(() => [
                                      createTextVNode(" عرض ")
                                    ]),
                                    _: 2
                                  }, 1032, ["href"]),
                                  createVNode(unref(Link), {
                                    href: _ctx.route("ships.edit", ship.id),
                                    class: "text-green-600 hover:text-green-900"
                                  }, {
                                    default: withCtx(() => [
                                      createTextVNode(" تعديل ")
                                    ]),
                                    _: 2
                                  }, 1032, ["href"])
                                ])
                              ])
                            ]);
                          }), 128))
                        ])
                      ])
                    ]),
                    __props.ships.links ? (openBlock(), createBlock("div", {
                      key: 0,
                      class: "px-6 py-3 border-t border-gray-200"
                    }, [
                      createVNode("div", { class: "flex justify-between items-center" }, [
                        createVNode("div", { class: "text-sm text-gray-700" }, " عرض " + toDisplayString(__props.ships.from) + " إلى " + toDisplayString(__props.ships.to) + " من " + toDisplayString(__props.ships.total) + " نتيجة ", 1),
                        createVNode("div", { class: "flex space-x-1" }, [
                          (openBlock(true), createBlock(Fragment, null, renderList(__props.ships.links, (link) => {
                            return openBlock(), createBlock(Fragment, {
                              key: link.label
                            }, [
                              link.url ? (openBlock(), createBlock(unref(Link), {
                                key: 0,
                                href: link.url,
                                class: [
                                  "px-3 py-2 text-sm rounded-md",
                                  link.active ? "bg-blue-500 text-white" : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-300"
                                ],
                                innerHTML: link.label
                              }, null, 8, ["href", "class", "innerHTML"])) : (openBlock(), createBlock("span", {
                                key: 1,
                                class: [
                                  "px-3 py-2 text-sm rounded-md",
                                  "bg-gray-100 text-gray-400 cursor-not-allowed"
                                ],
                                innerHTML: link.label
                              }, null, 8, ["innerHTML"]))
                            ], 64);
                          }), 128))
                        ])
                      ])
                    ])) : createCommentVNode("", true)
                  ])
                ])
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<!--]-->`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("resources/js/Pages/Ships/Index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
export {
  _sfc_main as default
};
