<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            $table->string('payment_number')->unique();
            $table->date('payment_date');
            $table->decimal('amount', 12, 2);
            $table->string('currency', 3)->default('SYP');
            $table->decimal('exchange_rate', 10, 4)->default(1);
            $table->enum('method', ['cash', 'bank_transfer', 'check', 'credit_card', 'online', 'mobile_payment']);
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'])
                ->default('pending');
            $table->string('reference_number')->nullable(); // رقم مرجعي من البنك
            $table->string('transaction_id')->nullable(); // معرف المعاملة
            $table->string('bank_name')->nullable();
            $table->string('account_number')->nullable();
            $table->date('check_date')->nullable();
            $table->string('check_number')->nullable();
            $table->text('payment_details')->nullable();
            $table->json('gateway_response')->nullable(); // استجابة بوابة الدفع
            $table->datetime('processed_at')->nullable();
            $table->foreignId('processed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('notes')->nullable();
            $table->json('attachments')->nullable(); // مرفقات (إيصالات، صور)
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['status', 'payment_date']);
            $table->index(['method', 'currency']);
            $table->index('payment_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
