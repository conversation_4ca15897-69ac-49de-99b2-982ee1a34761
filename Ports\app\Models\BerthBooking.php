<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class BerthBooking extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'berth_id',
        'voyage_id',
        'arrival_id',
        'booking_number',
        'start_time',
        'end_time',
        'actual_start_time',
        'actual_end_time',
        'status',
        'priority',
        'purpose',
        'required_services',
        'equipment_needed',
        'estimated_cost',
        'actual_cost',
        'special_instructions',
        'cancellation_reason',
        'approved_by',
        'approved_at',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'actual_start_time' => 'datetime',
        'actual_end_time' => 'datetime',
        'approved_at' => 'datetime',
        'estimated_cost' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'required_services' => 'array',
        'equipment_needed' => 'array',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'requested',
        'priority' => 'normal',
    ];

    // العلاقات
    public function berth(): BelongsTo
    {
        return $this->belongsTo(Berth::class);
    }

    public function voyage(): BelongsTo
    {
        return $this->belongsTo(Voyage::class);
    }

    public function arrival(): BelongsTo
    {
        return $this->belongsTo(Arrival::class);
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function operations(): HasMany
    {
        return $this->hasMany(Operation::class);
    }

    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    // النطاقات
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active')
            ->where('start_time', '<=', now())
            ->where('end_time', '>=', now());
    }

    public function scopeUpcoming($query)
    {
        return $query->where('start_time', '>', now())
            ->whereIn('status', ['confirmed', 'requested']);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeForBerth($query, $berthId)
    {
        return $query->where('berth_id', $berthId);
    }

    // الخصائص المحسوبة
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active' &&
               $this->start_time <= now() &&
               $this->end_time >= now();
    }

    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'completed';
    }

    public function getPlannedDurationAttribute(): int
    {
        return $this->start_time->diffInHours($this->end_time);
    }

    public function getActualDurationAttribute(): ?int
    {
        if ($this->actual_start_time && $this->actual_end_time) {
            return $this->actual_start_time->diffInHours($this->actual_end_time);
        }
        return null;
    }

    public function getDurationVarianceAttribute(): ?int
    {
        $actualDuration = $this->actual_duration;
        if ($actualDuration !== null) {
            return $actualDuration - $this->planned_duration;
        }
        return null;
    }

    public function getCostVarianceAttribute(): ?float
    {
        if ($this->actual_cost !== null && $this->estimated_cost !== null) {
            return $this->actual_cost - $this->estimated_cost;
        }
        return null;
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->status === 'active' && $this->end_time < now();
    }

    public function getTimeRemainingAttribute(): ?int
    {
        if ($this->is_active) {
            return now()->diffInHours($this->end_time, false);
        }
        return null;
    }

    // دوال مساعدة
    public function canBeModified(): bool
    {
        return in_array($this->status, ['requested', 'confirmed']) &&
               $this->start_time > now()->addHours(2);
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['requested', 'confirmed']) &&
               $this->start_time > now()->addHours(1);
    }
}
