<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('voyages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ship_id')->constrained()->onDelete('cascade');
            $table->foreignId('agent_id')->constrained()->onDelete('cascade');
            $table->string('voyage_number')->unique();
            $table->string('origin_port');
            $table->string('destination_port');
            $table->string('last_port')->nullable();
            $table->string('next_port')->nullable();
            $table->enum('voyage_type', ['arrival', 'departure', 'transit']);
            $table->enum('cargo_type', ['container', 'bulk', 'general', 'liquid', 'passenger', 'empty']);
            $table->decimal('cargo_quantity', 12, 2)->nullable();
            $table->string('cargo_unit')->nullable(); // طن، حاوية، متر مكعب
            $table->text('cargo_description')->nullable();
            $table->integer('crew_count');
            $table->integer('passenger_count')->default(0);
            $table->datetime('eta'); // Estimated Time of Arrival
            $table->datetime('etd')->nullable(); // Estimated Time of Departure
            $table->datetime('ata')->nullable(); // Actual Time of Arrival
            $table->datetime('atd')->nullable(); // Actual Time of Departure
            $table->enum('status', ['planned', 'arrived', 'berthed', 'working', 'completed', 'departed', 'cancelled'])
                ->default('planned');
            $table->text('purpose')->nullable(); // غرض الزيارة
            $table->json('dangerous_goods')->nullable(); // بضائع خطرة
            $table->json('crew_list')->nullable(); // قائمة الطاقم
            $table->json('passenger_list')->nullable(); // قائمة الركاب
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['status', 'eta']);
            $table->index(['voyage_type', 'cargo_type']);
            $table->index('voyage_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('voyages');
    }
};
