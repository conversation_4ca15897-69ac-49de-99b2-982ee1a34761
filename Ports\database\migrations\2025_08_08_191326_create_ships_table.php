<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ships', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('name_en')->nullable();
            $table->string('imo_number')->unique();
            $table->string('call_sign')->unique();
            $table->string('mmsi_number')->nullable();
            $table->string('flag_country');
            $table->string('ship_type');
            $table->decimal('length', 8, 2);
            $table->decimal('width', 8, 2);
            $table->decimal('draft', 8, 2);
            $table->decimal('gross_tonnage', 10, 2);
            $table->decimal('net_tonnage', 10, 2);
            $table->decimal('deadweight', 10, 2);
            $table->integer('year_built');
            $table->string('classification_society')->nullable();
            $table->string('owner_name');
            $table->string('owner_address')->nullable();
            $table->string('operator_name')->nullable();
            $table->string('operator_address')->nullable();
            $table->enum('status', ['active', 'inactive', 'blacklisted'])->default('active');
            $table->json('certificates')->nullable(); // شهادات السفينة
            $table->json('equipment')->nullable(); // معدات السفينة
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['ship_type', 'flag_country']);
            $table->index(['name', 'status']);
            $table->index('gross_tonnage');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ships');
    }
};
