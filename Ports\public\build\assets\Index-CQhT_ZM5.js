import{m as g,c as d,o as a,a as n,b as u,h as S,w as c,d as t,t as o,f as p,p as k,v as M,k as C,g as H,F as m,i as V,n as w,q as h,l as _,s as D,x as N}from"./app-CdA4UbU6.js";import{_ as O}from"./AuthenticatedLayout-CBV1ADW9.js";import"./ApplicationLogo-WdDnJ3OK.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const U={class:"flex justify-between items-center"},I={class:"py-12"},K={class:"max-w-7xl mx-auto sm:px-6 lg:px-8"},q={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"},A={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},F={class:"p-6"},E={class:"flex items-center"},$={class:"ml-4"},G={class:"text-2xl font-semibold text-gray-900"},J={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},P={class:"p-6"},Q={class:"flex items-center"},R={class:"ml-4"},W={class:"text-2xl font-semibold text-gray-900"},X={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},Y={class:"p-6"},Z={class:"flex items-center"},tt={class:"ml-4"},et={class:"text-2xl font-semibold text-gray-900"},st={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},ot={class:"p-6"},lt={class:"flex items-center"},it={class:"ml-4"},at={class:"text-2xl font-semibold text-gray-900"},dt={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},rt={class:"p-6"},nt={class:"grid grid-cols-1 md:grid-cols-5 gap-4"},ut={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},ct={class:"overflow-x-auto"},xt={class:"min-w-full divide-y divide-gray-200"},gt={class:"bg-white divide-y divide-gray-200"},pt={class:"px-6 py-4 whitespace-nowrap"},mt={class:"text-sm font-medium text-gray-900"},ht={class:"text-sm text-gray-500"},vt={class:"px-6 py-4 whitespace-nowrap"},ft={class:"text-sm font-medium text-gray-900"},yt={class:"text-sm text-gray-500"},bt={class:"px-6 py-4 whitespace-nowrap"},wt={class:"text-sm text-gray-900"},_t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},kt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Mt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Ct={class:"px-6 py-4 whitespace-nowrap"},Vt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Tt={class:"flex space-x-2"},zt={key:0,class:"px-6 py-3 border-t border-gray-200"},jt={class:"flex justify-between items-center"},Bt={class:"text-sm text-gray-700"},Lt={class:"flex space-x-1"},St=["innerHTML"],Ut={__name:"Index",props:{voyages:Object,statistics:Object,filters:Object},setup(i){const x=i,v=g(x.filters.search||""),f=g(x.filters.status||""),y=g(x.filters.type||""),b=g(x.filters.agent_id||""),r=()=>{N.get(route("voyages.index"),{search:v.value,status:f.value,type:y.value,agent_id:b.value},{preserveState:!0,replace:!0})},T=l=>({planned:"bg-blue-100 text-blue-800",arrived:"bg-yellow-100 text-yellow-800",berthed:"bg-green-100 text-green-800",working:"bg-purple-100 text-purple-800",completed:"bg-gray-100 text-gray-800",departed:"bg-indigo-100 text-indigo-800",cancelled:"bg-red-100 text-red-800"})[l]||"bg-gray-100 text-gray-800",z=l=>({planned:"مخططة",arrived:"وصلت",berthed:"راسية",working:"تعمل",completed:"مكتملة",departed:"غادرت",cancelled:"ملغية"})[l]||l,j=l=>({arrival:"وصول",departure:"مغادرة",transit:"عبور"})[l]||l,B=l=>({container:"حاويات",bulk:"بضائع سائبة",general:"بضائع عامة",liquid:"سوائل",passenger:"ركاب",empty:"فارغة"})[l]||l,L=l=>new Date(l).toLocaleDateString("ar-SA",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return(l,e)=>(a(),d(m,null,[n(u(S),{title:"الرحلات"}),n(O,null,{header:c(()=>[t("div",U,[e[5]||(e[5]=t("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"}," الرحلات ",-1)),n(u(h),{href:l.route("voyages.create"),class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"},{default:c(()=>e[4]||(e[4]=[_(" إضافة رحلة جديدة ",-1)])),_:1,__:[4]},8,["href"])])]),default:c(()=>[t("div",I,[t("div",K,[t("div",q,[t("div",A,[t("div",F,[t("div",E,[e[7]||(e[7]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"})])])],-1)),t("div",$,[e[6]||(e[6]=t("p",{class:"text-sm font-medium text-gray-500"},"إجمالي الرحلات",-1)),t("p",G,o(i.statistics.total),1)])])])]),t("div",J,[t("div",P,[t("div",Q,[e[9]||(e[9]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])])],-1)),t("div",R,[e[8]||(e[8]=t("p",{class:"text-sm font-medium text-gray-500"},"نشطة",-1)),t("p",W,o(i.statistics.active),1)])])])]),t("div",X,[t("div",Y,[t("div",Z,[e[11]||(e[11]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})])])],-1)),t("div",tt,[e[10]||(e[10]=t("p",{class:"text-sm font-medium text-gray-500"},"مكتملة",-1)),t("p",et,o(i.statistics.completed),1)])])])]),t("div",st,[t("div",ot,[t("div",lt,[e[13]||(e[13]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"}),t("path",{d:"M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"})])])],-1)),t("div",it,[e[12]||(e[12]=t("p",{class:"text-sm font-medium text-gray-500"},"متوسط الإقامة",-1)),t("p",at,o(i.statistics.average_stay_time_hours)+" ساعة",1)])])])])]),t("div",dt,[t("div",rt,[t("div",nt,[t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"البحث",-1)),p(t("input",{"onUpdate:modelValue":e[0]||(e[0]=s=>v.value=s),onKeyup:k(r,["enter"]),type:"text",placeholder:"البحث برقم الرحلة أو اسم السفينة...",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},null,544),[[M,v.value]])]),t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"الحالة",-1)),p(t("select",{"onUpdate:modelValue":e[1]||(e[1]=s=>f.value=s),onChange:r,class:"w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},e[15]||(e[15]=[t("option",{value:""},"جميع الحالات",-1),t("option",{value:"planned"},"مخططة",-1),t("option",{value:"arrived"},"وصلت",-1),t("option",{value:"berthed"},"راسية",-1),t("option",{value:"working"},"تعمل",-1),t("option",{value:"completed"},"مكتملة",-1),t("option",{value:"departed"},"غادرت",-1),t("option",{value:"cancelled"},"ملغية",-1)]),544),[[C,f.value]])]),t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"نوع الرحلة",-1)),p(t("select",{"onUpdate:modelValue":e[2]||(e[2]=s=>y.value=s),onChange:r,class:"w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},e[17]||(e[17]=[t("option",{value:""},"جميع الأنواع",-1),t("option",{value:"arrival"},"وصول",-1),t("option",{value:"departure"},"مغادرة",-1),t("option",{value:"transit"},"عبور",-1)]),544),[[C,y.value]])]),t("div",null,[e[19]||(e[19]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"الوكيل",-1)),p(t("input",{"onUpdate:modelValue":e[3]||(e[3]=s=>b.value=s),onKeyup:k(r,["enter"]),type:"text",placeholder:"معرف الوكيل...",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},null,544),[[M,b.value]])]),t("div",{class:"flex items-end"},[t("button",{onClick:r,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full"}," بحث ")])])])]),t("div",ut,[t("div",ct,[t("table",xt,[e[22]||(e[22]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," رقم الرحلة "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," السفينة "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," الوكيل "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," النوع "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," نوع البضاعة "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," الوصول المتوقع "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," الحالة "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," الإجراءات ")])],-1)),t("tbody",gt,[(a(!0),d(m,null,V(i.voyages.data,s=>(a(),d("tr",{key:s.id,class:"hover:bg-gray-50"},[t("td",pt,[t("div",mt,o(s.voyage_number),1),t("div",ht,o(s.origin_port)+" → "+o(s.destination_port),1)]),t("td",vt,[t("div",ft,o(s.ship?.name),1),t("div",yt,"IMO: "+o(s.ship?.imo_number),1)]),t("td",bt,[t("div",wt,o(s.agent?.company_name),1)]),t("td",_t,o(j(s.voyage_type)),1),t("td",kt,o(B(s.cargo_type)),1),t("td",Mt,o(L(s.eta)),1),t("td",Ct,[t("span",{class:w([T(s.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(z(s.status)),3)]),t("td",Vt,[t("div",Tt,[n(u(h),{href:l.route("voyages.show",s.id),class:"text-indigo-600 hover:text-indigo-900"},{default:c(()=>e[20]||(e[20]=[_(" عرض ",-1)])),_:2,__:[20]},1032,["href"]),n(u(h),{href:l.route("voyages.edit",s.id),class:"text-green-600 hover:text-green-900"},{default:c(()=>e[21]||(e[21]=[_(" تعديل ",-1)])),_:2,__:[21]},1032,["href"])])])]))),128))])])]),i.voyages.links?(a(),d("div",zt,[t("div",jt,[t("div",Bt," عرض "+o(i.voyages.from)+" إلى "+o(i.voyages.to)+" من "+o(i.voyages.total)+" نتيجة ",1),t("div",Lt,[(a(!0),d(m,null,V(i.voyages.links,s=>(a(),d(m,{key:s.label},[s.url?(a(),D(u(h),{key:0,href:s.url,class:w(["px-3 py-2 text-sm rounded-md",s.active?"bg-blue-500 text-white":"bg-white text-gray-700 hover:bg-gray-50 border border-gray-300"]),innerHTML:s.label},null,8,["href","class","innerHTML"])):(a(),d("span",{key:1,class:w(["px-3 py-2 text-sm rounded-md","bg-gray-100 text-gray-400 cursor-not-allowed"]),innerHTML:s.label},null,8,St))],64))),128))])])])):H("",!0)])])])]),_:1})],64))}};export{Ut as default};
