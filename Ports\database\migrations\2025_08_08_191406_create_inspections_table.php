<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inspections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('voyage_id')->constrained()->onDelete('cascade');
            $table->string('inspection_number')->unique();
            $table->enum('type', ['safety', 'security', 'customs', 'health', 'immigration', 'environmental', 'technical']);
            $table->string('authority'); // الجهة المفتشة
            $table->foreignId('inspector_id')->constrained('users')->onDelete('cascade');
            $table->datetime('scheduled_at');
            $table->datetime('started_at')->nullable();
            $table->datetime('completed_at')->nullable();
            $table->enum('status', ['scheduled', 'in_progress', 'completed', 'cancelled', 'rescheduled'])
                ->default('scheduled');
            $table->enum('result', ['passed', 'passed_with_observations', 'failed', 'pending_reinspection'])
                ->nullable();
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
            $table->text('purpose');
            $table->text('scope')->nullable(); // نطاق التفتيش
            $table->json('checklist_items')->nullable(); // عناصر قائمة التفتيش
            $table->json('findings')->nullable(); // النتائج والملاحظات
            $table->json('deficiencies')->nullable(); // أوجه القصور
            $table->json('corrective_actions')->nullable(); // الإجراءات التصحيحية
            $table->datetime('follow_up_date')->nullable();
            $table->text('recommendations')->nullable();
            $table->json('documents_reviewed')->nullable(); // الوثائق المراجعة
            $table->json('certificates_issued')->nullable(); // الشهادات الصادرة
            $table->boolean('requires_follow_up')->default(false);
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['type', 'status']);
            $table->index(['scheduled_at', 'priority']);
            $table->index('inspection_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inspections');
    }
};
