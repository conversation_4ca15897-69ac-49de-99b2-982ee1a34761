<?php

namespace App\Services;

use App\Models\Agent;
use App\Models\User;
use App\Repositories\AgentRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class AgentService
{
    public function __construct(
        private AgentRepository $agentRepository
    ) {}

    /**
     * Get all agents with pagination
     */
    public function getAllAgents(int $perPage = 15): LengthAwarePaginator
    {
        return $this->agentRepository
            ->with(['user'])
            ->latest()
            ->paginate($perPage);
    }

    /**
     * Get active agents
     */
    public function getActiveAgents(): Collection
    {
        return $this->agentRepository->getActive();
    }

    /**
     * Find agent by ID
     */
    public function findAgent(int $id): ?Agent
    {
        return $this->agentRepository
            ->with(['user', 'voyages', 'invoices'])
            ->find($id);
    }

    /**
     * Create new agent
     */
    public function createAgent(array $data): Agent
    {
        $this->validateAgentData($data);

        $userData = [
            'name' => $data['company_name'],
            'email' => $data['email'],
            'password' => Hash::make($data['password'] ?? 'password'),
            'phone' => $data['phone'] ?? null,
            'department' => 'الوكالة الملاحية',
            'position' => 'وكيل ملاحي',
            'is_active' => true,
        ];

        $agentData = array_except($data, ['password']);

        return $this->agentRepository->createWithUser($agentData, $userData);
    }

    /**
     * Update agent
     */
    public function updateAgent(int $id, array $data): bool
    {
        $this->validateAgentData($data, $id);

        $agent = $this->agentRepository->findOrFail($id);

        // Update user data if provided
        if (isset($data['email']) || isset($data['phone'])) {
            $userData = [];
            if (isset($data['email'])) {
                $userData['email'] = $data['email'];
            }
            if (isset($data['phone'])) {
                $userData['phone'] = $data['phone'];
            }
            if (isset($data['company_name'])) {
                $userData['name'] = $data['company_name'];
            }

            $agent->user->update($userData);
        }

        // Update agent data
        $agentData = array_except($data, ['password', 'email']);
        return $this->agentRepository->update($id, $agentData);
    }

    /**
     * Delete agent
     */
    public function deleteAgent(int $id): bool
    {
        $agent = $this->agentRepository->findOrFail($id);
        
        // Check if agent has active voyages
        if ($agent->voyages()->whereIn('status', ['planned', 'arrived', 'berthed', 'working'])->exists()) {
            throw new \Exception('لا يمكن حذف الوكيل لوجود رحلات نشطة');
        }

        return $this->agentRepository->transaction(function () use ($agent) {
            // Soft delete user
            $agent->user->delete();
            
            // Soft delete agent
            return $this->agentRepository->delete($agent->id);
        });
    }

    /**
     * Update agent status
     */
    public function updateAgentStatus(int $id, string $status): bool
    {
        $validStatuses = ['active', 'inactive', 'suspended'];
        
        if (!in_array($status, $validStatuses)) {
            throw new \InvalidArgumentException('حالة غير صحيحة');
        }

        $result = $this->agentRepository->updateStatus($id, $status);

        if ($result) {
            $agent = $this->agentRepository->find($id);
            $agent->user->update(['is_active' => $status === 'active']);
        }

        return $result;
    }

    /**
     * Search agents
     */
    public function searchAgents(string $term): Collection
    {
        return $this->agentRepository->searchByName($term);
    }

    /**
     * Get agents statistics
     */
    public function getStatistics(): array
    {
        return $this->agentRepository->getStatistics();
    }

    /**
     * Get agents with expiring licenses
     */
    public function getExpiringSoon(int $days = 30): Collection
    {
        return $this->agentRepository->getExpiringSoon($days);
    }

    /**
     * Renew agent license
     */
    public function renewLicense(int $id, \DateTime $newExpiryDate): bool
    {
        return $this->agentRepository->update($id, [
            'license_expiry' => $newExpiryDate,
            'status' => 'active'
        ]);
    }

    /**
     * Get agents for dropdown
     */
    public function getAgentsForSelect(): Collection
    {
        return $this->agentRepository->getForSelect();
    }

    /**
     * Get agent dashboard data
     */
    public function getAgentDashboard(int $agentId): array
    {
        $agent = $this->agentRepository
            ->with(['voyages', 'invoices'])
            ->findOrFail($agentId);

        $activeVoyages = $agent->voyages()
            ->whereIn('status', ['planned', 'arrived', 'berthed', 'working'])
            ->count();

        $pendingInvoices = $agent->invoices()
            ->whereIn('status', ['draft', 'sent'])
            ->count();

        $totalRevenue = $agent->invoices()
            ->where('status', 'paid')
            ->sum('total_amount');

        $recentVoyages = $agent->voyages()
            ->with(['ship', 'arrival', 'departure'])
            ->latest()
            ->limit(5)
            ->get();

        return [
            'agent' => $agent,
            'active_voyages' => $activeVoyages,
            'pending_invoices' => $pendingInvoices,
            'total_revenue' => $totalRevenue,
            'recent_voyages' => $recentVoyages,
        ];
    }

    /**
     * Validate agent data
     */
    private function validateAgentData(array $data, ?int $agentId = null): void
    {
        $rules = [
            'company_name' => 'required|string|max:255',
            'license_number' => 'required|string|unique:agents,license_number' . ($agentId ? ",$agentId" : ''),
            'email' => 'required|email|unique:agents,email' . ($agentId ? ",$agentId" : ''),
            'phone' => 'required|string|max:20',
            'address' => 'required|string',
            'contact_person' => 'required|string|max:255',
            'license_expiry' => 'required|date|after:today',
            'status' => 'in:active,inactive,suspended',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    /**
     * Bulk operations
     */
    public function bulkUpdateStatus(array $agentIds, string $status): int
    {
        $validStatuses = ['active', 'inactive', 'suspended'];
        
        if (!in_array($status, $validStatuses)) {
            throw new \InvalidArgumentException('حالة غير صحيحة');
        }

        return $this->agentRepository->bulkUpdateStatus($agentIds, $status);
    }

    /**
     * Get agents requiring license renewal
     */
    public function getRequiringRenewal(): Collection
    {
        return $this->agentRepository->getRequiringRenewal();
    }

    /**
     * Generate agent report
     */
    public function generateReport(array $filters = []): array
    {
        $query = $this->agentRepository->query;

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['license_expiry_from'])) {
            $query->where('license_expiry', '>=', $filters['license_expiry_from']);
        }

        if (isset($filters['license_expiry_to'])) {
            $query->where('license_expiry', '<=', $filters['license_expiry_to']);
        }

        $agents = $query->with(['user', 'voyages', 'invoices'])->get();

        return [
            'agents' => $agents,
            'total_count' => $agents->count(),
            'active_count' => $agents->where('status', 'active')->count(),
            'inactive_count' => $agents->where('status', 'inactive')->count(),
            'suspended_count' => $agents->where('status', 'suspended')->count(),
            'total_voyages' => $agents->sum(fn($agent) => $agent->voyages->count()),
            'total_revenue' => $agents->sum(fn($agent) => $agent->invoices->where('status', 'paid')->sum('total_amount')),
        ];
    }
}
