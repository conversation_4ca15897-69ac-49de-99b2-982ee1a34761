<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, useForm, Link } from '@inertiajs/vue3';
import { ref, computed } from 'vue';

const props = defineProps({
    ships: Array,
    agents: Array,
    berths: Array
});

const form = useForm({
    ship_id: '',
    agent_id: '',
    voyage_number: '',
    origin_port: '',
    destination_port: '',
    cargo_type: '',
    cargo_description: '',
    cargo_quantity: '',
    cargo_unit: '',
    estimated_arrival: '',
    estimated_departure: '',
    actual_arrival: '',
    actual_departure: '',
    berth_id: '',
    purpose: '',
    crew_count: '',
    passenger_count: '',
    last_port: '',
    next_port: '',
    status: 'scheduled'
});

const cargoTypes = [
    { value: 'container', label: 'حاويات' },
    { value: 'bulk_dry', label: 'بضائع سائبة جافة' },
    { value: 'bulk_liquid', label: 'بضائع سائبة سائلة' },
    { value: 'general', label: 'بضائع عامة' },
    { value: 'passengers', label: 'ركاب' },
    { value: 'vehicles', label: 'مركبات' },
    { value: 'livestock', label: 'مواشي' },
    { value: 'dangerous', label: 'بضائع خطرة' }
];

const cargoUnits = [
    { value: 'tons', label: 'طن' },
    { value: 'teu', label: 'حاوية مكافئة' },
    { value: 'cubic_meters', label: 'متر مكعب' },
    { value: 'pieces', label: 'قطعة' },
    { value: 'passengers', label: 'راكب' },
    { value: 'vehicles', label: 'مركبة' }
];

const purposes = [
    { value: 'loading', label: 'تحميل' },
    { value: 'unloading', label: 'تفريغ' },
    { value: 'loading_unloading', label: 'تحميل وتفريغ' },
    { value: 'transit', label: 'عبور' },
    { value: 'bunkering', label: 'تزويد بالوقود' },
    { value: 'maintenance', label: 'صيانة' },
    { value: 'emergency', label: 'طوارئ' }
];

const selectedShip = computed(() => {
    return props.ships.find(ship => ship.id == form.ship_id);
});

const availableBerths = computed(() => {
    if (!selectedShip.value) return props.berths;
    
    return props.berths.filter(berth => {
        // Filter berths based on ship size and type
        return berth.status === 'available' && 
               berth.max_length >= selectedShip.value.length &&
               berth.max_width >= selectedShip.value.width &&
               berth.max_draft >= selectedShip.value.draft;
    });
});

const submit = () => {
    form.post(route('voyages.store'));
};

// Generate voyage number automatically
const generateVoyageNumber = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    form.voyage_number = `V${year}${month}${day}${random}`;
};

// Auto-generate voyage number on component mount
if (!form.voyage_number) {
    generateVoyageNumber();
}
</script>

<template>
    <Head title="إضافة رحلة جديدة" />

    <AuthenticatedLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                إضافة رحلة جديدة
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <form @submit.prevent="submit" class="p-6 space-y-6">
                        <!-- Basic Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">المعلومات الأساسية</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        السفينة *
                                    </label>
                                    <select 
                                        v-model="form.ship_id"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.ship_id }"
                                    >
                                        <option value="">اختر السفينة</option>
                                        <option v-for="ship in ships" :key="ship.id" :value="ship.id">
                                            {{ ship.name }} ({{ ship.imo_number }})
                                        </option>
                                    </select>
                                    <div v-if="form.errors.ship_id" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.ship_id }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الوكيل البحري *
                                    </label>
                                    <select 
                                        v-model="form.agent_id"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.agent_id }"
                                    >
                                        <option value="">اختر الوكيل البحري</option>
                                        <option v-for="agent in agents" :key="agent.id" :value="agent.id">
                                            {{ agent.name }}
                                        </option>
                                    </select>
                                    <div v-if="form.errors.agent_id" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.agent_id }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        رقم الرحلة *
                                    </label>
                                    <div class="flex">
                                        <input 
                                            v-model="form.voyage_number"
                                            type="text" 
                                            required
                                            class="flex-1 rounded-l-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            :class="{ 'border-red-500': form.errors.voyage_number }"
                                        />
                                        <button 
                                            type="button"
                                            @click="generateVoyageNumber"
                                            class="px-3 py-2 bg-gray-200 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-300"
                                        >
                                            توليد
                                        </button>
                                    </div>
                                    <div v-if="form.errors.voyage_number" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.voyage_number }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الغرض من الزيارة *
                                    </label>
                                    <select 
                                        v-model="form.purpose"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.purpose }"
                                    >
                                        <option value="">اختر الغرض</option>
                                        <option v-for="purpose in purposes" :key="purpose.value" :value="purpose.value">
                                            {{ purpose.label }}
                                        </option>
                                    </select>
                                    <div v-if="form.errors.purpose" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.purpose }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Port Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات الموانئ</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        ميناء المنشأ *
                                    </label>
                                    <input 
                                        v-model="form.origin_port"
                                        type="text" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.origin_port }"
                                    />
                                    <div v-if="form.errors.origin_port" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.origin_port }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        ميناء الوجهة *
                                    </label>
                                    <input 
                                        v-model="form.destination_port"
                                        type="text" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.destination_port }"
                                    />
                                    <div v-if="form.errors.destination_port" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.destination_port }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الميناء السابق
                                    </label>
                                    <input 
                                        v-model="form.last_port"
                                        type="text" 
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.last_port }"
                                    />
                                    <div v-if="form.errors.last_port" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.last_port }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الميناء التالي
                                    </label>
                                    <input 
                                        v-model="form.next_port"
                                        type="text" 
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.next_port }"
                                    />
                                    <div v-if="form.errors.next_port" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.next_port }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Cargo Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات البضائع</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        نوع البضاعة *
                                    </label>
                                    <select 
                                        v-model="form.cargo_type"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.cargo_type }"
                                    >
                                        <option value="">اختر نوع البضاعة</option>
                                        <option v-for="type in cargoTypes" :key="type.value" :value="type.value">
                                            {{ type.label }}
                                        </option>
                                    </select>
                                    <div v-if="form.errors.cargo_type" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.cargo_type }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        وصف البضاعة
                                    </label>
                                    <input 
                                        v-model="form.cargo_description"
                                        type="text" 
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.cargo_description }"
                                    />
                                    <div v-if="form.errors.cargo_description" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.cargo_description }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        كمية البضاعة
                                    </label>
                                    <input 
                                        v-model="form.cargo_quantity"
                                        type="number" 
                                        step="0.01"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.cargo_quantity }"
                                    />
                                    <div v-if="form.errors.cargo_quantity" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.cargo_quantity }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        وحدة القياس
                                    </label>
                                    <select 
                                        v-model="form.cargo_unit"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.cargo_unit }"
                                    >
                                        <option value="">اختر وحدة القياس</option>
                                        <option v-for="unit in cargoUnits" :key="unit.value" :value="unit.value">
                                            {{ unit.label }}
                                        </option>
                                    </select>
                                    <div v-if="form.errors.cargo_unit" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.cargo_unit }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Schedule Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات الجدولة</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الوصول المتوقع *
                                    </label>
                                    <input 
                                        v-model="form.estimated_arrival"
                                        type="datetime-local" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.estimated_arrival }"
                                    />
                                    <div v-if="form.errors.estimated_arrival" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.estimated_arrival }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        المغادرة المتوقعة *
                                    </label>
                                    <input 
                                        v-model="form.estimated_departure"
                                        type="datetime-local" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.estimated_departure }"
                                    />
                                    <div v-if="form.errors.estimated_departure" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.estimated_departure }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الوصول الفعلي
                                    </label>
                                    <input 
                                        v-model="form.actual_arrival"
                                        type="datetime-local" 
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.actual_arrival }"
                                    />
                                    <div v-if="form.errors.actual_arrival" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.actual_arrival }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        المغادرة الفعلية
                                    </label>
                                    <input 
                                        v-model="form.actual_departure"
                                        type="datetime-local" 
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.actual_departure }"
                                    />
                                    <div v-if="form.errors.actual_departure" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.actual_departure }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Berth and Crew Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات الرصيف والطاقم</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الرصيف المطلوب
                                    </label>
                                    <select 
                                        v-model="form.berth_id"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.berth_id }"
                                    >
                                        <option value="">اختر الرصيف</option>
                                        <option v-for="berth in availableBerths" :key="berth.id" :value="berth.id">
                                            {{ berth.name }} ({{ berth.type }})
                                        </option>
                                    </select>
                                    <div v-if="form.errors.berth_id" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.berth_id }}
                                    </div>
                                    <div v-if="selectedShip && availableBerths.length === 0" class="text-yellow-600 text-sm mt-1">
                                        لا توجد أرصفة متاحة مناسبة لحجم هذه السفينة
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        عدد أفراد الطاقم
                                    </label>
                                    <input 
                                        v-model="form.crew_count"
                                        type="number" 
                                        min="0"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.crew_count }"
                                    />
                                    <div v-if="form.errors.crew_count" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.crew_count }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        عدد الركاب
                                    </label>
                                    <input 
                                        v-model="form.passenger_count"
                                        type="number" 
                                        min="0"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.passenger_count }"
                                    />
                                    <div v-if="form.errors.passenger_count" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.passenger_count }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Status -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                حالة الرحلة
                            </label>
                            <select 
                                v-model="form.status"
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                :class="{ 'border-red-500': form.errors.status }"
                            >
                                <option value="scheduled">مجدولة</option>
                                <option value="en_route">في الطريق</option>
                                <option value="arrived">وصلت</option>
                                <option value="in_port">في الميناء</option>
                                <option value="departed">غادرت</option>
                                <option value="cancelled">ملغية</option>
                                <option value="delayed">متأخرة</option>
                            </select>
                            <div v-if="form.errors.status" class="text-red-500 text-sm mt-1">
                                {{ form.errors.status }}
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                            <Link :href="route('voyages.index')" 
                                  class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                إلغاء
                            </Link>
                            <button 
                                type="submit" 
                                :disabled="form.processing"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                            >
                                <span v-if="form.processing">جاري الحفظ...</span>
                                <span v-else>حفظ</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
