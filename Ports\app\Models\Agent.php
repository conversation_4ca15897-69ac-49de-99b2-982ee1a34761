<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Agent extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'company_name',
        'company_name_en',
        'license_number',
        'tax_number',
        'commercial_register',
        'address',
        'address_en',
        'phone',
        'mobile',
        'fax',
        'email',
        'website',
        'contact_person',
        'contact_person_en',
        'contact_phone',
        'status',
        'license_expiry',
        'services',
        'metadata',
    ];

    protected $casts = [
        'license_expiry' => 'date',
        'services' => 'array',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'active',
    ];

    // العلاقات
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function voyages(): HasMany
    {
        return $this->hasMany(Voyage::class);
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    // النطاقات (Scopes)
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeExpiringSoon($query, $days = 30)
    {
        return $query->where('license_expiry', '<=', now()->addDays($days));
    }

    // الخصائص المحسوبة
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active';
    }

    public function getIsLicenseValidAttribute(): bool
    {
        return $this->license_expiry > now();
    }

    public function getFullCompanyNameAttribute(): string
    {
        return $this->company_name_en ?
            $this->company_name . ' / ' . $this->company_name_en :
            $this->company_name;
    }
}
