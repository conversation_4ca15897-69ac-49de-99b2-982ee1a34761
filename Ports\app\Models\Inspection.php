<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Inspection extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'voyage_id',
        'inspection_number',
        'type',
        'authority',
        'inspector_id',
        'scheduled_at',
        'started_at',
        'completed_at',
        'status',
        'result',
        'priority',
        'purpose',
        'scope',
        'checklist_items',
        'findings',
        'deficiencies',
        'corrective_actions',
        'follow_up_date',
        'recommendations',
        'documents_reviewed',
        'certificates_issued',
        'requires_follow_up',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'follow_up_date' => 'datetime',
        'checklist_items' => 'array',
        'findings' => 'array',
        'deficiencies' => 'array',
        'corrective_actions' => 'array',
        'documents_reviewed' => 'array',
        'certificates_issued' => 'array',
        'requires_follow_up' => 'boolean',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'scheduled',
        'priority' => 'normal',
        'requires_follow_up' => false,
    ];

    // العلاقات
    public function voyage(): BelongsTo
    {
        return $this->belongsTo(Voyage::class);
    }

    public function inspector(): BelongsTo
    {
        return $this->belongsTo(User::class, 'inspector_id');
    }

    public function items(): HasMany
    {
        return $this->hasMany(InspectionItem::class);
    }

    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    // النطاقات
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByResult($query, $result)
    {
        return $query->where('result', $result);
    }

    public function scopeScheduledToday($query)
    {
        return $query->whereDate('scheduled_at', today());
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'scheduled')
            ->where('scheduled_at', '<', now());
    }

    public function scopeRequiringFollowUp($query)
    {
        return $query->where('requires_follow_up', true)
            ->whereNotNull('follow_up_date');
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    // الخصائص المحسوبة
    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'completed';
    }

    public function getIsPassedAttribute(): bool
    {
        return $this->result === 'passed';
    }

    public function getIsFailedAttribute(): bool
    {
        return $this->result === 'failed';
    }

    public function getHasDeficienciesAttribute(): bool
    {
        return !empty($this->deficiencies) ||
               $this->items()->where('result', 'non_compliant')->exists();
    }

    public function getDurationAttribute(): ?int
    {
        if ($this->started_at && $this->completed_at) {
            return $this->started_at->diffInMinutes($this->completed_at);
        }
        return null;
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->status === 'scheduled' && $this->scheduled_at < now();
    }

    public function getComplianceRateAttribute(): float
    {
        $totalItems = $this->items()->count();
        if ($totalItems === 0) {
            return 100.0;
        }

        $compliantItems = $this->items()
            ->whereIn('result', ['compliant', 'not_applicable'])
            ->count();

        return ($compliantItems / $totalItems) * 100;
    }

    public function getCriticalDeficienciesCountAttribute(): int
    {
        return $this->items()
            ->where('result', 'non_compliant')
            ->where('severity', 'critical')
            ->count();
    }

    public function getMajorDeficienciesCountAttribute(): int
    {
        return $this->items()
            ->where('result', 'non_compliant')
            ->where('severity', 'major')
            ->count();
    }

    public function getMinorDeficienciesCountAttribute(): int
    {
        return $this->items()
            ->where('result', 'non_compliant')
            ->where('severity', 'minor')
            ->count();
    }

    // دوال مساعدة
    public function canStart(): bool
    {
        return $this->status === 'scheduled' && $this->scheduled_at <= now();
    }

    public function canComplete(): bool
    {
        return $this->status === 'in_progress';
    }

    public function canReschedule(): bool
    {
        return in_array($this->status, ['scheduled', 'in_progress']);
    }

    public function determineResult(): string
    {
        $criticalCount = $this->critical_deficiencies_count;
        $majorCount = $this->major_deficiencies_count;

        if ($criticalCount > 0) {
            return 'failed';
        }

        if ($majorCount > 3) {
            return 'failed';
        }

        if ($majorCount > 0 || $this->minor_deficiencies_count > 0) {
            return 'passed_with_observations';
        }

        return 'passed';
    }
}
