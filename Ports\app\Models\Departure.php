<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Departure extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'voyage_id',
        'arrival_id',
        'departure_number',
        'requested_etd',
        'confirmed_etd',
        'actual_departure',
        'unberthing_time',
        'pilot_disembark_time',
        'status',
        'departure_reason',
        'next_destination',
        'all_clearances_obtained',
        'all_fees_paid',
        'cargo_operations_completed',
        'requires_pilot',
        'requires_tugboat',
        'clearance_status',
        'outstanding_fees',
        'final_documents',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'requested_etd' => 'datetime',
        'confirmed_etd' => 'datetime',
        'actual_departure' => 'datetime',
        'unberthing_time' => 'datetime',
        'pilot_disembark_time' => 'datetime',
        'all_clearances_obtained' => 'boolean',
        'all_fees_paid' => 'boolean',
        'cargo_operations_completed' => 'boolean',
        'requires_pilot' => 'boolean',
        'requires_tugboat' => 'boolean',
        'clearance_status' => 'array',
        'outstanding_fees' => 'array',
        'final_documents' => 'array',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'requested',
        'all_clearances_obtained' => false,
        'all_fees_paid' => false,
        'cargo_operations_completed' => false,
        'requires_pilot' => true,
        'requires_tugboat' => false,
    ];

    // العلاقات
    public function voyage(): BelongsTo
    {
        return $this->belongsTo(Voyage::class);
    }

    public function arrival(): BelongsTo
    {
        return $this->belongsTo(Arrival::class);
    }

    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    // النطاقات
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopePending($query)
    {
        return $query->whereIn('status', ['requested', 'clearance_pending']);
    }

    public function scopeReadyToDepart($query)
    {
        return $query->where('status', 'cleared')
            ->where('all_clearances_obtained', true)
            ->where('all_fees_paid', true)
            ->where('cargo_operations_completed', true);
    }

    public function scopeDepartingToday($query)
    {
        return $query->whereDate('confirmed_etd', today())
            ->whereIn('status', ['cleared', 'unberthed']);
    }

    // الخصائص المحسوبة
    public function getIsDepartedAttribute(): bool
    {
        return $this->status === 'departed';
    }

    public function getIsReadyToDepartAttribute(): bool
    {
        return $this->all_clearances_obtained &&
               $this->all_fees_paid &&
               $this->cargo_operations_completed;
    }

    public function getDelayInHoursAttribute(): ?int
    {
        if ($this->actual_departure && $this->confirmed_etd) {
            return $this->confirmed_etd->diffInHours($this->actual_departure, false);
        }
        return null;
    }

    public function getIsDelayedAttribute(): bool
    {
        $delay = $this->delay_in_hours;
        return $delay !== null && $delay > 0;
    }

    public function getOutstandingRequirementsAttribute(): array
    {
        $requirements = [];

        if (!$this->all_clearances_obtained) {
            $requirements[] = 'clearances';
        }

        if (!$this->all_fees_paid) {
            $requirements[] = 'fees';
        }

        if (!$this->cargo_operations_completed) {
            $requirements[] = 'cargo_operations';
        }

        return $requirements;
    }

    public function getPortStayDurationAttribute(): ?int
    {
        if ($this->arrival && $this->arrival->actual_arrival && $this->actual_departure) {
            return $this->arrival->actual_arrival->diffInHours($this->actual_departure);
        }
        return null;
    }
}
