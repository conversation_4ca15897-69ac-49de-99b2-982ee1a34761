<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Agent;
use App\Models\Berth;
use App\Models\Fee;
use App\Models\Pilot;
use Illuminate\Support\Facades\Hash;

class PortDataSeeder extends Seeder
{
    public function run(): void
    {
        // إنشاء المستخدم الإداري الرئيسي
        $admin = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'مدير النظام',
            'password' => Hash::make('password'),
            'phone' => '+963-41-123456',
            'department' => 'إدارة النظام',
            'position' => 'مدير النظام',
            'is_active' => true,
            'timezone' => 'Asia/Damascus',
            'language' => 'ar',
        ]);
        $admin->assignRole('admin');

        // إنشاء مدير الميناء
        $portManager = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'أحمد محمد الأسد',
            'password' => Hash::make('password'),
            'phone' => '+963-41-234567',
            'department' => 'الإدارة العليا',
            'position' => 'مدير الميناء',
            'is_active' => true,
            'timezone' => 'Asia/Damascus',
            'language' => 'ar',
        ]);
        $portManager->assignRole('port_manager');

        // إنشاء مستخدمين لبرج المراقبة
        $controlTowerUser = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'محمد علي حسن',
            'password' => Hash::make('password'),
            'phone' => '+963-41-345678',
            'department' => 'برج المراقبة',
            'position' => 'مراقب حركة السفن',
            'is_active' => true,
            'timezone' => 'Asia/Damascus',
            'language' => 'ar',
        ]);
        $controlTowerUser->assignRole('control_tower');

        // إنشاء مستخدم مالي
        $financialUser = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'فاطمة أحمد الخوري',
            'password' => Hash::make('password'),
            'phone' => '+963-41-456789',
            'department' => 'الشؤون المالية',
            'position' => 'موظف مالي',
            'is_active' => true,
            'timezone' => 'Asia/Damascus',
            'language' => 'ar',
        ]);
        $financialUser->assignRole('financial_officer');

        // إنشاء الأرصفة
        $berths = [
            [
                'name' => 'الرصيف الأول - حاويات',
                'code' => 'B01',
                'terminal' => 'محطة الحاويات',
                'type' => 'container',
                'length' => 300.00,
                'width' => 40.00,
                'depth' => 15.00,
                'max_draft' => 14.00,
                'max_loa' => 280.00,
                'max_beam' => 35.00,
                'max_dwt' => 80000.00,
                'status' => 'available',
                'facilities' => ['crane', 'electricity', 'water', 'lighting'],
                'equipment' => ['container_crane', 'reach_stacker'],
                'hourly_rate' => 500.00,
                'daily_rate' => 10000.00,
                'coordinates' => ['lat' => 35.5138, 'lng' => 35.7833],
            ],
            [
                'name' => 'الرصيف الثاني - حاويات',
                'code' => 'B02',
                'terminal' => 'محطة الحاويات',
                'type' => 'container',
                'length' => 280.00,
                'width' => 40.00,
                'depth' => 14.00,
                'max_draft' => 13.50,
                'max_loa' => 260.00,
                'max_beam' => 32.00,
                'max_dwt' => 70000.00,
                'status' => 'available',
                'facilities' => ['crane', 'electricity', 'water', 'lighting'],
                'equipment' => ['container_crane', 'reach_stacker'],
                'hourly_rate' => 450.00,
                'daily_rate' => 9000.00,
                'coordinates' => ['lat' => 35.5140, 'lng' => 35.7835],
            ],
            [
                'name' => 'الرصيف الثالث - بضائع عامة',
                'code' => 'B03',
                'terminal' => 'محطة البضائع العامة',
                'type' => 'general',
                'length' => 200.00,
                'width' => 30.00,
                'depth' => 12.00,
                'max_draft' => 11.00,
                'max_loa' => 180.00,
                'max_beam' => 28.00,
                'max_dwt' => 40000.00,
                'status' => 'available',
                'facilities' => ['crane', 'electricity', 'water'],
                'equipment' => ['mobile_crane', 'forklift'],
                'hourly_rate' => 300.00,
                'daily_rate' => 6000.00,
                'coordinates' => ['lat' => 35.5142, 'lng' => 35.7837],
            ],
            [
                'name' => 'الرصيف الرابع - سوائل',
                'code' => 'B04',
                'terminal' => 'محطة السوائل',
                'type' => 'liquid',
                'length' => 250.00,
                'width' => 35.00,
                'depth' => 16.00,
                'max_draft' => 15.00,
                'max_loa' => 230.00,
                'max_beam' => 30.00,
                'max_dwt' => 60000.00,
                'status' => 'available',
                'facilities' => ['pipeline', 'electricity', 'water', 'fire_system'],
                'equipment' => ['loading_arms', 'pumps'],
                'hourly_rate' => 600.00,
                'daily_rate' => 12000.00,
                'coordinates' => ['lat' => 35.5144, 'lng' => 35.7839],
            ],
            [
                'name' => 'الرصيف الخامس - بضائع سائبة',
                'code' => 'B05',
                'terminal' => 'محطة البضائع السائبة',
                'type' => 'bulk',
                'length' => 220.00,
                'width' => 35.00,
                'depth' => 13.00,
                'max_draft' => 12.00,
                'max_loa' => 200.00,
                'max_beam' => 30.00,
                'max_dwt' => 50000.00,
                'status' => 'available',
                'facilities' => ['conveyor', 'electricity', 'water'],
                'equipment' => ['grab_crane', 'conveyor_belt'],
                'hourly_rate' => 400.00,
                'daily_rate' => 8000.00,
                'coordinates' => ['lat' => 35.5146, 'lng' => 35.7841],
            ],
        ];

        foreach ($berths as $berthData) {
            Berth::firstOrCreate(['code' => $berthData['code']], $berthData);
        }

        // إنشاء الرسوم الأساسية
        $fees = [
            [
                'code' => 'PORT_DUES',
                'name' => 'رسوم الميناء',
                'name_en' => 'Port Dues',
                'description' => 'رسوم أساسية لدخول الميناء',
                'category' => 'port',
                'calculation_method' => 'per_ton',
                'base_rate' => 50.00,
                'minimum_charge' => 1000.00,
                'currency' => 'SYP',
                'effective_from' => now()->startOfYear(),
                'is_active' => true,
            ],
            [
                'code' => 'BERTH_DUES',
                'name' => 'رسوم الرسو',
                'name_en' => 'Berth Dues',
                'description' => 'رسوم استخدام الرصيف',
                'category' => 'berth',
                'calculation_method' => 'per_hour',
                'base_rate' => 200.00,
                'minimum_charge' => 500.00,
                'currency' => 'SYP',
                'effective_from' => now()->startOfYear(),
                'is_active' => true,
            ],
            [
                'code' => 'PILOTAGE',
                'name' => 'رسوم الإرشاد',
                'name_en' => 'Pilotage Fees',
                'description' => 'رسوم خدمات الإرشاد البحري',
                'category' => 'pilotage',
                'calculation_method' => 'per_ton',
                'base_rate' => 25.00,
                'minimum_charge' => 2000.00,
                'currency' => 'SYP',
                'effective_from' => now()->startOfYear(),
                'is_active' => true,
            ],
            [
                'code' => 'TUGBOAT',
                'name' => 'رسوم القاطرة',
                'name_en' => 'Tugboat Fees',
                'description' => 'رسوم خدمات القاطرة',
                'category' => 'service',
                'calculation_method' => 'fixed',
                'base_rate' => 5000.00,
                'currency' => 'SYP',
                'effective_from' => now()->startOfYear(),
                'is_active' => true,
            ],
            [
                'code' => 'CARGO_HANDLING',
                'name' => 'رسوم مناولة البضائع',
                'name_en' => 'Cargo Handling Fees',
                'description' => 'رسوم مناولة وتفريغ البضائع',
                'category' => 'cargo',
                'calculation_method' => 'per_ton',
                'base_rate' => 100.00,
                'currency' => 'SYP',
                'effective_from' => now()->startOfYear(),
                'is_active' => true,
            ],
        ];

        foreach ($fees as $feeData) {
            Fee::firstOrCreate(['code' => $feeData['code']], $feeData);
        }

        // إنشاء وكيل ملاحي تجريبي
        $agentUser = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'شركة الوكالة الملاحية المتحدة',
            'password' => Hash::make('password'),
            'phone' => '+963-41-567890',
            'department' => 'الوكالة الملاحية',
            'position' => 'وكيل ملاحي',
            'is_active' => true,
            'timezone' => 'Asia/Damascus',
            'language' => 'ar',
        ]);
        $agentUser->assignRole('agent');

        Agent::firstOrCreate([
            'user_id' => $agentUser->id
        ], [
            'company_name' => 'شركة الوكالة الملاحية المتحدة',
            'company_name_en' => 'United Maritime Agency',
            'license_number' => 'MA-2024-001',
            'tax_number' => '*********',
            'commercial_register' => 'CR-2024-001',
            'address' => 'شارع الميناء، اللاذقية، سوريا',
            'address_en' => 'Port Street, Lattakia, Syria',
            'phone' => '+963-41-567890',
            'mobile' => '+963-**********',
            'email' => '<EMAIL>',
            'contact_person' => 'أحمد محمد العلي',
            'contact_person_en' => 'Ahmad Mohammad Al-Ali',
            'contact_phone' => '+963-**********',
            'status' => 'active',
            'license_expiry' => now()->addYear(),
            'services' => ['ship_agency', 'cargo_handling', 'customs_clearance'],
        ]);

        // إنشاء مرشد بحري تجريبي
        $pilotUser = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'الكابتن محمد أحمد السوري',
            'password' => Hash::make('password'),
            'phone' => '+963-41-678901',
            'department' => 'الإرشاد البحري',
            'position' => 'مرشد بحري أول',
            'is_active' => true,
            'timezone' => 'Asia/Damascus',
            'language' => 'ar',
        ]);
        $pilotUser->assignRole('pilot');

        Pilot::firstOrCreate([
            'user_id' => $pilotUser->id
        ], [
            'license_number' => 'PIL-2024-001',
            'full_name' => 'محمد أحمد السوري',
            'full_name_en' => 'Mohammad Ahmad Al-Souri',
            'date_of_birth' => '1975-05-15',
            'nationality' => 'سوري',
            'phone' => '+963-41-678901',
            'mobile' => '+963-988-234567',
            'email' => '<EMAIL>',
            'address' => 'حي الزراعة، اللاذقية، سوريا',
            'status' => 'active',
            'grade' => 'senior',
            'license_issue_date' => '2020-01-01',
            'license_expiry_date' => now()->addYears(5),
            'certifications' => ['basic_pilot', 'container_ships', 'tankers'],
            'ship_types_authorized' => ['container', 'bulk', 'general', 'liquid'],
            'max_ship_length' => 300.00,
            'max_ship_draft' => 15.00,
            'years_experience' => 15,
            'languages' => ['ar', 'en'],
            'night_operations' => true,
            'emergency_operations' => true,
            'hourly_rate' => 1000.00,
            'overtime_rate' => 1500.00,
        ]);
    }
}
