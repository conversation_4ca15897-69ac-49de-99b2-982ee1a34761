<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\AgentController;
use App\Http\Controllers\ShipController;
use App\Http\Controllers\VoyageController;
use App\Http\Controllers\BerthController;
use App\Http\Controllers\ControlTowerController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

Route::get('/', function () {
    if (Auth::check()) {
        return redirect()->route('dashboard');
    }
    return Inertia::render('Welcome', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
        'laravelVersion' => Application::VERSION,
        'phpVersion' => PHP_VERSION,
    ]);
});

// Dashboard routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/api/port-traffic', [DashboardController::class, 'getPortTraffic'])->name('api.port-traffic');
    Route::get('/api/port-status', [DashboardController::class, 'getPortStatus'])->name('api.port-status');
});

// Profile routes
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Agent routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::resource('agents', AgentController::class);
    Route::patch('agents/{agent}/status', [AgentController::class, 'updateStatus'])->name('agents.update-status');
    Route::get('agents/{agent}/dashboard', [AgentController::class, 'dashboard'])->name('agents.dashboard');
    Route::post('agents/bulk-action', [AgentController::class, 'bulkAction'])->name('agents.bulk-action');
    Route::get('api/agents/select', [AgentController::class, 'getForSelect'])->name('api.agents.select');
    Route::get('agents/export', [AgentController::class, 'export'])->name('agents.export');
});

// Ship routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::resource('ships', ShipController::class);
    Route::patch('ships/{ship}/status', [ShipController::class, 'updateStatus'])->name('ships.update-status');
    Route::get('ships/currently-in-port', [ShipController::class, 'getCurrentlyInPort'])->name('ships.currently-in-port');
    Route::post('ships/bulk-action', [ShipController::class, 'bulkAction'])->name('ships.bulk-action');
    Route::get('api/ships/select', [ShipController::class, 'getForSelect'])->name('api.ships.select');
    Route::get('api/ships/expected-today', [ShipController::class, 'getExpectedToday'])->name('api.ships.expected-today');
    Route::get('api/ships/departing-today', [ShipController::class, 'getDepartingToday'])->name('api.ships.departing-today');
    Route::get('api/ships/compatible-with-berth', [ShipController::class, 'getCompatibleWithBerth'])->name('api.ships.compatible-with-berth');
    Route::get('ships/export', [ShipController::class, 'export'])->name('ships.export');
    Route::post('ships/import', [ShipController::class, 'import'])->name('ships.import');
});

// Voyage routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::resource('voyages', VoyageController::class);
    Route::patch('voyages/{voyage}/status', [VoyageController::class, 'updateStatus'])->name('voyages.update-status');
    Route::get('voyages/dashboard', [VoyageController::class, 'dashboard'])->name('voyages.dashboard');
    Route::get('voyages/overdue', [VoyageController::class, 'getOverdue'])->name('voyages.overdue');
    Route::get('voyages/requiring-attention', [VoyageController::class, 'getRequiringAttention'])->name('voyages.requiring-attention');
    Route::post('voyages/{voyage}/schedule-operations', [VoyageController::class, 'scheduleOperations'])->name('voyages.schedule-operations');
    Route::get('api/voyages/arriving-today', [VoyageController::class, 'getArrivingToday'])->name('api.voyages.arriving-today');
    Route::get('api/voyages/departing-today', [VoyageController::class, 'getDepartingToday'])->name('api.voyages.departing-today');
    Route::get('api/voyages/port-traffic', [VoyageController::class, 'getPortTraffic'])->name('api.voyages.port-traffic');
    Route::get('voyages/export', [VoyageController::class, 'export'])->name('voyages.export');
});

// Berth routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::resource('berths', BerthController::class);
    Route::patch('berths/{berth}/status', [BerthController::class, 'updateStatus'])->name('berths.update-status');
    Route::post('berths/{berth}/booking', [BerthController::class, 'createBooking'])->name('berths.create-booking');
    Route::get('berths/{berth}/schedule', [BerthController::class, 'getSchedule'])->name('berths.schedule');
    Route::get('api/berths/available-for-ship', [BerthController::class, 'getAvailableForShip'])->name('api.berths.available-for-ship');
    Route::get('berths/utilization-report', [BerthController::class, 'getUtilizationReport'])->name('berths.utilization-report');

    // Control Tower routes
    Route::get('control-tower', [ControlTowerController::class, 'index'])->name('control-tower.index');
    Route::get('control-tower/vessels/positions', [ControlTowerController::class, 'getVesselPositions'])->name('control-tower.vessel-positions');
    Route::put('control-tower/vessels/{voyage}/position', [ControlTowerController::class, 'updateVesselPosition'])->name('control-tower.update-vessel-position');
    Route::get('control-tower/berths/status', [ControlTowerController::class, 'getBerthStatus'])->name('control-tower.berth-status');
    Route::get('control-tower/weather', [ControlTowerController::class, 'getWeatherUpdate'])->name('control-tower.weather');
    Route::get('control-tower/alerts', [ControlTowerController::class, 'getTrafficAlerts'])->name('control-tower.alerts');
    Route::post('control-tower/alerts', [ControlTowerController::class, 'createAlert'])->name('control-tower.create-alert');
    Route::get('control-tower/statistics', [ControlTowerController::class, 'getPortStatistics'])->name('control-tower.statistics');
    Route::get('control-tower/vessels/{voyage}/history', [ControlTowerController::class, 'getVesselHistory'])->name('control-tower.vessel-history');
    Route::post('control-tower/export', [ControlTowerController::class, 'exportData'])->name('control-tower.export');
});

require __DIR__.'/auth.php';
