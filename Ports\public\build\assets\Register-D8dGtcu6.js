import{u as p,s as c,o as w,w as n,a,d as r,b as e,h as _,e as g,q as V,l as d,n as v}from"./app-CdA4UbU6.js";import{_ as b}from"./GuestLayout-CuRaoidN.js";import{_ as t,a as m,b as i}from"./TextInput-BBww1TQI.js";import{P as y}from"./PrimaryButton-BzUTxj76.js";import"./ApplicationLogo-WdDnJ3OK.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const x={class:"mt-4"},k={class:"mt-4"},q={class:"mt-4"},B={class:"mt-4 flex items-center justify-end"},h={__name:"Register",setup(N){const s=p({name:"",email:"",password:"",password_confirmation:""}),u=()=>{s.post(route("register"),{onFinish:()=>s.reset("password","password_confirmation")})};return(f,o)=>(w(),c(b,null,{default:n(()=>[a(e(_),{title:"Register"}),r("form",{onSubmit:g(u,["prevent"])},[r("div",null,[a(t,{for:"name",value:"Name"}),a(m,{id:"name",type:"text",class:"mt-1 block w-full",modelValue:e(s).name,"onUpdate:modelValue":o[0]||(o[0]=l=>e(s).name=l),required:"",autofocus:"",autocomplete:"name"},null,8,["modelValue"]),a(i,{class:"mt-2",message:e(s).errors.name},null,8,["message"])]),r("div",x,[a(t,{for:"email",value:"Email"}),a(m,{id:"email",type:"email",class:"mt-1 block w-full",modelValue:e(s).email,"onUpdate:modelValue":o[1]||(o[1]=l=>e(s).email=l),required:"",autocomplete:"username"},null,8,["modelValue"]),a(i,{class:"mt-2",message:e(s).errors.email},null,8,["message"])]),r("div",k,[a(t,{for:"password",value:"Password"}),a(m,{id:"password",type:"password",class:"mt-1 block w-full",modelValue:e(s).password,"onUpdate:modelValue":o[2]||(o[2]=l=>e(s).password=l),required:"",autocomplete:"new-password"},null,8,["modelValue"]),a(i,{class:"mt-2",message:e(s).errors.password},null,8,["message"])]),r("div",q,[a(t,{for:"password_confirmation",value:"Confirm Password"}),a(m,{id:"password_confirmation",type:"password",class:"mt-1 block w-full",modelValue:e(s).password_confirmation,"onUpdate:modelValue":o[3]||(o[3]=l=>e(s).password_confirmation=l),required:"",autocomplete:"new-password"},null,8,["modelValue"]),a(i,{class:"mt-2",message:e(s).errors.password_confirmation},null,8,["message"])]),r("div",B,[a(e(V),{href:f.route("login"),class:"rounded-md text-sm text-gray-600 underline hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"},{default:n(()=>o[4]||(o[4]=[d(" Already registered? ",-1)])),_:1,__:[4]},8,["href"]),a(y,{class:v(["ms-4",{"opacity-25":e(s).processing}]),disabled:e(s).processing},{default:n(()=>o[5]||(o[5]=[d(" Register ",-1)])),_:1,__:[5]},8,["class","disabled"])])],32)]),_:1}))}};export{h as default};
