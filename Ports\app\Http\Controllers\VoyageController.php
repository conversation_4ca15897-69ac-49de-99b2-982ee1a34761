<?php

namespace App\Http\Controllers;

use App\Models\Voyage;
use App\Services\VoyageService;
use App\Services\AgentService;
use App\Services\ShipService;
use App\Services\BerthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class VoyageController extends Controller
{
    public function __construct(
        private VoyageService $voyageService,
        private AgentService $agentService,
        private ShipService $shipService,
        private BerthService $berthService
    ) {}

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 15);
        $search = $request->get('search');
        $status = $request->get('status');
        $type = $request->get('type');
        $agentId = $request->get('agent_id');

        $voyages = $this->voyageService->getAllVoyages($perPage);

        // Filter by agent if user is an agent
        /** @var \App\Models\User $user */
        $user = Auth::user();
        if ($user && $user->hasRole('agent') && optional($user)->agent) {
            $voyages = $this->voyageService->getVoyagesForAgent($user->agent->id);
        }

        $statistics = $this->voyageService->getStatistics();

        return Inertia::render('Voyages/Index', [
            'voyages' => $voyages,
            'statistics' => $statistics,
            'filters' => [
                'search' => $search,
                'status' => $status,
                'type' => $type,
                'agent_id' => $agentId,
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $agents = $this->agentService->getAgentsForSelect();
        $ships = $this->shipService->getShipsForSelect();
        $berths = $this->berthService->getAvailableBerths();

        return Inertia::render('Voyages/Create', [
            'agents' => $agents,
            'ships' => $ships,
            'berths' => $berths,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $voyage = $this->voyageService->createVoyage($request->all());

            return redirect()->route('voyages.show', $voyage->id)
                ->with('success', 'تم إنشاء الرحلة بنجاح');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Voyage $voyage)
    {
        $voyage = $this->voyageService->findVoyage($voyage->id);

        if (!$voyage) {
            return redirect()->route('voyages.index')
                ->with('error', 'الرحلة غير موجودة');
        }

        // Check if user can access this voyage
        /** @var \App\Models\User $user */
        $user = Auth::user();
        if ($user && $user->hasRole('agent') && optional($user)->agent && $voyage->agent_id !== $user->agent->id) {
            abort(403, 'غير مصرح لك بالوصول إلى هذه الرحلة');
        }

        $timeline = $this->voyageService->getVoyageTimeline($voyage->id);
        $performanceMetrics = $this->voyageService->getPerformanceMetrics($voyage->id);

        return Inertia::render('Voyages/Show', [
            'voyage' => $voyage,
            'timeline' => $timeline,
            'performance_metrics' => $performanceMetrics,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Voyage $voyage)
    {
        // Check if user can edit this voyage
        /** @var \App\Models\User $user */
        $user = Auth::user();
        if ($user && $user->hasRole('agent') && optional($user)->agent && $voyage->agent_id !== $user->agent->id) {
            abort(403, 'غير مصرح لك بتعديل هذه الرحلة');
        }

        $agents = $this->agentService->getAgentsForSelect();
        $ships = $this->shipService->getShipsForSelect();
        $berths = $this->berthService->getBerthsForSelect();

        return Inertia::render('Voyages/Edit', [
            'voyage' => $voyage,
            'agents' => $agents,
            'ships' => $ships,
            'berths' => $berths,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Voyage $voyage)
    {
        try {
            $this->voyageService->updateVoyage($voyage->id, $request->all());

            return redirect()->route('voyages.show', $voyage->id)
                ->with('success', 'تم تحديث بيانات الرحلة بنجاح');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Voyage $voyage)
    {
        try {
            $this->voyageService->deleteVoyage($voyage->id);

            return redirect()->route('voyages.index')
                ->with('success', 'تم حذف الرحلة بنجاح');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Update voyage status
     */
    public function updateStatus(Request $request, Voyage $voyage)
    {
        $request->validate([
            'status' => 'required|in:planned,arrived,berthed,working,completed,departed,cancelled'
        ]);

        try {
            $this->voyageService->updateVoyageStatus($voyage->id, $request->status);

            return back()->with('success', 'تم تحديث حالة الرحلة بنجاح');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Get voyages arriving today
     */
    public function getArrivingToday()
    {
        $voyages = $this->voyageService->getArrivingToday();

        return response()->json($voyages);
    }

    /**
     * Get voyages departing today
     */
    public function getDepartingToday()
    {
        $voyages = $this->voyageService->getDepartingToday();

        return response()->json($voyages);
    }

    /**
     * Get overdue voyages
     */
    public function getOverdue()
    {
        $voyages = $this->voyageService->getOverdueVoyages();

        return Inertia::render('Voyages/Overdue', [
            'voyages' => $voyages,
        ]);
    }

    /**
     * Get voyages requiring attention
     */
    public function getRequiringAttention()
    {
        $voyages = $this->voyageService->getVoyagesRequiringAttention();

        return Inertia::render('Voyages/RequiringAttention', [
            'voyages' => $voyages,
        ]);
    }

    /**
     * Get port traffic summary
     */
    public function getPortTraffic(Request $request)
    {
        $date = $request->get('date') ? new \DateTime($request->get('date')) : null;
        $summary = $this->voyageService->getPortTrafficSummary($date);

        return response()->json($summary);
    }

    /**
     * Schedule operations for voyage
     */
    public function scheduleOperations(Request $request, Voyage $voyage)
    {
        $request->validate([
            'operations' => 'required|array',
            'operations.*.type' => 'required|string',
            'operations.*.description' => 'required|string',
            'operations.*.planned_start' => 'required|date',
            'operations.*.planned_end' => 'required|date|after:operations.*.planned_start',
        ]);

        try {
            $this->voyageService->scheduleOperations($voyage->id, $request->operations);

            return back()->with('success', 'تم جدولة العمليات بنجاح');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Export voyages data
     */
    public function export(Request $request)
    {
        $filters = $request->only(['agent_id', 'ship_id', 'voyage_type', 'cargo_type', 'status', 'date_from', 'date_to']);
        $report = $this->voyageService->generateReport($filters);

        return response()->json($report);
    }

    /**
     * Get voyage dashboard data
     */
    public function dashboard()
    {
        $dashboardData = $this->voyageService->getDashboardData();

        return Inertia::render('Voyages/Dashboard', $dashboardData);
    }
}
