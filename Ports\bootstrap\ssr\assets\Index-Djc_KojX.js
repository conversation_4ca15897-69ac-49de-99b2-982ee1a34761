import { ref, unref, withCtx, createTextVNode, createVNode, createBlock, openBlock, toDisplayString, withDirectives, withK<PERSON>s, vModelText, vModelSelect, createCommentVNode, Fragment, renderList, useSSRContext } from "vue";
import { ssrRenderComponent, ssrInterpolate, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual, ssrRenderList, ssrRenderClass } from "vue/server-renderer";
import { _ as _sfc_main$1 } from "./AuthenticatedLayout-DMuuhipg.js";
import { Head, Link, router } from "@inertiajs/vue3";
import "./ApplicationLogo-B2173abF.js";
import "./_plugin-vue_export-helper-1tPrXgE0.js";
const _sfc_main = {
  __name: "Index",
  __ssrInlineRender: true,
  props: {
    voyages: Object,
    statistics: Object,
    filters: Object
  },
  setup(__props) {
    const props = __props;
    const search = ref(props.filters.search || "");
    const status = ref(props.filters.status || "");
    const type = ref(props.filters.type || "");
    const agentId = ref(props.filters.agent_id || "");
    const performSearch = () => {
      router.get(route("voyages.index"), {
        search: search.value,
        status: status.value,
        type: type.value,
        agent_id: agentId.value
      }, {
        preserveState: true,
        replace: true
      });
    };
    const getStatusColor = (status2) => {
      const colors = {
        "planned": "bg-blue-100 text-blue-800",
        "arrived": "bg-yellow-100 text-yellow-800",
        "berthed": "bg-green-100 text-green-800",
        "working": "bg-purple-100 text-purple-800",
        "completed": "bg-gray-100 text-gray-800",
        "departed": "bg-indigo-100 text-indigo-800",
        "cancelled": "bg-red-100 text-red-800"
      };
      return colors[status2] || "bg-gray-100 text-gray-800";
    };
    const getStatusText = (status2) => {
      const texts = {
        "planned": "مخططة",
        "arrived": "وصلت",
        "berthed": "راسية",
        "working": "تعمل",
        "completed": "مكتملة",
        "departed": "غادرت",
        "cancelled": "ملغية"
      };
      return texts[status2] || status2;
    };
    const getVoyageTypeText = (type2) => {
      const types = {
        "arrival": "وصول",
        "departure": "مغادرة",
        "transit": "عبور"
      };
      return types[type2] || type2;
    };
    const getCargoTypeText = (type2) => {
      const types = {
        "container": "حاويات",
        "bulk": "بضائع سائبة",
        "general": "بضائع عامة",
        "liquid": "سوائل",
        "passenger": "ركاب",
        "empty": "فارغة"
      };
      return types[type2] || type2;
    };
    const formatDate = (date) => {
      return new Date(date).toLocaleDateString("ar-SA", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit"
      });
    };
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<!--[-->`);
      _push(ssrRenderComponent(unref(Head), { title: "الرحلات" }, null, _parent));
      _push(ssrRenderComponent(_sfc_main$1, null, {
        header: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="flex justify-between items-center"${_scopeId}><h2 class="font-semibold text-xl text-gray-800 leading-tight"${_scopeId}> الرحلات </h2>`);
            _push2(ssrRenderComponent(unref(Link), {
              href: _ctx.route("voyages.create"),
              class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(` إضافة رحلة جديدة `);
                } else {
                  return [
                    createTextVNode(" إضافة رحلة جديدة ")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`</div>`);
          } else {
            return [
              createVNode("div", { class: "flex justify-between items-center" }, [
                createVNode("h2", { class: "font-semibold text-xl text-gray-800 leading-tight" }, " الرحلات "),
                createVNode(unref(Link), {
                  href: _ctx.route("voyages.create"),
                  class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                }, {
                  default: withCtx(() => [
                    createTextVNode(" إضافة رحلة جديدة ")
                  ]),
                  _: 1
                }, 8, ["href"])
              ])
            ];
          }
        }),
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="py-12"${_scopeId}><div class="max-w-7xl mx-auto sm:px-6 lg:px-8"${_scopeId}><div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"${_scopeId}><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"${_scopeId}><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"${_scopeId}></path></svg></div></div><div class="ml-4"${_scopeId}><p class="text-sm font-medium text-gray-500"${_scopeId}>إجمالي الرحلات</p><p class="text-2xl font-semibold text-gray-900"${_scopeId}>${ssrInterpolate(__props.statistics.total)}</p></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"${_scopeId}><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"${_scopeId}></path></svg></div></div><div class="ml-4"${_scopeId}><p class="text-sm font-medium text-gray-500"${_scopeId}>نشطة</p><p class="text-2xl font-semibold text-gray-900"${_scopeId}>${ssrInterpolate(__props.statistics.active)}</p></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center"${_scopeId}><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"${_scopeId}></path></svg></div></div><div class="ml-4"${_scopeId}><p class="text-sm font-medium text-gray-500"${_scopeId}>مكتملة</p><p class="text-2xl font-semibold text-gray-900"${_scopeId}>${ssrInterpolate(__props.statistics.completed)}</p></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center"${_scopeId}><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"${_scopeId}></path><path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"${_scopeId}></path></svg></div></div><div class="ml-4"${_scopeId}><p class="text-sm font-medium text-gray-500"${_scopeId}>متوسط الإقامة</p><p class="text-2xl font-semibold text-gray-900"${_scopeId}>${ssrInterpolate(__props.statistics.average_stay_time_hours)} ساعة</p></div></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"${_scopeId}><div class="p-6"${_scopeId}><div class="grid grid-cols-1 md:grid-cols-5 gap-4"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}>البحث</label><input${ssrRenderAttr("value", search.value)} type="text" placeholder="البحث برقم الرحلة أو اسم السفينة..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"${_scopeId}></div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}>الحالة</label><select class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"${_scopeId}><option value=""${ssrIncludeBooleanAttr(Array.isArray(status.value) ? ssrLooseContain(status.value, "") : ssrLooseEqual(status.value, "")) ? " selected" : ""}${_scopeId}>جميع الحالات</option><option value="planned"${ssrIncludeBooleanAttr(Array.isArray(status.value) ? ssrLooseContain(status.value, "planned") : ssrLooseEqual(status.value, "planned")) ? " selected" : ""}${_scopeId}>مخططة</option><option value="arrived"${ssrIncludeBooleanAttr(Array.isArray(status.value) ? ssrLooseContain(status.value, "arrived") : ssrLooseEqual(status.value, "arrived")) ? " selected" : ""}${_scopeId}>وصلت</option><option value="berthed"${ssrIncludeBooleanAttr(Array.isArray(status.value) ? ssrLooseContain(status.value, "berthed") : ssrLooseEqual(status.value, "berthed")) ? " selected" : ""}${_scopeId}>راسية</option><option value="working"${ssrIncludeBooleanAttr(Array.isArray(status.value) ? ssrLooseContain(status.value, "working") : ssrLooseEqual(status.value, "working")) ? " selected" : ""}${_scopeId}>تعمل</option><option value="completed"${ssrIncludeBooleanAttr(Array.isArray(status.value) ? ssrLooseContain(status.value, "completed") : ssrLooseEqual(status.value, "completed")) ? " selected" : ""}${_scopeId}>مكتملة</option><option value="departed"${ssrIncludeBooleanAttr(Array.isArray(status.value) ? ssrLooseContain(status.value, "departed") : ssrLooseEqual(status.value, "departed")) ? " selected" : ""}${_scopeId}>غادرت</option><option value="cancelled"${ssrIncludeBooleanAttr(Array.isArray(status.value) ? ssrLooseContain(status.value, "cancelled") : ssrLooseEqual(status.value, "cancelled")) ? " selected" : ""}${_scopeId}>ملغية</option></select></div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}>نوع الرحلة</label><select class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"${_scopeId}><option value=""${ssrIncludeBooleanAttr(Array.isArray(type.value) ? ssrLooseContain(type.value, "") : ssrLooseEqual(type.value, "")) ? " selected" : ""}${_scopeId}>جميع الأنواع</option><option value="arrival"${ssrIncludeBooleanAttr(Array.isArray(type.value) ? ssrLooseContain(type.value, "arrival") : ssrLooseEqual(type.value, "arrival")) ? " selected" : ""}${_scopeId}>وصول</option><option value="departure"${ssrIncludeBooleanAttr(Array.isArray(type.value) ? ssrLooseContain(type.value, "departure") : ssrLooseEqual(type.value, "departure")) ? " selected" : ""}${_scopeId}>مغادرة</option><option value="transit"${ssrIncludeBooleanAttr(Array.isArray(type.value) ? ssrLooseContain(type.value, "transit") : ssrLooseEqual(type.value, "transit")) ? " selected" : ""}${_scopeId}>عبور</option></select></div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}>الوكيل</label><input${ssrRenderAttr("value", agentId.value)} type="text" placeholder="معرف الوكيل..." class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"${_scopeId}></div><div class="flex items-end"${_scopeId}><button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full"${_scopeId}> بحث </button></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="overflow-x-auto"${_scopeId}><table class="min-w-full divide-y divide-gray-200"${_scopeId}><thead class="bg-gray-50"${_scopeId}><tr${_scopeId}><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> رقم الرحلة </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> السفينة </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> الوكيل </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> النوع </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> نوع البضاعة </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> الوصول المتوقع </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> الحالة </th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"${_scopeId}> الإجراءات </th></tr></thead><tbody class="bg-white divide-y divide-gray-200"${_scopeId}><!--[-->`);
            ssrRenderList(__props.voyages.data, (voyage) => {
              _push2(`<tr class="hover:bg-gray-50"${_scopeId}><td class="px-6 py-4 whitespace-nowrap"${_scopeId}><div class="text-sm font-medium text-gray-900"${_scopeId}>${ssrInterpolate(voyage.voyage_number)}</div><div class="text-sm text-gray-500"${_scopeId}>${ssrInterpolate(voyage.origin_port)} → ${ssrInterpolate(voyage.destination_port)}</div></td><td class="px-6 py-4 whitespace-nowrap"${_scopeId}><div class="text-sm font-medium text-gray-900"${_scopeId}>${ssrInterpolate(voyage.ship?.name)}</div><div class="text-sm text-gray-500"${_scopeId}>IMO: ${ssrInterpolate(voyage.ship?.imo_number)}</div></td><td class="px-6 py-4 whitespace-nowrap"${_scopeId}><div class="text-sm text-gray-900"${_scopeId}>${ssrInterpolate(voyage.agent?.company_name)}</div></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"${_scopeId}>${ssrInterpolate(getVoyageTypeText(voyage.voyage_type))}</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"${_scopeId}>${ssrInterpolate(getCargoTypeText(voyage.cargo_type))}</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"${_scopeId}>${ssrInterpolate(formatDate(voyage.eta))}</td><td class="px-6 py-4 whitespace-nowrap"${_scopeId}><span class="${ssrRenderClass([getStatusColor(voyage.status), "inline-flex px-2 py-1 text-xs font-semibold rounded-full"])}"${_scopeId}>${ssrInterpolate(getStatusText(voyage.status))}</span></td><td class="px-6 py-4 whitespace-nowrap text-sm font-medium"${_scopeId}><div class="flex space-x-2"${_scopeId}>`);
              _push2(ssrRenderComponent(unref(Link), {
                href: _ctx.route("voyages.show", voyage.id),
                class: "text-indigo-600 hover:text-indigo-900"
              }, {
                default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(` عرض `);
                  } else {
                    return [
                      createTextVNode(" عرض ")
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
              _push2(ssrRenderComponent(unref(Link), {
                href: _ctx.route("voyages.edit", voyage.id),
                class: "text-green-600 hover:text-green-900"
              }, {
                default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                  if (_push3) {
                    _push3(` تعديل `);
                  } else {
                    return [
                      createTextVNode(" تعديل ")
                    ];
                  }
                }),
                _: 2
              }, _parent2, _scopeId));
              _push2(`</div></td></tr>`);
            });
            _push2(`<!--]--></tbody></table></div>`);
            if (__props.voyages.links) {
              _push2(`<div class="px-6 py-3 border-t border-gray-200"${_scopeId}><div class="flex justify-between items-center"${_scopeId}><div class="text-sm text-gray-700"${_scopeId}> عرض ${ssrInterpolate(__props.voyages.from)} إلى ${ssrInterpolate(__props.voyages.to)} من ${ssrInterpolate(__props.voyages.total)} نتيجة </div><div class="flex space-x-1"${_scopeId}><!--[-->`);
              ssrRenderList(__props.voyages.links, (link) => {
                _push2(`<!--[-->`);
                if (link.url) {
                  _push2(ssrRenderComponent(unref(Link), {
                    href: link.url,
                    class: [
                      "px-3 py-2 text-sm rounded-md",
                      link.active ? "bg-blue-500 text-white" : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-300"
                    ]
                  }, null, _parent2, _scopeId));
                } else {
                  _push2(`<span class="${ssrRenderClass([
                    "px-3 py-2 text-sm rounded-md",
                    "bg-gray-100 text-gray-400 cursor-not-allowed"
                  ])}"${_scopeId}>${link.label ?? ""}</span>`);
                }
                _push2(`<!--]-->`);
              });
              _push2(`<!--]--></div></div></div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div></div>`);
          } else {
            return [
              createVNode("div", { class: "py-12" }, [
                createVNode("div", { class: "max-w-7xl mx-auto sm:px-6 lg:px-8" }, [
                  createVNode("div", { class: "grid grid-cols-1 md:grid-cols-4 gap-6 mb-8" }, [
                    createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                      createVNode("div", { class: "p-6" }, [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", { class: "flex-shrink-0" }, [
                            createVNode("div", { class: "w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center" }, [
                              (openBlock(), createBlock("svg", {
                                class: "w-4 h-4 text-white",
                                fill: "currentColor",
                                viewBox: "0 0 20 20"
                              }, [
                                createVNode("path", { d: "M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" })
                              ]))
                            ])
                          ]),
                          createVNode("div", { class: "ml-4" }, [
                            createVNode("p", { class: "text-sm font-medium text-gray-500" }, "إجمالي الرحلات"),
                            createVNode("p", { class: "text-2xl font-semibold text-gray-900" }, toDisplayString(__props.statistics.total), 1)
                          ])
                        ])
                      ])
                    ]),
                    createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                      createVNode("div", { class: "p-6" }, [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", { class: "flex-shrink-0" }, [
                            createVNode("div", { class: "w-8 h-8 bg-green-500 rounded-full flex items-center justify-center" }, [
                              (openBlock(), createBlock("svg", {
                                class: "w-4 h-4 text-white",
                                fill: "currentColor",
                                viewBox: "0 0 20 20"
                              }, [
                                createVNode("path", {
                                  "fill-rule": "evenodd",
                                  d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
                                  "clip-rule": "evenodd"
                                })
                              ]))
                            ])
                          ]),
                          createVNode("div", { class: "ml-4" }, [
                            createVNode("p", { class: "text-sm font-medium text-gray-500" }, "نشطة"),
                            createVNode("p", { class: "text-2xl font-semibold text-gray-900" }, toDisplayString(__props.statistics.active), 1)
                          ])
                        ])
                      ])
                    ]),
                    createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                      createVNode("div", { class: "p-6" }, [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", { class: "flex-shrink-0" }, [
                            createVNode("div", { class: "w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center" }, [
                              (openBlock(), createBlock("svg", {
                                class: "w-4 h-4 text-white",
                                fill: "currentColor",
                                viewBox: "0 0 20 20"
                              }, [
                                createVNode("path", {
                                  "fill-rule": "evenodd",
                                  d: "M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",
                                  "clip-rule": "evenodd"
                                })
                              ]))
                            ])
                          ]),
                          createVNode("div", { class: "ml-4" }, [
                            createVNode("p", { class: "text-sm font-medium text-gray-500" }, "مكتملة"),
                            createVNode("p", { class: "text-2xl font-semibold text-gray-900" }, toDisplayString(__props.statistics.completed), 1)
                          ])
                        ])
                      ])
                    ]),
                    createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                      createVNode("div", { class: "p-6" }, [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", { class: "flex-shrink-0" }, [
                            createVNode("div", { class: "w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center" }, [
                              (openBlock(), createBlock("svg", {
                                class: "w-4 h-4 text-white",
                                fill: "currentColor",
                                viewBox: "0 0 20 20"
                              }, [
                                createVNode("path", { d: "M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z" }),
                                createVNode("path", { d: "M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z" })
                              ]))
                            ])
                          ]),
                          createVNode("div", { class: "ml-4" }, [
                            createVNode("p", { class: "text-sm font-medium text-gray-500" }, "متوسط الإقامة"),
                            createVNode("p", { class: "text-2xl font-semibold text-gray-900" }, toDisplayString(__props.statistics.average_stay_time_hours) + " ساعة", 1)
                          ])
                        ])
                      ])
                    ])
                  ]),
                  createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6" }, [
                    createVNode("div", { class: "p-6" }, [
                      createVNode("div", { class: "grid grid-cols-1 md:grid-cols-5 gap-4" }, [
                        createVNode("div", null, [
                          createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, "البحث"),
                          withDirectives(createVNode("input", {
                            "onUpdate:modelValue": ($event) => search.value = $event,
                            onKeyup: withKeys(performSearch, ["enter"]),
                            type: "text",
                            placeholder: "البحث برقم الرحلة أو اسم السفينة...",
                            class: "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                          }, null, 40, ["onUpdate:modelValue"]), [
                            [vModelText, search.value]
                          ])
                        ]),
                        createVNode("div", null, [
                          createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, "الحالة"),
                          withDirectives(createVNode("select", {
                            "onUpdate:modelValue": ($event) => status.value = $event,
                            onChange: performSearch,
                            class: "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                          }, [
                            createVNode("option", { value: "" }, "جميع الحالات"),
                            createVNode("option", { value: "planned" }, "مخططة"),
                            createVNode("option", { value: "arrived" }, "وصلت"),
                            createVNode("option", { value: "berthed" }, "راسية"),
                            createVNode("option", { value: "working" }, "تعمل"),
                            createVNode("option", { value: "completed" }, "مكتملة"),
                            createVNode("option", { value: "departed" }, "غادرت"),
                            createVNode("option", { value: "cancelled" }, "ملغية")
                          ], 40, ["onUpdate:modelValue"]), [
                            [vModelSelect, status.value]
                          ])
                        ]),
                        createVNode("div", null, [
                          createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, "نوع الرحلة"),
                          withDirectives(createVNode("select", {
                            "onUpdate:modelValue": ($event) => type.value = $event,
                            onChange: performSearch,
                            class: "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                          }, [
                            createVNode("option", { value: "" }, "جميع الأنواع"),
                            createVNode("option", { value: "arrival" }, "وصول"),
                            createVNode("option", { value: "departure" }, "مغادرة"),
                            createVNode("option", { value: "transit" }, "عبور")
                          ], 40, ["onUpdate:modelValue"]), [
                            [vModelSelect, type.value]
                          ])
                        ]),
                        createVNode("div", null, [
                          createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, "الوكيل"),
                          withDirectives(createVNode("input", {
                            "onUpdate:modelValue": ($event) => agentId.value = $event,
                            onKeyup: withKeys(performSearch, ["enter"]),
                            type: "text",
                            placeholder: "معرف الوكيل...",
                            class: "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                          }, null, 40, ["onUpdate:modelValue"]), [
                            [vModelText, agentId.value]
                          ])
                        ]),
                        createVNode("div", { class: "flex items-end" }, [
                          createVNode("button", {
                            onClick: performSearch,
                            class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full"
                          }, " بحث ")
                        ])
                      ])
                    ])
                  ]),
                  createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                    createVNode("div", { class: "overflow-x-auto" }, [
                      createVNode("table", { class: "min-w-full divide-y divide-gray-200" }, [
                        createVNode("thead", { class: "bg-gray-50" }, [
                          createVNode("tr", null, [
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " رقم الرحلة "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " السفينة "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " الوكيل "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " النوع "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " نوع البضاعة "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " الوصول المتوقع "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " الحالة "),
                            createVNode("th", { class: "px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" }, " الإجراءات ")
                          ])
                        ]),
                        createVNode("tbody", { class: "bg-white divide-y divide-gray-200" }, [
                          (openBlock(true), createBlock(Fragment, null, renderList(__props.voyages.data, (voyage) => {
                            return openBlock(), createBlock("tr", {
                              key: voyage.id,
                              class: "hover:bg-gray-50"
                            }, [
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap" }, [
                                createVNode("div", { class: "text-sm font-medium text-gray-900" }, toDisplayString(voyage.voyage_number), 1),
                                createVNode("div", { class: "text-sm text-gray-500" }, toDisplayString(voyage.origin_port) + " → " + toDisplayString(voyage.destination_port), 1)
                              ]),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap" }, [
                                createVNode("div", { class: "text-sm font-medium text-gray-900" }, toDisplayString(voyage.ship?.name), 1),
                                createVNode("div", { class: "text-sm text-gray-500" }, "IMO: " + toDisplayString(voyage.ship?.imo_number), 1)
                              ]),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap" }, [
                                createVNode("div", { class: "text-sm text-gray-900" }, toDisplayString(voyage.agent?.company_name), 1)
                              ]),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap text-sm text-gray-900" }, toDisplayString(getVoyageTypeText(voyage.voyage_type)), 1),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap text-sm text-gray-900" }, toDisplayString(getCargoTypeText(voyage.cargo_type)), 1),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap text-sm text-gray-900" }, toDisplayString(formatDate(voyage.eta)), 1),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap" }, [
                                createVNode("span", {
                                  class: [getStatusColor(voyage.status), "inline-flex px-2 py-1 text-xs font-semibold rounded-full"]
                                }, toDisplayString(getStatusText(voyage.status)), 3)
                              ]),
                              createVNode("td", { class: "px-6 py-4 whitespace-nowrap text-sm font-medium" }, [
                                createVNode("div", { class: "flex space-x-2" }, [
                                  createVNode(unref(Link), {
                                    href: _ctx.route("voyages.show", voyage.id),
                                    class: "text-indigo-600 hover:text-indigo-900"
                                  }, {
                                    default: withCtx(() => [
                                      createTextVNode(" عرض ")
                                    ]),
                                    _: 2
                                  }, 1032, ["href"]),
                                  createVNode(unref(Link), {
                                    href: _ctx.route("voyages.edit", voyage.id),
                                    class: "text-green-600 hover:text-green-900"
                                  }, {
                                    default: withCtx(() => [
                                      createTextVNode(" تعديل ")
                                    ]),
                                    _: 2
                                  }, 1032, ["href"])
                                ])
                              ])
                            ]);
                          }), 128))
                        ])
                      ])
                    ]),
                    __props.voyages.links ? (openBlock(), createBlock("div", {
                      key: 0,
                      class: "px-6 py-3 border-t border-gray-200"
                    }, [
                      createVNode("div", { class: "flex justify-between items-center" }, [
                        createVNode("div", { class: "text-sm text-gray-700" }, " عرض " + toDisplayString(__props.voyages.from) + " إلى " + toDisplayString(__props.voyages.to) + " من " + toDisplayString(__props.voyages.total) + " نتيجة ", 1),
                        createVNode("div", { class: "flex space-x-1" }, [
                          (openBlock(true), createBlock(Fragment, null, renderList(__props.voyages.links, (link) => {
                            return openBlock(), createBlock(Fragment, {
                              key: link.label
                            }, [
                              link.url ? (openBlock(), createBlock(unref(Link), {
                                key: 0,
                                href: link.url,
                                class: [
                                  "px-3 py-2 text-sm rounded-md",
                                  link.active ? "bg-blue-500 text-white" : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-300"
                                ],
                                innerHTML: link.label
                              }, null, 8, ["href", "class", "innerHTML"])) : (openBlock(), createBlock("span", {
                                key: 1,
                                class: [
                                  "px-3 py-2 text-sm rounded-md",
                                  "bg-gray-100 text-gray-400 cursor-not-allowed"
                                ],
                                innerHTML: link.label
                              }, null, 8, ["innerHTML"]))
                            ], 64);
                          }), 128))
                        ])
                      ])
                    ])) : createCommentVNode("", true)
                  ])
                ])
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<!--]-->`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("resources/js/Pages/Voyages/Index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
export {
  _sfc_main as default
};
