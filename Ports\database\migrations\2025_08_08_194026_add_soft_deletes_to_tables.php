<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إضافة soft deletes للجداول التي تحتاجها
        $tables = [
            'agents',
            'ships',
            'voyages',
            'berths',
            'arrivals',
            'departures',
            'berth_bookings',
            'operations',
            'inspections',
            'fees',
            'invoices',
            'payments',
            'pilots',
            'pilot_assignments',
            'maintenance_requests',
            'documents',
            'port_alerts'
        ];

        foreach ($tables as $tableName) {
            Schema::table($tableName, function (Blueprint $table) {
                $table->softDeletes();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $tables = [
            'agents',
            'ships',
            'voyages',
            'berths',
            'arrivals',
            'departures',
            'berth_bookings',
            'operations',
            'inspections',
            'fees',
            'invoices',
            'payments',
            'pilots',
            'pilot_assignments',
            'maintenance_requests',
            'documents',
            'port_alerts'
        ];

        foreach ($tables as $tableName) {
            Schema::table($tableName, function (Blueprint $table) {
                $table->dropSoftDeletes();
            });
        }
    }
};
