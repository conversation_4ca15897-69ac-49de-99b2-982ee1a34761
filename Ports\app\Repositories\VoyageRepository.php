<?php

namespace App\Repositories;

use App\Models\Voyage;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;

class VoyageRepository extends BaseRepository
{
    protected function getModel(): Model
    {
        return new Voyage();
    }

    /**
     * Get active voyages
     */
    public function getActive(): Collection
    {
        return $this->scope('active')->all();
    }

    /**
     * Get voyages by status
     */
    public function getByStatus(string $status): Collection
    {
        return $this->scope('byStatus', $status)->all();
    }

    /**
     * Get voyages by type
     */
    public function getByType(string $type): Collection
    {
        return $this->scope('byType', $type)->all();
    }

    /**
     * Get voyages arriving today
     */
    public function getArrivingToday(): Collection
    {
        return $this->scope('arrivingToday')->all();
    }

    /**
     * Get voyages departing today
     */
    public function getDepartingToday(): Collection
    {
        return $this->scope('departingToday')->all();
    }

    /**
     * Find voyage by number
     */
    public function findByNumber(string $voyageNumber): ?Voyage
    {
        return $this->findBy('voyage_number', $voyageNumber);
    }

    /**
     * Get voyages with relationships
     */
    public function getWithRelations(): Collection
    {
        return $this->with(['ship', 'agent', 'arrival', 'departure', 'berthBookings'])->all();
    }

    /**
     * Get voyages for agent
     */
    public function getForAgent(int $agentId): Collection
    {
        return $this->where('agent_id', $agentId)
            ->with(['ship', 'arrival', 'departure'])
            ->latest()
            ->all();
    }

    /**
     * Get voyages for ship
     */
    public function getForShip(int $shipId): Collection
    {
        return $this->where('ship_id', $shipId)
            ->with(['agent', 'arrival', 'departure'])
            ->latest()
            ->all();
    }

    /**
     * Get voyages in date range
     */
    public function getInDateRange(\DateTime $startDate, \DateTime $endDate): Collection
    {
        return $this->query
            ->where(function ($query) use ($startDate, $endDate) {
                $query->whereBetween('eta', [$startDate, $endDate])
                    ->orWhereBetween('etd', [$startDate, $endDate]);
            })
            ->with(['ship', 'agent'])
            ->get();
    }

    /**
     * Get voyages with dangerous cargo
     */
    public function getWithDangerousCargo(): Collection
    {
        return $this->query
            ->whereNotNull('dangerous_goods')
            ->whereJsonLength('dangerous_goods', '>', 0)
            ->with(['ship', 'agent'])
            ->get();
    }

    /**
     * Get voyages requiring pilot
     */
    public function getRequiringPilot(): Collection
    {
        return $this->query
            ->whereHas('arrival', function ($query) {
                $query->where('requires_pilot', true)
                    ->whereIn('status', ['confirmed', 'pilot_assigned', 'approaching']);
            })
            ->with(['ship', 'agent', 'arrival'])
            ->get();
    }

    /**
     * Get voyages statistics
     */
    public function getStatistics(): array
    {
        $total = $this->count();
        $active = $this->scope('active')->count();
        $completed = $this->scope('byStatus', 'completed')->count();
        
        $byType = $this->model
            ->selectRaw('voyage_type, COUNT(*) as count')
            ->groupBy('voyage_type')
            ->pluck('count', 'voyage_type')
            ->toArray();

        $byCargoType = $this->model
            ->selectRaw('cargo_type, COUNT(*) as count')
            ->groupBy('cargo_type')
            ->pluck('count', 'cargo_type')
            ->toArray();

        $averageStayTime = $this->model
            ->whereHas('arrival', function ($query) {
                $query->whereNotNull('actual_arrival');
            })
            ->whereHas('departure', function ($query) {
                $query->whereNotNull('actual_departure');
            })
            ->with(['arrival', 'departure'])
            ->get()
            ->avg(function ($voyage) {
                if ($voyage->arrival && $voyage->departure && 
                    $voyage->arrival->actual_arrival && $voyage->departure->actual_departure) {
                    return $voyage->arrival->actual_arrival->diffInHours($voyage->departure->actual_departure);
                }
                return null;
            });

        return [
            'total' => $total,
            'active' => $active,
            'completed' => $completed,
            'by_type' => $byType,
            'by_cargo_type' => $byCargoType,
            'average_stay_time_hours' => round($averageStayTime ?? 0, 2),
        ];
    }

    /**
     * Get voyages for dashboard
     */
    public function getDashboardData(): array
    {
        $arrivingToday = $this->getArrivingToday();
        $departingToday = $this->getDepartingToday();
        $currentlyInPort = $this->getByStatus('berthed');
        $awaitingBerth = $this->getByStatus('arrived');

        return [
            'arriving_today' => $arrivingToday,
            'departing_today' => $departingToday,
            'currently_in_port' => $currentlyInPort,
            'awaiting_berth' => $awaitingBerth,
            'total_active' => $this->getActive()->count(),
        ];
    }

    /**
     * Get overdue voyages
     */
    public function getOverdue(): Collection
    {
        return $this->query
            ->where('status', 'planned')
            ->where('eta', '<', now())
            ->with(['ship', 'agent'])
            ->get();
    }

    /**
     * Get voyages requiring attention
     */
    public function getRequiringAttention(): Collection
    {
        return $this->query
            ->where(function ($query) {
                // Overdue arrivals
                $query->where('status', 'planned')
                    ->where('eta', '<', now()->subHours(2));
            })
            ->orWhere(function ($query) {
                // Long stays
                $query->where('status', 'berthed')
                    ->whereHas('arrival', function ($q) {
                        $q->where('actual_arrival', '<', now()->subDays(7));
                    });
            })
            ->orWhere(function ($query) {
                // Dangerous cargo
                $query->whereNotNull('dangerous_goods')
                    ->whereJsonLength('dangerous_goods', '>', 0)
                    ->whereIn('status', ['planned', 'arrived', 'berthed']);
            })
            ->with(['ship', 'agent', 'arrival', 'departure'])
            ->get();
    }

    /**
     * Update voyage status
     */
    public function updateStatus(int $voyageId, string $status): bool
    {
        return $this->update($voyageId, ['status' => $status]);
    }

    /**
     * Get voyage timeline
     */
    public function getTimeline(int $voyageId): array
    {
        $voyage = $this->with(['arrival', 'departure', 'operations', 'inspections', 'pilotAssignments'])
            ->findOrFail($voyageId);

        $timeline = [];

        // Voyage created
        $timeline[] = [
            'timestamp' => $voyage->created_at,
            'event' => 'voyage_created',
            'title' => 'تم إنشاء الرحلة',
            'description' => 'تم إنشاء رحلة جديدة للسفينة',
            'status' => 'completed'
        ];

        // Arrival events
        if ($voyage->arrival) {
            $arrival = $voyage->arrival;
            
            $timeline[] = [
                'timestamp' => $arrival->created_at,
                'event' => 'arrival_reported',
                'title' => 'تم الإبلاغ عن الوصول',
                'description' => 'تم تقديم إعلان الوصول',
                'status' => 'completed'
            ];

            if ($arrival->confirmed_eta) {
                $timeline[] = [
                    'timestamp' => $arrival->updated_at,
                    'event' => 'eta_confirmed',
                    'title' => 'تم تأكيد موعد الوصول',
                    'description' => 'تم تأكيد الموعد المتوقع للوصول',
                    'status' => 'completed'
                ];
            }

            if ($arrival->actual_arrival) {
                $timeline[] = [
                    'timestamp' => $arrival->actual_arrival,
                    'event' => 'ship_arrived',
                    'title' => 'وصلت السفينة',
                    'description' => 'وصلت السفينة فعلياً إلى الميناء',
                    'status' => 'completed'
                ];
            }

            if ($arrival->berthing_time) {
                $timeline[] = [
                    'timestamp' => $arrival->berthing_time,
                    'event' => 'ship_berthed',
                    'title' => 'رست السفينة',
                    'description' => 'رست السفينة في الرصيف المخصص',
                    'status' => 'completed'
                ];
            }
        }

        // Operations
        foreach ($voyage->operations as $operation) {
            if ($operation->actual_start) {
                $timeline[] = [
                    'timestamp' => $operation->actual_start,
                    'event' => 'operation_started',
                    'title' => 'بدأت العملية: ' . $operation->description,
                    'description' => 'بدأت عملية ' . $operation->type,
                    'status' => $operation->status === 'completed' ? 'completed' : 'in_progress'
                ];
            }

            if ($operation->actual_end) {
                $timeline[] = [
                    'timestamp' => $operation->actual_end,
                    'event' => 'operation_completed',
                    'title' => 'انتهت العملية: ' . $operation->description,
                    'description' => 'انتهت عملية ' . $operation->type,
                    'status' => 'completed'
                ];
            }
        }

        // Departure events
        if ($voyage->departure) {
            $departure = $voyage->departure;
            
            if ($departure->requested_etd) {
                $timeline[] = [
                    'timestamp' => $departure->created_at,
                    'event' => 'departure_requested',
                    'title' => 'تم طلب المغادرة',
                    'description' => 'تم تقديم طلب المغادرة',
                    'status' => 'completed'
                ];
            }

            if ($departure->actual_departure) {
                $timeline[] = [
                    'timestamp' => $departure->actual_departure,
                    'event' => 'ship_departed',
                    'title' => 'غادرت السفينة',
                    'description' => 'غادرت السفينة الميناء فعلياً',
                    'status' => 'completed'
                ];
            }
        }

        // Sort timeline by timestamp
        usort($timeline, function ($a, $b) {
            return $a['timestamp'] <=> $b['timestamp'];
        });

        return $timeline;
    }

    /**
     * Generate voyage number
     */
    public function generateVoyageNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');
        
        $lastVoyage = $this->model
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', now()->month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastVoyage ? 
            (int) substr($lastVoyage->voyage_number, -4) + 1 : 
            1;

        return sprintf('VOY-%s%s-%04d', $year, $month, $sequence);
    }
}
