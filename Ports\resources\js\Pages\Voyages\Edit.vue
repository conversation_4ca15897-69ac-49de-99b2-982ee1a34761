<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, useForm, Link } from '@inertiajs/vue3';
import { computed } from 'vue';

const props = defineProps({
    voyage: Object,
    ships: Array,
    agents: Array,
    berths: Array
});

const form = useForm({
    ship_id: props.voyage.ship_id || '',
    agent_id: props.voyage.agent_id || '',
    voyage_number: props.voyage.voyage_number || '',
    origin_port: props.voyage.origin_port || '',
    destination_port: props.voyage.destination_port || '',
    cargo_type: props.voyage.cargo_type || '',
    cargo_description: props.voyage.cargo_description || '',
    cargo_quantity: props.voyage.cargo_quantity || '',
    cargo_unit: props.voyage.cargo_unit || '',
    estimated_arrival: props.voyage.estimated_arrival || '',
    estimated_departure: props.voyage.estimated_departure || '',
    actual_arrival: props.voyage.actual_arrival || '',
    actual_departure: props.voyage.actual_departure || '',
    purpose: props.voyage.purpose || '',
    crew_count: props.voyage.crew_count || '',
    passenger_count: props.voyage.passenger_count || '',
    last_port: props.voyage.last_port || '',
    next_port: props.voyage.next_port || '',
    status: props.voyage.status || 'scheduled'
});

const cargoTypes = [
    { value: 'container', label: 'حاويات' },
    { value: 'bulk_dry', label: 'بضائع سائبة جافة' },
    { value: 'bulk_liquid', label: 'بضائع سائبة سائلة' },
    { value: 'general', label: 'بضائع عامة' },
    { value: 'passengers', label: 'ركاب' },
    { value: 'vehicles', label: 'مركبات' },
    { value: 'livestock', label: 'مواشي' },
    { value: 'dangerous', label: 'بضائع خطرة' }
];

const cargoUnits = [
    { value: 'tons', label: 'طن' },
    { value: 'teu', label: 'حاوية مكافئة' },
    { value: 'cubic_meters', label: 'متر مكعب' },
    { value: 'pieces', label: 'قطعة' },
    { value: 'passengers', label: 'راكب' },
    { value: 'vehicles', label: 'مركبة' }
];

const purposes = [
    { value: 'loading', label: 'تحميل' },
    { value: 'unloading', label: 'تفريغ' },
    { value: 'loading_unloading', label: 'تحميل وتفريغ' },
    { value: 'transit', label: 'عبور' },
    { value: 'bunkering', label: 'تزويد بالوقود' },
    { value: 'maintenance', label: 'صيانة' },
    { value: 'emergency', label: 'طوارئ' }
];

const selectedShip = computed(() => {
    return props.ships.find(ship => ship.id == form.ship_id);
});

const availableBerths = computed(() => {
    if (!selectedShip.value) return props.berths;
    
    return props.berths.filter(berth => {
        // Include current berth even if it doesn't meet criteria (already assigned)
        if (berth.id == form.berth_id) return true;
        
        // Filter other berths based on ship size and availability
        return berth.status === 'available' && 
               berth.max_length >= selectedShip.value.length &&
               berth.max_width >= selectedShip.value.width &&
               berth.max_draft >= selectedShip.value.draft;
    });
});

const submit = () => {
    form.put(route('voyages.update', props.voyage.id));
};

// Format datetime for input fields
const formatDateTime = (datetime) => {
    if (!datetime) return '';
    const date = new Date(datetime);
    return date.toISOString().slice(0, 16);
};

// Update form values with formatted dates
if (form.estimated_arrival) {
    form.estimated_arrival = formatDateTime(form.estimated_arrival);
}
if (form.estimated_departure) {
    form.estimated_departure = formatDateTime(form.estimated_departure);
}
if (form.actual_arrival) {
    form.actual_arrival = formatDateTime(form.actual_arrival);
}
if (form.actual_departure) {
    form.actual_departure = formatDateTime(form.actual_departure);
}
</script>

<template>
    <Head :title="`تعديل الرحلة - ${voyage.voyage_number}`" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    تعديل الرحلة - {{ voyage.voyage_number }}
                </h2>
                <Link :href="route('voyages.show', voyage.id)" 
                      class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    عرض التفاصيل
                </Link>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <form @submit.prevent="submit" class="p-6 space-y-6">
                        <!-- Basic Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">المعلومات الأساسية</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        السفينة *
                                    </label>
                                    <select 
                                        v-model="form.ship_id"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.ship_id }"
                                    >
                                        <option value="">اختر السفينة</option>
                                        <option v-for="ship in ships" :key="ship.id" :value="ship.id">
                                            {{ ship.name }} ({{ ship.imo_number }})
                                        </option>
                                    </select>
                                    <div v-if="form.errors.ship_id" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.ship_id }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الوكيل البحري *
                                    </label>
                                    <select 
                                        v-model="form.agent_id"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.agent_id }"
                                    >
                                        <option value="">اختر الوكيل البحري</option>
                                        <option v-for="agent in agents" :key="agent.id" :value="agent.id">
                                            {{ agent.name }}
                                        </option>
                                    </select>
                                    <div v-if="form.errors.agent_id" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.agent_id }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        رقم الرحلة *
                                    </label>
                                    <input 
                                        v-model="form.voyage_number"
                                        type="text" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.voyage_number }"
                                    />
                                    <div v-if="form.errors.voyage_number" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.voyage_number }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الغرض من الزيارة *
                                    </label>
                                    <select 
                                        v-model="form.purpose"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.purpose }"
                                    >
                                        <option value="">اختر الغرض</option>
                                        <option v-for="purpose in purposes" :key="purpose.value" :value="purpose.value">
                                            {{ purpose.label }}
                                        </option>
                                    </select>
                                    <div v-if="form.errors.purpose" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.purpose }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Port Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات الموانئ</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        ميناء المنشأ *
                                    </label>
                                    <input 
                                        v-model="form.origin_port"
                                        type="text" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.origin_port }"
                                    />
                                    <div v-if="form.errors.origin_port" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.origin_port }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        ميناء الوجهة *
                                    </label>
                                    <input 
                                        v-model="form.destination_port"
                                        type="text" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.destination_port }"
                                    />
                                    <div v-if="form.errors.destination_port" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.destination_port }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الميناء السابق
                                    </label>
                                    <input 
                                        v-model="form.last_port"
                                        type="text" 
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.last_port }"
                                    />
                                    <div v-if="form.errors.last_port" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.last_port }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الميناء التالي
                                    </label>
                                    <input 
                                        v-model="form.next_port"
                                        type="text" 
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.next_port }"
                                    />
                                    <div v-if="form.errors.next_port" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.next_port }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Cargo Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات البضائع</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        نوع البضاعة *
                                    </label>
                                    <select 
                                        v-model="form.cargo_type"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.cargo_type }"
                                    >
                                        <option value="">اختر نوع البضاعة</option>
                                        <option v-for="type in cargoTypes" :key="type.value" :value="type.value">
                                            {{ type.label }}
                                        </option>
                                    </select>
                                    <div v-if="form.errors.cargo_type" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.cargo_type }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        وصف البضاعة
                                    </label>
                                    <input 
                                        v-model="form.cargo_description"
                                        type="text" 
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.cargo_description }"
                                    />
                                    <div v-if="form.errors.cargo_description" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.cargo_description }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        كمية البضاعة
                                    </label>
                                    <input 
                                        v-model="form.cargo_quantity"
                                        type="number" 
                                        step="0.01"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.cargo_quantity }"
                                    />
                                    <div v-if="form.errors.cargo_quantity" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.cargo_quantity }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        وحدة القياس
                                    </label>
                                    <select 
                                        v-model="form.cargo_unit"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.cargo_unit }"
                                    >
                                        <option value="">اختر وحدة القياس</option>
                                        <option v-for="unit in cargoUnits" :key="unit.value" :value="unit.value">
                                            {{ unit.label }}
                                        </option>
                                    </select>
                                    <div v-if="form.errors.cargo_unit" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.cargo_unit }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Schedule Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات الجدولة</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الوصول المتوقع *
                                    </label>
                                    <input 
                                        v-model="form.estimated_arrival"
                                        type="datetime-local" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.estimated_arrival }"
                                    />
                                    <div v-if="form.errors.estimated_arrival" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.estimated_arrival }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        المغادرة المتوقعة *
                                    </label>
                                    <input 
                                        v-model="form.estimated_departure"
                                        type="datetime-local" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.estimated_departure }"
                                    />
                                    <div v-if="form.errors.estimated_departure" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.estimated_departure }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الوصول الفعلي
                                    </label>
                                    <input 
                                        v-model="form.actual_arrival"
                                        type="datetime-local" 
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.actual_arrival }"
                                    />
                                    <div v-if="form.errors.actual_arrival" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.actual_arrival }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        المغادرة الفعلية
                                    </label>
                                    <input 
                                        v-model="form.actual_departure"
                                        type="datetime-local" 
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.actual_departure }"
                                    />
                                    <div v-if="form.errors.actual_departure" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.actual_departure }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Berth and Crew Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات الرصيف والطاقم</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الرصيف
                                    </label>
                                    <select 
                                        v-model="form.berth_id"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.berth_id }"
                                    >
                                        <option value="">اختر الرصيف</option>
                                        <option v-for="berth in availableBerths" :key="berth.id" :value="berth.id">
                                            {{ berth.name }} ({{ berth.type }})
                                        </option>
                                    </select>
                                    <div v-if="form.errors.berth_id" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.berth_id }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        عدد أفراد الطاقم
                                    </label>
                                    <input 
                                        v-model="form.crew_count"
                                        type="number" 
                                        min="0"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.crew_count }"
                                    />
                                    <div v-if="form.errors.crew_count" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.crew_count }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        عدد الركاب
                                    </label>
                                    <input 
                                        v-model="form.passenger_count"
                                        type="number" 
                                        min="0"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.passenger_count }"
                                    />
                                    <div v-if="form.errors.passenger_count" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.passenger_count }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Status -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                حالة الرحلة
                            </label>
                            <select 
                                v-model="form.status"
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                :class="{ 'border-red-500': form.errors.status }"
                            >
                                <option value="scheduled">مجدولة</option>
                                <option value="en_route">في الطريق</option>
                                <option value="arrived">وصلت</option>
                                <option value="in_port">في الميناء</option>
                                <option value="departed">غادرت</option>
                                <option value="cancelled">ملغية</option>
                                <option value="delayed">متأخرة</option>
                            </select>
                            <div v-if="form.errors.status" class="text-red-500 text-sm mt-1">
                                {{ form.errors.status }}
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                            <Link :href="route('voyages.show', voyage.id)" 
                                  class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                إلغاء
                            </Link>
                            <button 
                                type="submit" 
                                :disabled="form.processing"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                            >
                                <span v-if="form.processing">جاري التحديث...</span>
                                <span v-else">تحديث</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
