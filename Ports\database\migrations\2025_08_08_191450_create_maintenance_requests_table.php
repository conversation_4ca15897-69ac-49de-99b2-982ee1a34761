<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('maintenance_requests', function (Blueprint $table) {
            $table->id();
            $table->string('request_number')->unique();
            $table->enum('type', ['preventive', 'corrective', 'emergency', 'upgrade']);
            $table->enum('category', ['berth', 'crane', 'electrical', 'mechanical', 'civil', 'marine', 'it', 'other']);
            $table->string('asset_name'); // اسم الأصل
            $table->string('asset_code')->nullable(); // رمز الأصل
            $table->string('location');
            $table->text('description');
            $table->enum('priority', ['low', 'normal', 'high', 'critical'])->default('normal');
            $table->enum('status', ['requested', 'approved', 'assigned', 'in_progress', 'completed', 'cancelled'])
                ->default('requested');
            $table->foreignId('requested_by')->constrained('users')->onDelete('cascade');
            $table->datetime('requested_at');
            $table->datetime('required_by')->nullable();
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->datetime('assigned_at')->nullable();
            $table->datetime('started_at')->nullable();
            $table->datetime('completed_at')->nullable();
            $table->decimal('estimated_cost', 10, 2)->nullable();
            $table->decimal('actual_cost', 10, 2)->nullable();
            $table->decimal('estimated_hours', 8, 2)->nullable();
            $table->decimal('actual_hours', 8, 2)->nullable();
            $table->json('required_parts')->nullable(); // قطع الغيار المطلوبة
            $table->json('used_parts')->nullable(); // قطع الغيار المستخدمة
            $table->json('required_tools')->nullable(); // أدوات مطلوبة
            $table->text('work_performed')->nullable();
            $table->text('completion_notes')->nullable();
            $table->boolean('requires_shutdown')->default(false);
            $table->datetime('shutdown_start')->nullable();
            $table->datetime('shutdown_end')->nullable();
            $table->json('safety_measures')->nullable();
            $table->json('quality_checks')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->datetime('approved_at')->nullable();
            $table->text('notes')->nullable();
            $table->json('attachments')->nullable(); // صور، ملفات
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['status', 'priority']);
            $table->index(['category', 'type']);
            $table->index('request_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('maintenance_requests');
    }
};
