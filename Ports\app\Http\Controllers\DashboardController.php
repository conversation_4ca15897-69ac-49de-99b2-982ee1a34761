<?php

namespace App\Http\Controllers;

use App\Services\VoyageService;
use App\Services\ShipService;
use App\Services\AgentService;
use App\Models\Berth;
use App\Models\Operation;
use App\Models\Inspection;
use App\Models\Invoice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function __construct(
        private VoyageService $voyageService,
        private ShipService $shipService,
        private AgentService $agentService
    ) {}

    /**
     * Display the main dashboard
     */
    public function index()
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Get role-specific dashboard data
        if ($user->hasRole('agent')) {
            return $this->agentDashboard();
        } elseif ($user->hasRole('pilot')) {
            return $this->pilotDashboard();
        } elseif ($user->hasRole('control_tower')) {
            return $this->controlTowerDashboard();
        } elseif ($user->hasRole('port_manager') || $user->hasRole('admin')) {
            return $this->executiveDashboard();
        } else {
            return $this->generalDashboard();
        }
    }

    /**
     * Agent dashboard
     */
    private function agentDashboard()
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();
        $agent = $user->agent;

        if (!$agent) {
            return redirect()->route('profile.edit')
                ->with('error', 'يرجى إكمال بيانات الوكالة الملاحية');
        }

        $dashboardData = $this->agentService->getAgentDashboard($agent->id);

        return Inertia::render('Dashboard', [
            'agent' => $dashboardData['agent'],
            'stats' => [
                'active_voyages' => $dashboardData['active_voyages'],
                'pending_invoices' => $dashboardData['pending_invoices'],
                'total_revenue' => $dashboardData['total_revenue'],
            ],
            'recent_voyages' => $dashboardData['recent_voyages'],
        ]);
    }

    /**
     * Pilot dashboard
     */
    private function pilotDashboard()
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();
        $pilot = $user->pilot;

        if (!$pilot) {
            return redirect()->route('profile.edit')
                ->with('error', 'يرجى إكمال بيانات المرشد البحري');
        }

        // For now, return basic dashboard since pilot methods don't exist yet
        return Inertia::render('Dashboard', [
            'pilot' => $pilot,
            'stats' => [
                'total_assignments' => 0,
                'completed_assignments' => 0,
                'success_rate' => 0,
                'average_duration' => 0,
            ],
        ]);
    }

    /**
     * Control tower dashboard
     */
    private function controlTowerDashboard()
    {
        $voyageData = $this->voyageService->getDashboardData();
        $shipData = $this->shipService->getShipDashboard();

        $currentlyInPort = $this->shipService->getCurrentlyInPort();
        $expectedToday = $this->shipService->getExpectedToday();
        $departingToday = $this->shipService->getDepartingToday();

        return Inertia::render('Dashboard', [
            'voyages' => $voyageData,
            'ships' => $shipData,
            'port_status' => [
                'currently_in_port' => $currentlyInPort,
                'expected_today' => $expectedToday,
                'departing_today' => $departingToday,
            ],
        ]);
    }

    /**
     * Executive dashboard
     */
    private function executiveDashboard()
    {
        // Get comprehensive statistics
        $voyageStats = $this->voyageService->getStatistics();
        $shipStats = $this->shipService->getStatistics();
        $agentStats = $this->agentService->getStatistics();

        // Get operational data
        $berthStats = [
            'total' => Berth::count(),
            'available' => Berth::where('status', 'available')->count(),
            'occupied' => Berth::where('status', 'occupied')->count(),
            'maintenance' => Berth::where('status', 'maintenance')->count(),
        ];

        $operationStats = [
            'active' => Operation::whereIn('status', ['in_progress', 'paused'])->count(),
            'completed_today' => Operation::where('status', 'completed')
                ->whereDate('updated_at', today())->count(),
            'scheduled_today' => Operation::whereDate('planned_start', today())->count(),
        ];

        $inspectionStats = [
            'scheduled_today' => Inspection::whereDate('scheduled_at', today())->count(),
            'completed_today' => Inspection::where('status', 'completed')
                ->whereDate('completed_at', today())->count(),
            'pending' => Inspection::where('status', 'scheduled')->count(),
        ];

        $financialStats = [
            'pending_invoices' => Invoice::where('status', 'sent')->count(),
            'overdue_invoices' => Invoice::where('status', 'overdue')->count(),
            'revenue_today' => Invoice::where('status', 'paid')
                ->whereDate('updated_at', today())->sum('total_amount'),
            'revenue_month' => Invoice::where('status', 'paid')
                ->whereMonth('updated_at', now()->month)
                ->whereYear('updated_at', now()->year)
                ->sum('total_amount'),
        ];

        return Inertia::render('Dashboard', [
            'voyage_stats' => $voyageStats,
            'ship_stats' => $shipStats,
            'agent_stats' => $agentStats,
            'berth_stats' => $berthStats,
            'operation_stats' => $operationStats,
            'inspection_stats' => $inspectionStats,
            'financial_stats' => $financialStats,
        ]);
    }

    /**
     * General dashboard for other roles
     */
    private function generalDashboard()
    {
        $basicStats = [
            'ships_in_port' => $this->shipService->getCurrentlyInPort()->count(),
            'expected_today' => $this->shipService->getExpectedToday()->count(),
            'departing_today' => $this->shipService->getDepartingToday()->count(),
            'active_operations' => Operation::whereIn('status', ['in_progress', 'paused'])->count(),
        ];

        return Inertia::render('Dashboard', [
            'stats' => $basicStats,
        ]);
    }

    /**
     * Get port traffic data for charts
     */
    public function getPortTraffic(Request $request)
    {
        $days = $request->get('days', 7);
        $data = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $trafficData = $this->voyageService->getPortTrafficSummary($date);

            $data[] = [
                'date' => $date->format('Y-m-d'),
                'arrivals' => $trafficData['arrivals']->count(),
                'departures' => $trafficData['departures']->count(),
                'total_movements' => $trafficData['total_movements'],
            ];
        }

        return response()->json($data);
    }

    /**
     * Get real-time port status
     */
    public function getPortStatus()
    {
        return response()->json([
            'currently_in_port' => $this->shipService->getCurrentlyInPort()->count(),
            'expected_today' => $this->shipService->getExpectedToday()->count(),
            'departing_today' => $this->shipService->getDepartingToday()->count(),
            'available_berths' => Berth::where('status', 'available')->count(),
            'active_operations' => Operation::whereIn('status', ['in_progress', 'paused'])->count(),
            'last_updated' => now()->toISOString(),
        ]);
    }
}
