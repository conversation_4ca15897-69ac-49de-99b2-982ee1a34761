import{A as B}from"./ApplicationLogo-WdDnJ3OK.js";import{B as D,C as M,y as m,m as S,c as v,o as d,d as s,f as $,a as n,z as f,D as C,w as r,n as u,T as N,s as w,b as y,q as b,g as z,l as i,t as _}from"./app-CdA4UbU6.js";const E={class:"relative"},j={__name:"Dropdown",props:{align:{type:String,default:"right"},width:{type:String,default:"48"},contentClasses:{type:String,default:"py-1 bg-white"}},setup(a){const o=a,e=h=>{l.value&&h.key==="Escape"&&(l.value=!1)};D(()=>document.addEventListener("keydown",e)),M(()=>document.removeEventListener("keydown",e));const t=m(()=>({48:"w-48"})[o.width.toString()]),g=m(()=>o.align==="left"?"ltr:origin-top-left rtl:origin-top-right start-0":o.align==="right"?"ltr:origin-top-right rtl:origin-top-left end-0":"origin-top"),l=S(!1);return(h,c)=>(d(),v("div",E,[s("div",{onClick:c[0]||(c[0]=k=>l.value=!l.value)},[f(h.$slots,"trigger")]),$(s("div",{class:"fixed inset-0 z-40",onClick:c[1]||(c[1]=k=>l.value=!1)},null,512),[[C,l.value]]),n(N,{"enter-active-class":"transition ease-out duration-200","enter-from-class":"opacity-0 scale-95","enter-to-class":"opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"opacity-100 scale-100","leave-to-class":"opacity-0 scale-95"},{default:r(()=>[$(s("div",{class:u(["absolute z-50 mt-2 rounded-md shadow-lg",[t.value,g.value]]),style:{display:"none"},onClick:c[2]||(c[2]=k=>l.value=!1)},[s("div",{class:u(["rounded-md ring-1 ring-black ring-opacity-5",a.contentClasses])},[f(h.$slots,"content")],2)],2),[[C,l.value]])]),_:3})]))}},L={__name:"DropdownLink",props:{href:{type:String,required:!0}},setup(a){return(o,e)=>(d(),w(y(b),{href:a.href,class:"block w-full px-4 py-2 text-start text-sm leading-5 text-gray-700 transition duration-150 ease-in-out hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"},{default:r(()=>[f(o.$slots,"default")]),_:3},8,["href"]))}},p={__name:"NavLink",props:{href:{type:String,required:!0},active:{type:Boolean}},setup(a){const o=a,e=m(()=>o.active?"inline-flex items-center px-1 pt-1 border-b-2 border-indigo-400 text-sm font-medium leading-5 text-gray-900 focus:outline-none focus:border-indigo-700 transition duration-150 ease-in-out":"inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium leading-5 text-gray-500 hover:text-gray-700 hover:border-gray-300 focus:outline-none focus:text-gray-700 focus:border-gray-300 transition duration-150 ease-in-out");return(t,g)=>(d(),w(y(b),{href:a.href,class:u(e.value)},{default:r(()=>[f(t.$slots,"default")]),_:3},8,["href","class"]))}},x={__name:"ResponsiveNavLink",props:{href:{type:String,required:!0},active:{type:Boolean}},setup(a){const o=a,e=m(()=>o.active?"block w-full ps-3 pe-4 py-2 border-l-4 border-indigo-400 text-start text-base font-medium text-indigo-700 bg-indigo-50 focus:outline-none focus:text-indigo-800 focus:bg-indigo-100 focus:border-indigo-700 transition duration-150 ease-in-out":"block w-full ps-3 pe-4 py-2 border-l-4 border-transparent text-start text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-300 focus:outline-none focus:text-gray-800 focus:bg-gray-50 focus:border-gray-300 transition duration-150 ease-in-out");return(t,g)=>(d(),w(y(b),{href:a.href,class:u(e.value)},{default:r(()=>[f(t.$slots,"default")]),_:3},8,["href","class"]))}},q={class:"min-h-screen bg-gray-100"},V={class:"border-b border-gray-100 bg-white"},A={class:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8"},O={class:"flex h-16 justify-between"},T={class:"flex"},P={class:"flex shrink-0 items-center"},R={class:"hidden space-x-8 sm:-my-px sm:ms-10 sm:flex"},U={class:"hidden sm:ms-6 sm:flex sm:items-center"},F={class:"relative ms-3"},G={class:"inline-flex rounded-md"},H={type:"button",class:"inline-flex items-center rounded-md border border-transparent bg-white px-3 py-2 text-sm font-medium leading-4 text-gray-500 transition duration-150 ease-in-out hover:text-gray-700 focus:outline-none"},I={class:"-me-2 flex items-center sm:hidden"},J={class:"h-6 w-6",stroke:"currentColor",fill:"none",viewBox:"0 0 24 24"},K={class:"space-y-1 pb-3 pt-2"},Q={class:"border-t border-gray-200 pb-1 pt-4"},W={class:"px-4"},X={class:"text-base font-medium text-gray-800"},Y={class:"text-sm font-medium text-gray-500"},Z={class:"mt-3 space-y-1"},ee={key:0,class:"bg-white shadow"},te={class:"mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8"},re={__name:"AuthenticatedLayout",setup(a){const o=S(!1);return(e,t)=>(d(),v("div",null,[s("div",q,[s("nav",V,[s("div",A,[s("div",O,[s("div",T,[s("div",P,[n(y(b),{href:e.route("dashboard")},{default:r(()=>[n(B,{class:"block h-9 w-auto fill-current text-gray-800"})]),_:1},8,["href"])]),s("div",R,[n(p,{href:e.route("dashboard"),active:e.route().current("dashboard")},{default:r(()=>t[1]||(t[1]=[i(" لوحة التحكم ",-1)])),_:1,__:[1]},8,["href","active"]),n(p,{href:e.route("voyages.index"),active:e.route().current("voyages.*")},{default:r(()=>t[2]||(t[2]=[i(" الرحلات ",-1)])),_:1,__:[2]},8,["href","active"]),n(p,{href:e.route("ships.index"),active:e.route().current("ships.*")},{default:r(()=>t[3]||(t[3]=[i(" السفن ",-1)])),_:1,__:[3]},8,["href","active"]),n(p,{href:e.route("agents.index"),active:e.route().current("agents.*")},{default:r(()=>t[4]||(t[4]=[i(" الوكلاء الملاحيين ",-1)])),_:1,__:[4]},8,["href","active"]),n(p,{href:e.route("berths.index"),active:e.route().current("berths.*")},{default:r(()=>t[5]||(t[5]=[i(" الأرصفة ",-1)])),_:1,__:[5]},8,["href","active"])])]),s("div",U,[s("div",F,[n(j,{align:"right",width:"48"},{trigger:r(()=>[s("span",G,[s("button",H,[i(_(e.$page.props.auth.user.name)+" ",1),t[6]||(t[6]=s("svg",{class:"-me-0.5 ms-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor"},[s("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1))])])]),content:r(()=>[n(L,{href:e.route("profile.edit")},{default:r(()=>t[7]||(t[7]=[i(" Profile ",-1)])),_:1,__:[7]},8,["href"]),n(L,{href:e.route("logout"),method:"post",as:"button"},{default:r(()=>t[8]||(t[8]=[i(" Log Out ",-1)])),_:1,__:[8]},8,["href"])]),_:1})])]),s("div",I,[s("button",{onClick:t[0]||(t[0]=g=>o.value=!o.value),class:"inline-flex items-center justify-center rounded-md p-2 text-gray-400 transition duration-150 ease-in-out hover:bg-gray-100 hover:text-gray-500 focus:bg-gray-100 focus:text-gray-500 focus:outline-none"},[(d(),v("svg",J,[s("path",{class:u({hidden:o.value,"inline-flex":!o.value}),"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"},null,2),s("path",{class:u({hidden:!o.value,"inline-flex":o.value}),"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,2)]))])])])]),s("div",{class:u([{block:o.value,hidden:!o.value},"sm:hidden"])},[s("div",K,[n(x,{href:e.route("dashboard"),active:e.route().current("dashboard")},{default:r(()=>t[9]||(t[9]=[i(" Dashboard ",-1)])),_:1,__:[9]},8,["href","active"])]),s("div",Q,[s("div",W,[s("div",X,_(e.$page.props.auth.user.name),1),s("div",Y,_(e.$page.props.auth.user.email),1)]),s("div",Z,[n(x,{href:e.route("profile.edit")},{default:r(()=>t[10]||(t[10]=[i(" Profile ",-1)])),_:1,__:[10]},8,["href"]),n(x,{href:e.route("logout"),method:"post",as:"button"},{default:r(()=>t[11]||(t[11]=[i(" Log Out ",-1)])),_:1,__:[11]},8,["href"])])])],2)]),e.$slots.header?(d(),v("header",ee,[s("div",te,[f(e.$slots,"header")])])):z("",!0),s("main",null,[f(e.$slots,"default")])])]))}};export{re as _};
