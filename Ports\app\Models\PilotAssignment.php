<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class PilotAssignment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'pilot_id',
        'voyage_id',
        'arrival_id',
        'departure_id',
        'assignment_number',
        'type',
        'scheduled_time',
        'boarding_time',
        'disembark_time',
        'boarding_location',
        'disembark_location',
        'status',
        'priority',
        'night_operation',
        'requires_tugboat',
        'weather_conditions',
        'special_requirements',
        'route_plan',
        'estimated_duration',
        'actual_duration',
        'pilot_fee',
        'completion_report',
        'incidents',
        'assigned_by',
        'assigned_at',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'scheduled_time' => 'datetime',
        'boarding_time' => 'datetime',
        'disembark_time' => 'datetime',
        'assigned_at' => 'datetime',
        'night_operation' => 'boolean',
        'requires_tugboat' => 'boolean',
        'weather_conditions' => 'array',
        'special_requirements' => 'array',
        'estimated_duration' => 'decimal:2',
        'actual_duration' => 'decimal:2',
        'pilot_fee' => 'decimal:2',
        'incidents' => 'array',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'assigned',
        'priority' => 'normal',
        'night_operation' => false,
        'requires_tugboat' => false,
    ];

    // العلاقات
    public function pilot(): BelongsTo
    {
        return $this->belongsTo(Pilot::class);
    }

    public function voyage(): BelongsTo
    {
        return $this->belongsTo(Voyage::class);
    }

    public function arrival(): BelongsTo
    {
        return $this->belongsTo(Arrival::class);
    }

    public function departure(): BelongsTo
    {
        return $this->belongsTo(Departure::class);
    }

    public function assignedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    // النطاقات
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeActive($query)
    {
        return $query->whereIn('status', ['assigned', 'confirmed', 'boarding', 'piloting']);
    }

    public function scopeScheduledToday($query)
    {
        return $query->whereDate('scheduled_time', today());
    }

    public function scopeUpcoming($query)
    {
        return $query->where('scheduled_time', '>', now())
            ->whereIn('status', ['assigned', 'confirmed']);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeNightOperations($query)
    {
        return $query->where('night_operation', true);
    }

    // الخصائص المحسوبة
    public function getIsActiveAttribute(): bool
    {
        return in_array($this->status, ['assigned', 'confirmed', 'boarding', 'piloting']);
    }

    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'completed';
    }

    public function getIsNightOperationAttribute(): bool
    {
        $hour = $this->scheduled_time->format('H');
        return $hour < 6 || $hour >= 18;
    }

    public function getDurationVarianceAttribute(): ?float
    {
        if ($this->actual_duration && $this->estimated_duration) {
            return $this->actual_duration - $this->estimated_duration;
        }
        return null;
    }

    public function getIsDelayedAttribute(): bool
    {
        return $this->boarding_time &&
               $this->boarding_time > $this->scheduled_time->addMinutes(15);
    }

    public function getDelayInMinutesAttribute(): ?int
    {
        if ($this->is_delayed) {
            return $this->scheduled_time->diffInMinutes($this->boarding_time);
        }
        return null;
    }

    public function getHasIncidentsAttribute(): bool
    {
        return !empty($this->incidents);
    }

    public function getFormattedScheduledTimeAttribute(): string
    {
        return $this->scheduled_time->format('Y-m-d H:i');
    }

    public function getFormattedFeeAttribute(): string
    {
        return $this->pilot_fee ? number_format($this->pilot_fee, 2) . ' SYP' : 'N/A';
    }

    // دوال مساعدة
    public function canStart(): bool
    {
        return $this->status === 'confirmed' &&
               $this->scheduled_time <= now()->addMinutes(30);
    }

    public function canComplete(): bool
    {
        return in_array($this->status, ['boarding', 'piloting']);
    }

    public function canCancel(): bool
    {
        return in_array($this->status, ['assigned', 'confirmed']) &&
               $this->scheduled_time > now()->addHours(1);
    }

    public function markAsBoarding(): bool
    {
        if ($this->status === 'confirmed') {
            return $this->update([
                'status' => 'boarding',
                'boarding_time' => now(),
            ]);
        }
        return false;
    }

    public function markAsPiloting(): bool
    {
        if ($this->status === 'boarding') {
            return $this->update(['status' => 'piloting']);
        }
        return false;
    }

    public function markAsCompleted(array $completionData = []): bool
    {
        if (!$this->canComplete()) {
            return false;
        }

        $data = array_merge([
            'status' => 'completed',
            'disembark_time' => now(),
        ], $completionData);

        // حساب المدة الفعلية
        if ($this->boarding_time && !isset($data['actual_duration'])) {
            $data['actual_duration'] = $this->boarding_time->diffInHours(now(), true);
        }

        return $this->update($data);
    }

    public function addIncident(string $description, string $severity = 'minor'): void
    {
        $incidents = $this->incidents ?? [];

        $incidents[] = [
            'timestamp' => now()->toISOString(),
            'description' => $description,
            'severity' => $severity,
            'reported_by' => Auth::id(),
        ];

        $this->update(['incidents' => $incidents]);
    }

    public function calculateFee(): float
    {
        if ($this->pilot_fee) {
            return $this->pilot_fee;
        }

        $duration = $this->actual_duration ?? $this->estimated_duration ?? 2;
        return $this->pilot->calculateFee($this->scheduled_time, $duration);
    }

    public static function generateAssignmentNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');

        $lastAssignment = self::whereYear('created_at', $year)
            ->whereMonth('created_at', now()->month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastAssignment ?
            (int) substr($lastAssignment->assignment_number, -4) + 1 :
            1;

        return sprintf('PA-%s%s-%04d', $year, $month, $sequence);
    }

    public function getWeatherSuitability(): string
    {
        $conditions = $this->weather_conditions;

        if (!$conditions) {
            return 'unknown';
        }

        $windSpeed = $conditions['wind_speed'] ?? 0;
        $visibility = $conditions['visibility'] ?? 10;
        $waveHeight = $conditions['wave_height'] ?? 0;

        if ($windSpeed > 25 || $visibility < 2 || $waveHeight > 3) {
            return 'poor';
        }

        if ($windSpeed > 15 || $visibility < 5 || $waveHeight > 2) {
            return 'moderate';
        }

        return 'good';
    }
}
