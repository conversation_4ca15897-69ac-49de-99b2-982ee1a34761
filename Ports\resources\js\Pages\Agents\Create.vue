<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';

const form = useForm({
    company_name: '',
    company_name_en: '',
    license_number: '',
    email: '',
    phone: '',
    address: '',
    address_en: '',
    contact_person: '',
    contact_person_en: '',
    license_expiry: '',
    services: [],
    notes: '',
    status: 'active'
});

const availableServices = [
    'ship_agency',
    'cargo_handling',
    'customs_clearance',
    'crew_services',
    'supply_services',
    'waste_management',
    'bunker_services',
    'repair_services'
];

const getServiceName = (service) => {
    const names = {
        'ship_agency': 'وكالة ملاحية',
        'cargo_handling': 'مناولة البضائع',
        'customs_clearance': 'تخليص جمركي',
        'crew_services': 'خدمات الطاقم',
        'supply_services': 'خدمات التموين',
        'waste_management': 'إدارة النفايات',
        'bunker_services': 'خدمات الوقود',
        'repair_services': 'خدمات الإصلاح'
    };
    return names[service] || service;
};

const submit = () => {
    form.post(route('agents.store'));
};
</script>

<template>
    <Head title="إضافة وكيل ملاحي جديد" />

    <AuthenticatedLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                إضافة وكيل ملاحي جديد
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <form @submit.prevent="submit" class="p-6 space-y-6">
                        <!-- Company Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات الشركة</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        اسم الشركة (عربي) *
                                    </label>
                                    <input 
                                        v-model="form.company_name"
                                        type="text" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.company_name }"
                                    />
                                    <div v-if="form.errors.company_name" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.company_name }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        اسم الشركة (إنجليزي)
                                    </label>
                                    <input 
                                        v-model="form.company_name_en"
                                        type="text" 
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.company_name_en }"
                                    />
                                    <div v-if="form.errors.company_name_en" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.company_name_en }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        رقم الترخيص *
                                    </label>
                                    <input 
                                        v-model="form.license_number"
                                        type="text" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.license_number }"
                                    />
                                    <div v-if="form.errors.license_number" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.license_number }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        تاريخ انتهاء الترخيص *
                                    </label>
                                    <input 
                                        v-model="form.license_expiry"
                                        type="date" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.license_expiry }"
                                    />
                                    <div v-if="form.errors.license_expiry" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.license_expiry }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات الاتصال</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الشخص المسؤول (عربي) *
                                    </label>
                                    <input 
                                        v-model="form.contact_person"
                                        type="text" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.contact_person }"
                                    />
                                    <div v-if="form.errors.contact_person" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.contact_person }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الشخص المسؤول (إنجليزي)
                                    </label>
                                    <input 
                                        v-model="form.contact_person_en"
                                        type="text" 
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.contact_person_en }"
                                    />
                                    <div v-if="form.errors.contact_person_en" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.contact_person_en }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        البريد الإلكتروني *
                                    </label>
                                    <input 
                                        v-model="form.email"
                                        type="email" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.email }"
                                    />
                                    <div v-if="form.errors.email" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.email }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        رقم الهاتف *
                                    </label>
                                    <input 
                                        v-model="form.phone"
                                        type="tel" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.phone }"
                                    />
                                    <div v-if="form.errors.phone" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.phone }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Address Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات العنوان</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        العنوان (عربي) *
                                    </label>
                                    <textarea 
                                        v-model="form.address"
                                        rows="3"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.address }"
                                    ></textarea>
                                    <div v-if="form.errors.address" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.address }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        العنوان (إنجليزي)
                                    </label>
                                    <textarea 
                                        v-model="form.address_en"
                                        rows="3"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.address_en }"
                                    ></textarea>
                                    <div v-if="form.errors.address_en" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.address_en }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Services -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">الخدمات المقدمة</h3>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div v-for="service in availableServices" :key="service" class="flex items-center">
                                    <input 
                                        :id="service"
                                        v-model="form.services"
                                        :value="service"
                                        type="checkbox"
                                        class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                    />
                                    <label :for="service" class="mr-2 text-sm text-gray-700">
                                        {{ getServiceName(service) }}
                                    </label>
                                </div>
                            </div>
                            <div v-if="form.errors.services" class="text-red-500 text-sm mt-1">
                                {{ form.errors.services }}
                            </div>
                        </div>

                        <!-- Status and Notes -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    الحالة
                                </label>
                                <select 
                                    v-model="form.status"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    :class="{ 'border-red-500': form.errors.status }"
                                >
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="suspended">معلق</option>
                                </select>
                                <div v-if="form.errors.status" class="text-red-500 text-sm mt-1">
                                    {{ form.errors.status }}
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    ملاحظات
                                </label>
                                <textarea 
                                    v-model="form.notes"
                                    rows="3"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    :class="{ 'border-red-500': form.errors.notes }"
                                ></textarea>
                                <div v-if="form.errors.notes" class="text-red-500 text-sm mt-1">
                                    {{ form.errors.notes }}
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                            <Link :href="route('agents.index')" 
                                  class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                إلغاء
                            </Link>
                            <button 
                                type="submit" 
                                :disabled="form.processing"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                            >
                                <span v-if="form.processing">جاري الحفظ...</span>
                                <span v-else>حفظ</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
