<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('berths', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->string('terminal')->nullable();
            $table->enum('type', ['container', 'bulk', 'general', 'liquid', 'passenger', 'ro_ro']);
            $table->decimal('length', 8, 2);
            $table->decimal('width', 8, 2)->nullable();
            $table->decimal('depth', 8, 2);
            $table->decimal('max_draft', 8, 2);
            $table->decimal('max_loa', 8, 2); // Maximum Length Overall
            $table->decimal('max_beam', 8, 2); // Maximum Beam
            $table->decimal('max_dwt', 10, 2); // Maximum Deadweight
            $table->enum('status', ['available', 'occupied', 'reserved', 'maintenance', 'out_of_service'])
                ->default('available');
            $table->json('facilities')->nullable(); // مرافق الرصيف (رافعات، كهرباء، مياه)
            $table->json('equipment')->nullable(); // معدات الرصيف
            $table->decimal('hourly_rate', 8, 2)->nullable(); // سعر الساعة
            $table->decimal('daily_rate', 8, 2)->nullable(); // سعر اليوم
            $table->text('notes')->nullable();
            $table->json('coordinates')->nullable(); // إحداثيات GPS
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['status', 'type']);
            $table->index(['code', 'terminal']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('berths');
    }
};
