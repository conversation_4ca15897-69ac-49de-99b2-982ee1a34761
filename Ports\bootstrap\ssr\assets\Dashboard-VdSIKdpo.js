import { unref, withCtx, createVNode, createBlock, openBlock, toDisplayString, useSSRContext } from "vue";
import { ssrRenderComponent, ssrInterpolate } from "vue/server-renderer";
import { _ as _sfc_main$1 } from "./AuthenticatedLayout-DMuuhipg.js";
import { Head } from "@inertiajs/vue3";
import "./ApplicationLogo-B2173abF.js";
import "./_plugin-vue_export-helper-1tPrXgE0.js";
const _sfc_main = {
  __name: "Dashboard",
  __ssrInlineRender: true,
  props: {
    stats: {
      type: Object,
      default: () => ({
        ships_in_port: 0,
        expected_today: 0,
        departing_today: 0,
        active_operations: 0
      })
    }
  },
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<!--[-->`);
      _push(ssrRenderComponent(unref(Head), { title: "لوحة التحكم" }, null, _parent));
      _push(ssrRenderComponent(_sfc_main$1, null, {
        header: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<h2 class="text-xl font-semibold leading-tight text-gray-800"${_scopeId}> لوحة التحكم الرئيسية </h2>`);
          } else {
            return [
              createVNode("h2", { class: "text-xl font-semibold leading-tight text-gray-800" }, " لوحة التحكم الرئيسية ")
            ];
          }
        }),
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="py-12"${_scopeId}><div class="mx-auto max-w-7xl sm:px-6 lg:px-8"${_scopeId}><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"${_scopeId}><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"${_scopeId}><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"${_scopeId}></path></svg></div></div><div class="ml-4"${_scopeId}><p class="text-sm font-medium text-gray-500"${_scopeId}>السفن في الميناء</p><p class="text-2xl font-semibold text-gray-900"${_scopeId}>${ssrInterpolate(__props.stats?.ships_in_port || 0)}</p></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"${_scopeId}><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"${_scopeId}></path></svg></div></div><div class="ml-4"${_scopeId}><p class="text-sm font-medium text-gray-500"${_scopeId}>متوقعة اليوم</p><p class="text-2xl font-semibold text-gray-900"${_scopeId}>${ssrInterpolate(__props.stats?.expected_today || 0)}</p></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center"${_scopeId}><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1zM3 7a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1V7zM3 12a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1z" clip-rule="evenodd"${_scopeId}></path></svg></div></div><div class="ml-4"${_scopeId}><p class="text-sm font-medium text-gray-500"${_scopeId}>مغادرة اليوم</p><p class="text-2xl font-semibold text-gray-900"${_scopeId}>${ssrInterpolate(__props.stats?.departing_today || 0)}</p></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6"${_scopeId}><div class="flex items-center"${_scopeId}><div class="flex-shrink-0"${_scopeId}><div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center"${_scopeId}><svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"${_scopeId}><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"${_scopeId}></path></svg></div></div><div class="ml-4"${_scopeId}><p class="text-sm font-medium text-gray-500"${_scopeId}>العمليات النشطة</p><p class="text-2xl font-semibold text-gray-900"${_scopeId}>${ssrInterpolate(__props.stats?.active_operations || 0)}</p></div></div></div></div></div><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><div class="p-6 text-gray-900"${_scopeId}><h3 class="text-lg font-medium mb-4"${_scopeId}>مرحباً بك في نظام إدارة ميناء اللاذقية</h3><p class="text-gray-600 mb-4"${_scopeId}> نظام شامل لإدارة جميع عمليات الميناء من وصول السفن وحتى مغادرتها، مع إدارة متكاملة للوكلاء الملاحيين والأرصفة والعمليات المالية. </p><div class="grid grid-cols-1 md:grid-cols-3 gap-4"${_scopeId}><div class="text-center p-4 bg-blue-50 rounded-lg"${_scopeId}><h4 class="font-medium text-blue-900 mb-2"${_scopeId}>إدارة السفن والرحلات</h4><p class="text-sm text-blue-700"${_scopeId}>تتبع شامل لحركة السفن والرحلات</p></div><div class="text-center p-4 bg-green-50 rounded-lg"${_scopeId}><h4 class="font-medium text-green-900 mb-2"${_scopeId}>إدارة الأرصفة</h4><p class="text-sm text-green-700"${_scopeId}>حجز وإدارة الأرصفة بكفاءة</p></div><div class="text-center p-4 bg-purple-50 rounded-lg"${_scopeId}><h4 class="font-medium text-purple-900 mb-2"${_scopeId}>النظام المالي</h4><p class="text-sm text-purple-700"${_scopeId}>إدارة الفواتير والمدفوعات</p></div></div></div></div></div></div>`);
          } else {
            return [
              createVNode("div", { class: "py-12" }, [
                createVNode("div", { class: "mx-auto max-w-7xl sm:px-6 lg:px-8" }, [
                  createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8" }, [
                    createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                      createVNode("div", { class: "p-6" }, [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", { class: "flex-shrink-0" }, [
                            createVNode("div", { class: "w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center" }, [
                              (openBlock(), createBlock("svg", {
                                class: "w-4 h-4 text-white",
                                fill: "currentColor",
                                viewBox: "0 0 20 20"
                              }, [
                                createVNode("path", { d: "M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" })
                              ]))
                            ])
                          ]),
                          createVNode("div", { class: "ml-4" }, [
                            createVNode("p", { class: "text-sm font-medium text-gray-500" }, "السفن في الميناء"),
                            createVNode("p", { class: "text-2xl font-semibold text-gray-900" }, toDisplayString(__props.stats?.ships_in_port || 0), 1)
                          ])
                        ])
                      ])
                    ]),
                    createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                      createVNode("div", { class: "p-6" }, [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", { class: "flex-shrink-0" }, [
                            createVNode("div", { class: "w-8 h-8 bg-green-500 rounded-full flex items-center justify-center" }, [
                              (openBlock(), createBlock("svg", {
                                class: "w-4 h-4 text-white",
                                fill: "currentColor",
                                viewBox: "0 0 20 20"
                              }, [
                                createVNode("path", {
                                  "fill-rule": "evenodd",
                                  d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
                                  "clip-rule": "evenodd"
                                })
                              ]))
                            ])
                          ]),
                          createVNode("div", { class: "ml-4" }, [
                            createVNode("p", { class: "text-sm font-medium text-gray-500" }, "متوقعة اليوم"),
                            createVNode("p", { class: "text-2xl font-semibold text-gray-900" }, toDisplayString(__props.stats?.expected_today || 0), 1)
                          ])
                        ])
                      ])
                    ]),
                    createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                      createVNode("div", { class: "p-6" }, [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", { class: "flex-shrink-0" }, [
                            createVNode("div", { class: "w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center" }, [
                              (openBlock(), createBlock("svg", {
                                class: "w-4 h-4 text-white",
                                fill: "currentColor",
                                viewBox: "0 0 20 20"
                              }, [
                                createVNode("path", {
                                  "fill-rule": "evenodd",
                                  d: "M3 17a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1zM3 7a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1V7zM3 12a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1z",
                                  "clip-rule": "evenodd"
                                })
                              ]))
                            ])
                          ]),
                          createVNode("div", { class: "ml-4" }, [
                            createVNode("p", { class: "text-sm font-medium text-gray-500" }, "مغادرة اليوم"),
                            createVNode("p", { class: "text-2xl font-semibold text-gray-900" }, toDisplayString(__props.stats?.departing_today || 0), 1)
                          ])
                        ])
                      ])
                    ]),
                    createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                      createVNode("div", { class: "p-6" }, [
                        createVNode("div", { class: "flex items-center" }, [
                          createVNode("div", { class: "flex-shrink-0" }, [
                            createVNode("div", { class: "w-8 h-8 bg-red-500 rounded-full flex items-center justify-center" }, [
                              (openBlock(), createBlock("svg", {
                                class: "w-4 h-4 text-white",
                                fill: "currentColor",
                                viewBox: "0 0 20 20"
                              }, [
                                createVNode("path", {
                                  "fill-rule": "evenodd",
                                  d: "M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",
                                  "clip-rule": "evenodd"
                                })
                              ]))
                            ])
                          ]),
                          createVNode("div", { class: "ml-4" }, [
                            createVNode("p", { class: "text-sm font-medium text-gray-500" }, "العمليات النشطة"),
                            createVNode("p", { class: "text-2xl font-semibold text-gray-900" }, toDisplayString(__props.stats?.active_operations || 0), 1)
                          ])
                        ])
                      ])
                    ])
                  ]),
                  createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                    createVNode("div", { class: "p-6 text-gray-900" }, [
                      createVNode("h3", { class: "text-lg font-medium mb-4" }, "مرحباً بك في نظام إدارة ميناء اللاذقية"),
                      createVNode("p", { class: "text-gray-600 mb-4" }, " نظام شامل لإدارة جميع عمليات الميناء من وصول السفن وحتى مغادرتها، مع إدارة متكاملة للوكلاء الملاحيين والأرصفة والعمليات المالية. "),
                      createVNode("div", { class: "grid grid-cols-1 md:grid-cols-3 gap-4" }, [
                        createVNode("div", { class: "text-center p-4 bg-blue-50 rounded-lg" }, [
                          createVNode("h4", { class: "font-medium text-blue-900 mb-2" }, "إدارة السفن والرحلات"),
                          createVNode("p", { class: "text-sm text-blue-700" }, "تتبع شامل لحركة السفن والرحلات")
                        ]),
                        createVNode("div", { class: "text-center p-4 bg-green-50 rounded-lg" }, [
                          createVNode("h4", { class: "font-medium text-green-900 mb-2" }, "إدارة الأرصفة"),
                          createVNode("p", { class: "text-sm text-green-700" }, "حجز وإدارة الأرصفة بكفاءة")
                        ]),
                        createVNode("div", { class: "text-center p-4 bg-purple-50 rounded-lg" }, [
                          createVNode("h4", { class: "font-medium text-purple-900 mb-2" }, "النظام المالي"),
                          createVNode("p", { class: "text-sm text-purple-700" }, "إدارة الفواتير والمدفوعات")
                        ])
                      ])
                    ])
                  ])
                ])
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<!--]-->`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("resources/js/Pages/Dashboard.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
export {
  _sfc_main as default
};
