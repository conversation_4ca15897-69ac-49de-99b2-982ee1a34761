<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pilots', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('license_number')->unique();
            $table->string('full_name');
            $table->string('full_name_en')->nullable();
            $table->date('date_of_birth');
            $table->string('nationality');
            $table->string('phone');
            $table->string('mobile');
            $table->string('email')->unique();
            $table->text('address');
            $table->enum('status', ['active', 'inactive', 'on_leave', 'suspended'])->default('active');
            $table->enum('grade', ['junior', 'senior', 'chief', 'master'])->default('junior');
            $table->date('license_issue_date');
            $table->date('license_expiry_date');
            $table->json('certifications')->nullable(); // شهادات إضافية
            $table->json('ship_types_authorized')->nullable(); // أنواع السفن المخول لإرشادها
            $table->decimal('max_ship_length', 8, 2)->nullable(); // أقصى طول سفينة
            $table->decimal('max_ship_draft', 8, 2)->nullable(); // أقصى غاطس سفينة
            $table->integer('years_experience');
            $table->json('languages')->nullable(); // اللغات المتحدث بها
            $table->boolean('night_operations')->default(true); // عمليات ليلية
            $table->boolean('emergency_operations')->default(false); // عمليات طوارئ
            $table->decimal('hourly_rate', 8, 2)->nullable();
            $table->decimal('overtime_rate', 8, 2)->nullable();
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['status', 'grade']);
            $table->index('license_expiry_date');
            $table->index('license_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pilots');
    }
};
