import{m as y,c as a,o as d,a as r,b as n,h as M,w as c,d as t,t as i,f as w,p as C,v as j,k as z,g as B,F as x,i as b,n as h,q as u,l as f,s as S,x as V}from"./app-CdA4UbU6.js";import{_ as L}from"./AuthenticatedLayout-CBV1ADW9.js";import"./ApplicationLogo-WdDnJ3OK.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const T={class:"flex justify-between items-center"},H={class:"py-12"},D={class:"max-w-7xl mx-auto sm:px-6 lg:px-8"},N={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"},O={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},A={class:"p-6"},F={class:"flex items-center"},K={class:"ml-4"},U={class:"text-2xl font-semibold text-gray-900"},q={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},E={class:"p-6"},I={class:"flex items-center"},$={class:"ml-4"},G={class:"text-2xl font-semibold text-gray-900"},J={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},P={class:"p-6"},Q={class:"flex items-center"},R={class:"ml-4"},W={class:"text-2xl font-semibold text-gray-900"},X={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},Y={class:"p-6"},Z={class:"flex items-center"},tt={class:"ml-4"},et={class:"text-2xl font-semibold text-gray-900"},st={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},it={class:"p-6"},ot={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},lt={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},dt={class:"overflow-x-auto"},at={class:"min-w-full divide-y divide-gray-200"},rt={class:"bg-white divide-y divide-gray-200"},nt={class:"px-6 py-4 whitespace-nowrap"},ct={class:"text-sm font-medium text-gray-900"},xt={class:"text-sm text-gray-500"},ut={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},gt={class:"px-6 py-4 whitespace-nowrap"},mt={class:"text-sm text-gray-900"},pt={class:"text-sm text-gray-500"},ht={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ft={class:"px-6 py-4 whitespace-nowrap"},vt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},yt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},wt={class:"flex space-x-2"},bt={key:0,class:"px-6 py-3 border-t border-gray-200"},_t={class:"flex justify-between items-center"},kt={class:"text-sm text-gray-700"},Mt={class:"flex space-x-1"},Ct=["innerHTML"],Vt={__name:"Index",props:{agents:Object,statistics:Object,filters:Object},setup(o){const v=o,g=y(v.filters.search||""),m=y(v.filters.status||""),p=()=>{V.get(route("agents.index"),{search:g.value,status:m.value},{preserveState:!0,replace:!0})},_=l=>({active:"bg-green-100 text-green-800",inactive:"bg-gray-100 text-gray-800",suspended:"bg-red-100 text-red-800"})[l]||"bg-gray-100 text-gray-800",k=l=>({active:"نشط",inactive:"غير نشط",suspended:"معلق"})[l]||l;return(l,e)=>(d(),a(x,null,[r(n(M),{title:"الوكلاء الملاحيين"}),r(L,null,{header:c(()=>[t("div",T,[e[3]||(e[3]=t("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"}," الوكلاء الملاحيين ",-1)),r(n(u),{href:l.route("agents.create"),class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"},{default:c(()=>e[2]||(e[2]=[f(" إضافة وكيل جديد ",-1)])),_:1,__:[2]},8,["href"])])]),default:c(()=>[t("div",H,[t("div",D,[t("div",N,[t("div",O,[t("div",A,[t("div",F,[e[5]||(e[5]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",K,[e[4]||(e[4]=t("p",{class:"text-sm font-medium text-gray-500"},"إجمالي الوكلاء",-1)),t("p",U,i(o.statistics.total),1)])])])]),t("div",q,[t("div",E,[t("div",I,[e[7]||(e[7]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])])],-1)),t("div",$,[e[6]||(e[6]=t("p",{class:"text-sm font-medium text-gray-500"},"نشط",-1)),t("p",G,i(o.statistics.active),1)])])])]),t("div",J,[t("div",P,[t("div",Q,[e[9]||(e[9]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})])])],-1)),t("div",R,[e[8]||(e[8]=t("p",{class:"text-sm font-medium text-gray-500"},"تنتهي قريباً",-1)),t("p",W,i(o.statistics.expiring_soon),1)])])])]),t("div",X,[t("div",Y,[t("div",Z,[e[11]||(e[11]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"}),t("path",{d:"M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"})])])],-1)),t("div",tt,[e[10]||(e[10]=t("p",{class:"text-sm font-medium text-gray-500"},"معدل النشاط",-1)),t("p",et,i(o.statistics.active_percentage)+"%",1)])])])])]),t("div",st,[t("div",it,[t("div",ot,[t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"البحث",-1)),w(t("input",{"onUpdate:modelValue":e[0]||(e[0]=s=>g.value=s),onKeyup:C(p,["enter"]),type:"text",placeholder:"البحث بالاسم أو البريد الإلكتروني...",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},null,544),[[j,g.value]])]),t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"الحالة",-1)),w(t("select",{"onUpdate:modelValue":e[1]||(e[1]=s=>m.value=s),onChange:p,class:"w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},e[13]||(e[13]=[t("option",{value:""},"جميع الحالات",-1),t("option",{value:"active"},"نشط",-1),t("option",{value:"inactive"},"غير نشط",-1),t("option",{value:"suspended"},"معلق",-1)]),544),[[z,m.value]])]),t("div",{class:"flex items-end"},[t("button",{onClick:p,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full"}," بحث ")])])])]),t("div",lt,[t("div",dt,[t("table",at,[e[17]||(e[17]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," اسم الشركة "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," رقم الترخيص "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," الشخص المسؤول "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," البريد الإلكتروني "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," الحالة "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," انتهاء الترخيص "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," الإجراءات ")])],-1)),t("tbody",rt,[(d(!0),a(x,null,b(o.agents.data,s=>(d(),a("tr",{key:s.id,class:"hover:bg-gray-50"},[t("td",nt,[t("div",ct,i(s.company_name),1),t("div",xt,i(s.company_name_en),1)]),t("td",ut,i(s.license_number),1),t("td",gt,[t("div",mt,i(s.contact_person),1),t("div",pt,i(s.phone),1)]),t("td",ht,i(s.email),1),t("td",ft,[t("span",{class:h([_(s.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},i(k(s.status)),3)]),t("td",vt,i(new Date(s.license_expiry).toLocaleDateString("ar-SA")),1),t("td",yt,[t("div",wt,[r(n(u),{href:l.route("agents.show",s.id),class:"text-indigo-600 hover:text-indigo-900"},{default:c(()=>e[15]||(e[15]=[f(" عرض ",-1)])),_:2,__:[15]},1032,["href"]),r(n(u),{href:l.route("agents.edit",s.id),class:"text-green-600 hover:text-green-900"},{default:c(()=>e[16]||(e[16]=[f(" تعديل ",-1)])),_:2,__:[16]},1032,["href"])])])]))),128))])])]),o.agents.links?(d(),a("div",bt,[t("div",_t,[t("div",kt," عرض "+i(o.agents.from)+" إلى "+i(o.agents.to)+" من "+i(o.agents.total)+" نتيجة ",1),t("div",Mt,[(d(!0),a(x,null,b(o.agents.links,s=>(d(),a(x,{key:s.label},[s.url?(d(),S(n(u),{key:0,href:s.url,class:h(["px-3 py-2 text-sm rounded-md",s.active?"bg-blue-500 text-white":"bg-white text-gray-700 hover:bg-gray-50 border border-gray-300"]),innerHTML:s.label},null,8,["href","class","innerHTML"])):(d(),a("span",{key:1,class:h(["px-3 py-2 text-sm rounded-md","bg-gray-100 text-gray-400 cursor-not-allowed"]),innerHTML:s.label},null,8,Ct))],64))),128))])])])):B("",!0)])])])]),_:1})],64))}};export{Vt as default};
