import{_ as i}from"./AuthenticatedLayout-CBV1ADW9.js";import m from"./DeleteUserForm-DZOPve4k.js";import l from"./UpdatePasswordForm-BfBKn5N6.js";import r from"./UpdateProfileInformationForm-g6QRZnM5.js";import{c as d,o as n,a as t,b as c,h as p,w as o,d as s,F as _}from"./app-CdA4UbU6.js";import"./ApplicationLogo-WdDnJ3OK.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./TextInput-BBww1TQI.js";import"./PrimaryButton-BzUTxj76.js";const u={class:"py-12"},f={class:"mx-auto max-w-7xl space-y-6 sm:px-6 lg:px-8"},x={class:"bg-white p-4 shadow sm:rounded-lg sm:p-8"},h={class:"bg-white p-4 shadow sm:rounded-lg sm:p-8"},g={class:"bg-white p-4 shadow sm:rounded-lg sm:p-8"},N={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,e)=>(n(),d(_,null,[t(c(p),{title:"Profile"}),t(i,null,{header:o(()=>e[0]||(e[0]=[s("h2",{class:"text-xl font-semibold leading-tight text-gray-800"}," Profile ",-1)])),default:o(()=>[s("div",u,[s("div",f,[s("div",x,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",h,[t(l,{class:"max-w-xl"})]),s("div",g,[t(m,{class:"max-w-xl"})])])])]),_:1})],64))}};export{N as default};
