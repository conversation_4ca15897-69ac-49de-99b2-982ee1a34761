<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class PortAlert extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'alert_number',
        'type',
        'priority',
        'title',
        'message',
        'recipients',
        'channels',
        'alertable_type',
        'alertable_id',
        'status',
        'scheduled_at',
        'sent_at',
        'expires_at',
        'delivery_status',
        'is_urgent',
        'requires_acknowledgment',
        'acknowledged_by',
        'created_by',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
        'expires_at' => 'datetime',
        'recipients' => 'array',
        'channels' => 'array',
        'delivery_status' => 'array',
        'is_urgent' => 'boolean',
        'requires_acknowledgment' => 'boolean',
        'acknowledged_by' => 'array',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'pending',
        'priority' => 'normal',
        'is_urgent' => false,
        'requires_acknowledgment' => false,
    ];

    // العلاقات
    public function alertable(): MorphTo
    {
        return $this->morphTo();
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // النطاقات
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeUrgent($query)
    {
        return $query->where('is_urgent', true);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    public function scopeScheduledToday($query)
    {
        return $query->whereDate('scheduled_at', today());
    }

    public function scopeExpired($query)
    {
        return $query->whereNotNull('expires_at')
            ->where('expires_at', '<', now());
    }

    public function scopeActive($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>=', now());
        });
    }

    public function scopeRequiringAcknowledgment($query)
    {
        return $query->where('requires_acknowledgment', true);
    }

    // الخصائص المحسوبة
    public function getIsSentAttribute(): bool
    {
        return $this->status === 'sent';
    }

    public function getIsPendingAttribute(): bool
    {
        return $this->status === 'pending';
    }

    public function getIsExpiredAttribute(): bool
    {
        return $this->expires_at && $this->expires_at < now();
    }

    public function getIsUrgentAttribute(): bool
    {
        return $this->attributes['is_urgent'] ?? false;
    }

    public function getIsCriticalAttribute(): bool
    {
        return $this->priority === 'critical';
    }

    public function getAcknowledgmentRateAttribute(): float
    {
        if (!$this->requires_acknowledgment || empty($this->recipients)) {
            return 100.0;
        }

        $totalRecipients = count($this->recipients);
        $acknowledgedCount = count($this->acknowledged_by ?? []);

        return ($acknowledgedCount / $totalRecipients) * 100;
    }

    public function getDeliveryRateAttribute(): float
    {
        if (empty($this->delivery_status)) {
            return 0.0;
        }

        $totalChannels = count($this->channels);
        $deliveredChannels = 0;

        foreach ($this->delivery_status as $status) {
            if ($status === 'delivered') {
                $deliveredChannels++;
            }
        }

        return $totalChannels > 0 ? ($deliveredChannels / $totalChannels) * 100 : 0.0;
    }

    public function getFormattedScheduledAtAttribute(): string
    {
        return $this->scheduled_at ?
            $this->scheduled_at->format('Y-m-d H:i') :
            'Not scheduled';
    }

    public function getPriorityColorAttribute(): string
    {
        return match($this->priority) {
            'critical' => 'red',
            'high' => 'orange',
            'normal' => 'blue',
            'low' => 'green',
            default => 'gray'
        };
    }

    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'yellow',
            'sent' => 'green',
            'delivered' => 'blue',
            'failed' => 'red',
            default => 'gray'
        };
    }

    // دوال مساعدة
    public function canBeSent(): bool
    {
        return $this->status === 'pending' &&
               (!$this->scheduled_at || $this->scheduled_at <= now());
    }

    public function canBeEdited(): bool
    {
        return $this->status === 'pending';
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'sent']);
    }

    public function markAsSent(): bool
    {
        return $this->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);
    }

    public function markAsDelivered(): bool
    {
        return $this->update(['status' => 'delivered']);
    }

    public function markAsFailed(?string $reason = null): bool
    {
        return $this->update([
            'status' => 'failed',
            'notes' => $reason ? $this->notes . "\nFailure reason: " . $reason : $this->notes,
        ]);
    }

    public function acknowledge(User $user): bool
    {
        if (!$this->requires_acknowledgment) {
            return false;
        }

        $acknowledgedBy = $this->acknowledged_by ?? [];

        if (!in_array($user->id, $acknowledgedBy)) {
            $acknowledgedBy[] = [
                'user_id' => $user->id,
                'acknowledged_at' => now()->toISOString(),
            ];

            return $this->update(['acknowledged_by' => $acknowledgedBy]);
        }

        return true;
    }

    public function isAcknowledgedBy(User $user): bool
    {
        if (!$this->requires_acknowledgment) {
            return true;
        }

        $acknowledgedBy = $this->acknowledged_by ?? [];

        foreach ($acknowledgedBy as $acknowledgment) {
            if ($acknowledgment['user_id'] === $user->id) {
                return true;
            }
        }

        return false;
    }

    public function updateDeliveryStatus(string $channel, string $status): bool
    {
        $deliveryStatus = $this->delivery_status ?? [];
        $deliveryStatus[$channel] = $status;

        return $this->update(['delivery_status' => $deliveryStatus]);
    }

    public static function generateAlertNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');

        $lastAlert = self::whereYear('created_at', $year)
            ->whereMonth('created_at', now()->month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastAlert ?
            (int) substr($lastAlert->alert_number, -4) + 1 :
            1;

        return sprintf('ALT-%s%s-%04d', $year, $month, $sequence);
    }

    public static function createForVoyage(
        Voyage $voyage,
        string $type,
        string $title,
        string $message,
        array $recipients = [],
        array $channels = ['database', 'broadcast'],
        string $priority = 'normal'
    ): self {
        return self::create([
            'alert_number' => self::generateAlertNumber(),
            'type' => $type,
            'priority' => $priority,
            'title' => $title,
            'message' => $message,
            'recipients' => $recipients,
            'channels' => $channels,
            'alertable_type' => Voyage::class,
            'alertable_id' => $voyage->id,
            'created_by' => Auth::id(),
        ]);
    }

    public static function createSystemAlert(
        string $title,
        string $message,
        array $recipients = [],
        string $priority = 'normal'
    ): self {
        return self::create([
            'alert_number' => self::generateAlertNumber(),
            'type' => 'system',
            'priority' => $priority,
            'title' => $title,
            'message' => $message,
            'recipients' => $recipients,
            'channels' => ['database', 'broadcast', 'email'],
            'created_by' => Auth::id(),
        ]);
    }

    public static function createEmergencyAlert(
        string $title,
        string $message,
        array $recipients = []
    ): self {
        return self::create([
            'alert_number' => self::generateAlertNumber(),
            'type' => 'emergency',
            'priority' => 'critical',
            'title' => $title,
            'message' => $message,
            'recipients' => $recipients,
            'channels' => ['database', 'broadcast', 'email', 'sms'],
            'is_urgent' => true,
            'requires_acknowledgment' => true,
            'created_by' => Auth::id(),
        ]);
    }
}
