<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('operation_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('operation_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->datetime('logged_at');
            $table->enum('event_type', ['start', 'pause', 'resume', 'complete', 'cancel', 'update', 'incident', 'note']);
            $table->string('title');
            $table->text('description')->nullable();
            $table->json('data_before')->nullable(); // البيانات قبل التغيير
            $table->json('data_after')->nullable(); // البيانات بعد التغيير
            $table->decimal('quantity_processed', 12, 2)->nullable();
            $table->string('location')->nullable(); // موقع الحدث
            $table->json('weather_conditions')->nullable(); // ظروف الطقس
            $table->json('equipment_status')->nullable(); // حالة المعدات
            $table->enum('severity', ['info', 'warning', 'error', 'critical'])->default('info');
            $table->boolean('requires_attention')->default(false);
            $table->json('attachments')->nullable(); // مرفقات (صور، ملفات)
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['operation_id', 'logged_at']);
            $table->index(['event_type', 'severity']);
            $table->index('requires_attention');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('operation_logs');
    }
};
