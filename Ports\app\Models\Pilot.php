<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Pilot extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'license_number',
        'full_name',
        'full_name_en',
        'date_of_birth',
        'nationality',
        'phone',
        'mobile',
        'email',
        'address',
        'status',
        'grade',
        'license_issue_date',
        'license_expiry_date',
        'certifications',
        'ship_types_authorized',
        'max_ship_length',
        'max_ship_draft',
        'years_experience',
        'languages',
        'night_operations',
        'emergency_operations',
        'hourly_rate',
        'overtime_rate',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'license_issue_date' => 'date',
        'license_expiry_date' => 'date',
        'certifications' => 'array',
        'ship_types_authorized' => 'array',
        'max_ship_length' => 'decimal:2',
        'max_ship_draft' => 'decimal:2',
        'years_experience' => 'integer',
        'languages' => 'array',
        'night_operations' => 'boolean',
        'emergency_operations' => 'boolean',
        'hourly_rate' => 'decimal:2',
        'overtime_rate' => 'decimal:2',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'active',
        'grade' => 'junior',
        'night_operations' => true,
        'emergency_operations' => false,
    ];

    // العلاقات
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function assignments(): HasMany
    {
        return $this->hasMany(PilotAssignment::class);
    }

    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    // النطاقات
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeAvailable($query)
    {
        return $query->where('status', 'active')
            ->whereDoesntHave('assignments', function ($q) {
                $q->whereIn('status', ['assigned', 'confirmed', 'boarding', 'piloting'])
                  ->where('scheduled_time', '<=', now()->addHours(2))
                  ->where('scheduled_time', '>=', now()->subHours(6));
            });
    }

    public function scopeByGrade($query, $grade)
    {
        return $query->where('grade', $grade);
    }

    public function scopeCanHandleShip($query, $length, $draft, $shipType)
    {
        return $query->where('max_ship_length', '>=', $length)
            ->where('max_ship_draft', '>=', $draft)
            ->where(function ($q) use ($shipType) {
                $q->whereNull('ship_types_authorized')
                  ->orWhereJsonContains('ship_types_authorized', $shipType);
            });
    }

    public function scopeForNightOperations($query)
    {
        return $query->where('night_operations', true);
    }

    public function scopeForEmergency($query)
    {
        return $query->where('emergency_operations', true);
    }

    public function scopeLicenseExpiring($query, $days = 30)
    {
        return $query->where('license_expiry_date', '<=', now()->addDays($days))
            ->where('license_expiry_date', '>', now());
    }

    // الخصائص المحسوبة
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active';
    }

    public function getIsAvailableAttribute(): bool
    {
        return $this->is_active && !$this->hasActiveAssignment();
    }

    public function getAgeAttribute(): int
    {
        return $this->date_of_birth->age;
    }

    public function getFullNameWithGradeAttribute(): string
    {
        return $this->full_name . ' (' . ucfirst($this->grade) . ')';
    }

    public function getIsLicenseValidAttribute(): bool
    {
        return $this->license_expiry_date > now();
    }

    public function getIsLicenseExpiringSoonAttribute(): bool
    {
        return $this->license_expiry_date <= now()->addDays(30) &&
               $this->license_expiry_date > now();
    }

    public function getDaysUntilLicenseExpiryAttribute(): int
    {
        return now()->diffInDays($this->license_expiry_date, false);
    }

    public function getTotalAssignmentsAttribute(): int
    {
        return $this->assignments()->count();
    }

    public function getCompletedAssignmentsAttribute(): int
    {
        return $this->assignments()->where('status', 'completed')->count();
    }

    public function getSuccessRateAttribute(): float
    {
        $total = $this->total_assignments;
        if ($total === 0) {
            return 100.0;
        }

        return ($this->completed_assignments / $total) * 100;
    }

    public function getAverageAssignmentDurationAttribute(): ?float
    {
        $completedAssignments = $this->assignments()
            ->where('status', 'completed')
            ->whereNotNull('actual_duration')
            ->get();

        if ($completedAssignments->isEmpty()) {
            return null;
        }

        return $completedAssignments->avg('actual_duration');
    }

    // دوال مساعدة
    public function canHandleShip(Ship $ship): bool
    {
        if (!$this->is_active || !$this->is_license_valid) {
            return false;
        }

        if ($ship->length > $this->max_ship_length ||
            $ship->draft > $this->max_ship_draft) {
            return false;
        }

        if ($this->ship_types_authorized &&
            !in_array($ship->ship_type, $this->ship_types_authorized)) {
            return false;
        }

        return true;
    }

    public function hasActiveAssignment(): bool
    {
        return $this->assignments()
            ->whereIn('status', ['assigned', 'confirmed', 'boarding', 'piloting'])
            ->where('scheduled_time', '<=', now()->addHours(2))
            ->where('scheduled_time', '>=', now()->subHours(6))
            ->exists();
    }

    public function getCurrentAssignment(): ?PilotAssignment
    {
        return $this->assignments()
            ->whereIn('status', ['assigned', 'confirmed', 'boarding', 'piloting'])
            ->where('scheduled_time', '<=', now()->addHours(2))
            ->where('scheduled_time', '>=', now()->subHours(6))
            ->first();
    }

    public function getUpcomingAssignments()
    {
        return $this->assignments()
            ->whereIn('status', ['assigned', 'confirmed'])
            ->where('scheduled_time', '>', now())
            ->orderBy('scheduled_time')
            ->get();
    }

    public function isAvailableForTime(\DateTime $scheduledTime): bool
    {
        if (!$this->is_available) {
            return false;
        }

        // التحقق من عدم وجود تكليف آخر في نفس الوقت (±2 ساعات)
        return !$this->assignments()
            ->whereIn('status', ['assigned', 'confirmed', 'boarding', 'piloting'])
            ->where('scheduled_time', '>=', $scheduledTime->modify('-2 hours'))
            ->where('scheduled_time', '<=', $scheduledTime->modify('+2 hours'))
            ->exists();
    }

    public function calculateFee(\DateTime $scheduledTime, float $duration): float
    {
        $isNightTime = $scheduledTime->format('H') < 6 || $scheduledTime->format('H') >= 18;
        $rate = $isNightTime ? $this->overtime_rate : $this->hourly_rate;

        return $rate * $duration;
    }
}
