<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Invoice extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'voyage_id',
        'agent_id',
        'invoice_number',
        'invoice_date',
        'due_date',
        'status',
        'type',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'currency',
        'exchange_rate',
        'line_items',
        'tax_breakdown',
        'discount_breakdown',
        'payment_terms',
        'notes',
        'internal_notes',
        'created_by',
        'approved_by',
        'approved_at',
        'pdf_path',
        'metadata',
    ];

    protected $casts = [
        'invoice_date' => 'date',
        'due_date' => 'date',
        'approved_at' => 'datetime',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'exchange_rate' => 'decimal:4',
        'line_items' => 'array',
        'tax_breakdown' => 'array',
        'discount_breakdown' => 'array',
        'pdf_path' => 'array',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'draft',
        'type' => 'final',
        'currency' => 'SYP',
        'exchange_rate' => 1,
        'tax_amount' => 0,
        'discount_amount' => 0,
    ];

    // العلاقات
    public function voyage(): BelongsTo
    {
        return $this->belongsTo(Voyage::class);
    }

    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    // النطاقات
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'sent')
            ->where('due_date', '<', now());
    }

    public function scopeDueToday($query)
    {
        return $query->where('status', 'sent')
            ->whereDate('due_date', today());
    }

    public function scopeByAgent($query, $agentId)
    {
        return $query->where('agent_id', $agentId);
    }

    public function scopeUnpaid($query)
    {
        return $query->whereIn('status', ['sent', 'overdue']);
    }

    // الخصائص المحسوبة
    public function getIsPaidAttribute(): bool
    {
        return $this->status === 'paid';
    }

    public function getIsOverdueAttribute(): bool
    {
        return $this->status === 'sent' && $this->due_date < now();
    }

    public function getAmountPaidAttribute(): float
    {
        return $this->payments()
            ->where('status', 'completed')
            ->sum('amount');
    }

    public function getAmountDueAttribute(): float
    {
        return max(0, $this->total_amount - $this->amount_paid);
    }

    public function getIsFullyPaidAttribute(): bool
    {
        return $this->amount_due <= 0.01; // تسامح صغير للأخطاء العشرية
    }

    public function getIsPartiallyPaidAttribute(): bool
    {
        return $this->amount_paid > 0 && !$this->is_fully_paid;
    }

    public function getDaysOverdueAttribute(): int
    {
        if ($this->is_overdue) {
            return $this->due_date->diffInDays(now());
        }
        return 0;
    }

    public function getDaysUntilDueAttribute(): int
    {
        if (!$this->is_overdue && $this->status === 'sent') {
            return now()->diffInDays($this->due_date);
        }
        return 0;
    }

    public function getFormattedTotalAttribute(): string
    {
        return number_format($this->total_amount, 2) . ' ' . $this->currency;
    }

    // دوال مساعدة
    public function addLineItem(string $description, float $quantity, float $unitPrice, ?string $feeCode = null): void
    {
        $lineItems = $this->line_items ?? [];

        $lineItems[] = [
            'description' => $description,
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'total' => $quantity * $unitPrice,
            'fee_code' => $feeCode,
        ];

        $this->line_items = $lineItems;
        $this->recalculateTotal();
    }

    public function recalculateTotal(): void
    {
        $subtotal = 0;

        foreach ($this->line_items ?? [] as $item) {
            $subtotal += $item['total'] ?? 0;
        }

        $this->subtotal = $subtotal;
        $this->total_amount = $subtotal + $this->tax_amount - $this->discount_amount;
    }

    public function markAsPaid(): bool
    {
        if ($this->is_fully_paid) {
            return $this->update(['status' => 'paid']);
        }
        return false;
    }

    public function markAsOverdue(): bool
    {
        if ($this->is_overdue && $this->status === 'sent') {
            return $this->update(['status' => 'overdue']);
        }
        return false;
    }

    public function canBeEdited(): bool
    {
        return $this->status === 'draft';
    }

    public function canBeApproved(): bool
    {
        return $this->status === 'draft' && $this->total_amount > 0;
    }

    public function canBeSent(): bool
    {
        return $this->status === 'draft' && $this->approved_at;
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['draft', 'sent']) && !$this->is_partially_paid;
    }

    public static function generateInvoiceNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');

        $lastInvoice = self::whereYear('created_at', $year)
            ->whereMonth('created_at', now()->month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastInvoice ?
            (int) substr($lastInvoice->invoice_number, -4) + 1 :
            1;

        return sprintf('INV-%s%s-%04d', $year, $month, $sequence);
    }
}
