<?php

namespace App\Repositories;

use App\Models\Agent;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;

class AgentRepository extends BaseRepository
{
    protected function getModel(): Model
    {
        return new Agent();
    }

    /**
     * Get active agents
     */
    public function getActive(): Collection
    {
        return $this->scope('active')->all();
    }

    /**
     * Get agents with expiring licenses
     */
    public function getExpiringSoon(int $days = 30): Collection
    {
        return $this->scope('expiringSoon', $days)->all();
    }

    /**
     * Find agent by license number
     */
    public function findByLicenseNumber(string $licenseNumber): ?Agent
    {
        return $this->findBy('license_number', $licenseNumber);
    }

    /**
     * Find agent by email
     */
    public function findByEmail(string $email): ?Agent
    {
        return $this->findBy('email', $email);
    }

    /**
     * Get agents with their users
     */
    public function getWithUsers(): Collection
    {
        return $this->with(['user'])->all();
    }

    /**
     * Get agents with their voyages count
     */
    public function getWithVoyagesCount(): Collection
    {
        return $this->query
            ->withCount('voyages')
            ->get();
    }

    /**
     * Search agents by company name or contact person
     */
    public function searchByName(string $term): Collection
    {
        return $this->search($term, ['company_name', 'company_name_en', 'contact_person', 'contact_person_en'])
            ->all();
    }

    /**
     * Get agents by status
     */
    public function getByStatus(string $status): Collection
    {
        return $this->where('status', $status)->all();
    }

    /**
     * Get agents providing specific service
     */
    public function getByService(string $service): Collection
    {
        return $this->query
            ->whereJsonContains('services', $service)
            ->get();
    }

    /**
     * Update agent status
     */
    public function updateStatus(int $agentId, string $status): bool
    {
        return $this->update($agentId, ['status' => $status]);
    }

    /**
     * Get agents statistics
     */
    public function getStatistics(): array
    {
        $total = $this->count();
        $active = $this->where('status', 'active')->count();
        $inactive = $this->where('status', 'inactive')->count();
        $suspended = $this->where('status', 'suspended')->count();
        $expiringSoon = $this->scope('expiringSoon', 30)->count();

        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $inactive,
            'suspended' => $suspended,
            'expiring_soon' => $expiringSoon,
            'active_percentage' => $total > 0 ? round(($active / $total) * 100, 2) : 0,
        ];
    }

    /**
     * Get agents with recent activity
     */
    public function getWithRecentActivity(int $days = 30): Collection
    {
        return $this->query
            ->whereHas('voyages', function ($query) use ($days) {
                $query->where('created_at', '>=', now()->subDays($days));
            })
            ->with(['voyages' => function ($query) use ($days) {
                $query->where('created_at', '>=', now()->subDays($days))
                    ->latest();
            }])
            ->get();
    }

    /**
     * Create agent with user
     */
    public function createWithUser(array $agentData, array $userData): Agent
    {
        return $this->transaction(function () use ($agentData, $userData) {
            // Create user first
            $user = \App\Models\User::create($userData);
            
            // Assign agent role
            $user->assignRole('agent');
            
            // Create agent
            $agentData['user_id'] = $user->id;
            return $this->create($agentData);
        });
    }

    /**
     * Get agents for dropdown/select
     */
    public function getForSelect(): Collection
    {
        return $this->query
            ->select('id', 'company_name', 'status')
            ->where('status', 'active')
            ->orderBy('company_name')
            ->get()
            ->map(function ($agent) {
                return [
                    'id' => $agent->id,
                    'name' => $agent->company_name,
                    'status' => $agent->status,
                ];
            });
    }

    /**
     * Get agents with their financial summary
     */
    public function getWithFinancialSummary(): Collection
    {
        return $this->query
            ->with(['invoices' => function ($query) {
                $query->selectRaw('agent_id, COUNT(*) as total_invoices, SUM(total_amount) as total_amount, SUM(CASE WHEN status = "paid" THEN total_amount ELSE 0 END) as paid_amount')
                    ->groupBy('agent_id');
            }])
            ->get();
    }

    /**
     * Bulk update agent statuses
     */
    public function bulkUpdateStatus(array $agentIds, string $status): int
    {
        return $this->model->whereIn('id', $agentIds)->update(['status' => $status]);
    }

    /**
     * Get agents requiring license renewal
     */
    public function getRequiringRenewal(): Collection
    {
        return $this->query
            ->where('license_expiry', '<=', now()->addDays(60))
            ->where('status', 'active')
            ->orderBy('license_expiry')
            ->get();
    }
}
