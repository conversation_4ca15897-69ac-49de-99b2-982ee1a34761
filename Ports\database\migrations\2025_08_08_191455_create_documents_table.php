<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('documents', function (Blueprint $table) {
            $table->id();
            $table->string('document_number')->unique();
            $table->string('title');
            $table->string('title_en')->nullable();
            $table->enum('type', ['certificate', 'permit', 'invoice', 'receipt', 'report', 'manifest', 'clearance', 'other']);
            $table->enum('category', ['ship', 'cargo', 'crew', 'passenger', 'port', 'customs', 'health', 'security']);
            $table->morphs('documentable'); // علاقة polymorphic
            $table->string('file_path');
            $table->string('file_name');
            $table->string('file_type');
            $table->integer('file_size');
            $table->string('mime_type');
            $table->string('hash')->nullable(); // hash للتحقق من سلامة الملف
            $table->enum('status', ['draft', 'submitted', 'approved', 'rejected', 'expired'])->default('draft');
            $table->date('issue_date')->nullable();
            $table->date('expiry_date')->nullable();
            $table->string('issuing_authority')->nullable();
            $table->string('reference_number')->nullable();
            $table->boolean('is_required')->default(false);
            $table->boolean('is_verified')->default(false);
            $table->datetime('verified_at')->nullable();
            $table->foreignId('verified_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('verification_notes')->nullable();
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');
            $table->json('access_permissions')->nullable(); // صلاحيات الوصول
            $table->boolean('is_confidential')->default(false);
            $table->text('description')->nullable();
            $table->json('tags')->nullable(); // علامات للبحث
            $table->integer('version')->default(1);
            $table->foreignId('parent_document_id')->nullable()->constrained('documents')->onDelete('set null');
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['type', 'category']);
            $table->index(['status', 'expiry_date']);
            $table->index('document_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('documents');
    }
};
