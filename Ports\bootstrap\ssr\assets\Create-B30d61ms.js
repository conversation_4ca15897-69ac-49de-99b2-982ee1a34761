import { resolveComponent, unref, withCtx, createTextVNode, createVNode, withModifiers, withDirectives, createBlock, createCommentVNode, vModelText, openBlock, toDisplayString, Fragment, renderList, vModelCheckbox, vModelSelect, useSSRContext } from "vue";
import { ssrRenderComponent, ssrRenderAttr, ssrRenderClass, ssrInterpolate, ssrRenderList, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual } from "vue/server-renderer";
import { _ as _sfc_main$1 } from "./AuthenticatedLayout-DMuuhipg.js";
import { useForm, Head } from "@inertiajs/vue3";
import "./ApplicationLogo-B2173abF.js";
import "./_plugin-vue_export-helper-1tPrXgE0.js";
const _sfc_main = {
  __name: "Create",
  __ssrInlineRender: true,
  setup(__props) {
    const form = useForm({
      company_name: "",
      company_name_en: "",
      license_number: "",
      email: "",
      phone: "",
      address: "",
      address_en: "",
      contact_person: "",
      contact_person_en: "",
      license_expiry: "",
      services: [],
      notes: "",
      status: "active"
    });
    const availableServices = [
      "ship_agency",
      "cargo_handling",
      "customs_clearance",
      "crew_services",
      "supply_services",
      "waste_management",
      "bunker_services",
      "repair_services"
    ];
    const getServiceName = (service) => {
      const names = {
        "ship_agency": "وكالة ملاحية",
        "cargo_handling": "مناولة البضائع",
        "customs_clearance": "تخليص جمركي",
        "crew_services": "خدمات الطاقم",
        "supply_services": "خدمات التموين",
        "waste_management": "إدارة النفايات",
        "bunker_services": "خدمات الوقود",
        "repair_services": "خدمات الإصلاح"
      };
      return names[service] || service;
    };
    const submit = () => {
      form.post(route("agents.store"));
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_Link = resolveComponent("Link");
      _push(`<!--[-->`);
      _push(ssrRenderComponent(unref(Head), { title: "إضافة وكيل ملاحي جديد" }, null, _parent));
      _push(ssrRenderComponent(_sfc_main$1, null, {
        header: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<h2 class="font-semibold text-xl text-gray-800 leading-tight"${_scopeId}> إضافة وكيل ملاحي جديد </h2>`);
          } else {
            return [
              createVNode("h2", { class: "font-semibold text-xl text-gray-800 leading-tight" }, " إضافة وكيل ملاحي جديد ")
            ];
          }
        }),
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<div class="py-12"${_scopeId}><div class="max-w-4xl mx-auto sm:px-6 lg:px-8"${_scopeId}><div class="bg-white overflow-hidden shadow-sm sm:rounded-lg"${_scopeId}><form class="p-6 space-y-6"${_scopeId}><div${_scopeId}><h3 class="text-lg font-medium text-gray-900 mb-4"${_scopeId}>معلومات الشركة</h3><div class="grid grid-cols-1 md:grid-cols-2 gap-6"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}> اسم الشركة (عربي) * </label><input${ssrRenderAttr("value", unref(form).company_name)} type="text" required class="${ssrRenderClass([{ "border-red-500": unref(form).errors.company_name }, "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"])}"${_scopeId}>`);
            if (unref(form).errors.company_name) {
              _push2(`<div class="text-red-500 text-sm mt-1"${_scopeId}>${ssrInterpolate(unref(form).errors.company_name)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}> اسم الشركة (إنجليزي) </label><input${ssrRenderAttr("value", unref(form).company_name_en)} type="text" class="${ssrRenderClass([{ "border-red-500": unref(form).errors.company_name_en }, "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"])}"${_scopeId}>`);
            if (unref(form).errors.company_name_en) {
              _push2(`<div class="text-red-500 text-sm mt-1"${_scopeId}>${ssrInterpolate(unref(form).errors.company_name_en)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}> رقم الترخيص * </label><input${ssrRenderAttr("value", unref(form).license_number)} type="text" required class="${ssrRenderClass([{ "border-red-500": unref(form).errors.license_number }, "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"])}"${_scopeId}>`);
            if (unref(form).errors.license_number) {
              _push2(`<div class="text-red-500 text-sm mt-1"${_scopeId}>${ssrInterpolate(unref(form).errors.license_number)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}> تاريخ انتهاء الترخيص * </label><input${ssrRenderAttr("value", unref(form).license_expiry)} type="date" required class="${ssrRenderClass([{ "border-red-500": unref(form).errors.license_expiry }, "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"])}"${_scopeId}>`);
            if (unref(form).errors.license_expiry) {
              _push2(`<div class="text-red-500 text-sm mt-1"${_scopeId}>${ssrInterpolate(unref(form).errors.license_expiry)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div></div><div${_scopeId}><h3 class="text-lg font-medium text-gray-900 mb-4"${_scopeId}>معلومات الاتصال</h3><div class="grid grid-cols-1 md:grid-cols-2 gap-6"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}> الشخص المسؤول (عربي) * </label><input${ssrRenderAttr("value", unref(form).contact_person)} type="text" required class="${ssrRenderClass([{ "border-red-500": unref(form).errors.contact_person }, "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"])}"${_scopeId}>`);
            if (unref(form).errors.contact_person) {
              _push2(`<div class="text-red-500 text-sm mt-1"${_scopeId}>${ssrInterpolate(unref(form).errors.contact_person)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}> الشخص المسؤول (إنجليزي) </label><input${ssrRenderAttr("value", unref(form).contact_person_en)} type="text" class="${ssrRenderClass([{ "border-red-500": unref(form).errors.contact_person_en }, "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"])}"${_scopeId}>`);
            if (unref(form).errors.contact_person_en) {
              _push2(`<div class="text-red-500 text-sm mt-1"${_scopeId}>${ssrInterpolate(unref(form).errors.contact_person_en)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}> البريد الإلكتروني * </label><input${ssrRenderAttr("value", unref(form).email)} type="email" required class="${ssrRenderClass([{ "border-red-500": unref(form).errors.email }, "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"])}"${_scopeId}>`);
            if (unref(form).errors.email) {
              _push2(`<div class="text-red-500 text-sm mt-1"${_scopeId}>${ssrInterpolate(unref(form).errors.email)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}> رقم الهاتف * </label><input${ssrRenderAttr("value", unref(form).phone)} type="tel" required class="${ssrRenderClass([{ "border-red-500": unref(form).errors.phone }, "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"])}"${_scopeId}>`);
            if (unref(form).errors.phone) {
              _push2(`<div class="text-red-500 text-sm mt-1"${_scopeId}>${ssrInterpolate(unref(form).errors.phone)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div></div><div${_scopeId}><h3 class="text-lg font-medium text-gray-900 mb-4"${_scopeId}>معلومات العنوان</h3><div class="grid grid-cols-1 md:grid-cols-2 gap-6"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}> العنوان (عربي) * </label><textarea rows="3" required class="${ssrRenderClass([{ "border-red-500": unref(form).errors.address }, "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"])}"${_scopeId}>${ssrInterpolate(unref(form).address)}</textarea>`);
            if (unref(form).errors.address) {
              _push2(`<div class="text-red-500 text-sm mt-1"${_scopeId}>${ssrInterpolate(unref(form).errors.address)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}> العنوان (إنجليزي) </label><textarea rows="3" class="${ssrRenderClass([{ "border-red-500": unref(form).errors.address_en }, "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"])}"${_scopeId}>${ssrInterpolate(unref(form).address_en)}</textarea>`);
            if (unref(form).errors.address_en) {
              _push2(`<div class="text-red-500 text-sm mt-1"${_scopeId}>${ssrInterpolate(unref(form).errors.address_en)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div></div><div${_scopeId}><h3 class="text-lg font-medium text-gray-900 mb-4"${_scopeId}>الخدمات المقدمة</h3><div class="grid grid-cols-2 md:grid-cols-4 gap-4"${_scopeId}><!--[-->`);
            ssrRenderList(availableServices, (service) => {
              _push2(`<div class="flex items-center"${_scopeId}><input${ssrRenderAttr("id", service)}${ssrIncludeBooleanAttr(Array.isArray(unref(form).services) ? ssrLooseContain(unref(form).services, service) : unref(form).services) ? " checked" : ""}${ssrRenderAttr("value", service)} type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"${_scopeId}><label${ssrRenderAttr("for", service)} class="mr-2 text-sm text-gray-700"${_scopeId}>${ssrInterpolate(getServiceName(service))}</label></div>`);
            });
            _push2(`<!--]--></div>`);
            if (unref(form).errors.services) {
              _push2(`<div class="text-red-500 text-sm mt-1"${_scopeId}>${ssrInterpolate(unref(form).errors.services)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div><div class="grid grid-cols-1 md:grid-cols-2 gap-6"${_scopeId}><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}> الحالة </label><select class="${ssrRenderClass([{ "border-red-500": unref(form).errors.status }, "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"])}"${_scopeId}><option value="active"${ssrIncludeBooleanAttr(Array.isArray(unref(form).status) ? ssrLooseContain(unref(form).status, "active") : ssrLooseEqual(unref(form).status, "active")) ? " selected" : ""}${_scopeId}>نشط</option><option value="inactive"${ssrIncludeBooleanAttr(Array.isArray(unref(form).status) ? ssrLooseContain(unref(form).status, "inactive") : ssrLooseEqual(unref(form).status, "inactive")) ? " selected" : ""}${_scopeId}>غير نشط</option><option value="suspended"${ssrIncludeBooleanAttr(Array.isArray(unref(form).status) ? ssrLooseContain(unref(form).status, "suspended") : ssrLooseEqual(unref(form).status, "suspended")) ? " selected" : ""}${_scopeId}>معلق</option></select>`);
            if (unref(form).errors.status) {
              _push2(`<div class="text-red-500 text-sm mt-1"${_scopeId}>${ssrInterpolate(unref(form).errors.status)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div><div${_scopeId}><label class="block text-sm font-medium text-gray-700 mb-2"${_scopeId}> ملاحظات </label><textarea rows="3" class="${ssrRenderClass([{ "border-red-500": unref(form).errors.notes }, "w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"])}"${_scopeId}>${ssrInterpolate(unref(form).notes)}</textarea>`);
            if (unref(form).errors.notes) {
              _push2(`<div class="text-red-500 text-sm mt-1"${_scopeId}>${ssrInterpolate(unref(form).errors.notes)}</div>`);
            } else {
              _push2(`<!---->`);
            }
            _push2(`</div></div><div class="flex justify-end space-x-3 pt-6 border-t border-gray-200"${_scopeId}>`);
            _push2(ssrRenderComponent(_component_Link, {
              href: _ctx.route("agents.index"),
              class: "bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(` إلغاء `);
                } else {
                  return [
                    createTextVNode(" إلغاء ")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`<button type="submit"${ssrIncludeBooleanAttr(unref(form).processing) ? " disabled" : ""} class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"${_scopeId}>`);
            if (unref(form).processing) {
              _push2(`<span${_scopeId}>جاري الحفظ...</span>`);
            } else {
              _push2(`<span${_scopeId}>حفظ</span>`);
            }
            _push2(`</button></div></form></div></div></div>`);
          } else {
            return [
              createVNode("div", { class: "py-12" }, [
                createVNode("div", { class: "max-w-4xl mx-auto sm:px-6 lg:px-8" }, [
                  createVNode("div", { class: "bg-white overflow-hidden shadow-sm sm:rounded-lg" }, [
                    createVNode("form", {
                      onSubmit: withModifiers(submit, ["prevent"]),
                      class: "p-6 space-y-6"
                    }, [
                      createVNode("div", null, [
                        createVNode("h3", { class: "text-lg font-medium text-gray-900 mb-4" }, "معلومات الشركة"),
                        createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-6" }, [
                          createVNode("div", null, [
                            createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, " اسم الشركة (عربي) * "),
                            withDirectives(createVNode("input", {
                              "onUpdate:modelValue": ($event) => unref(form).company_name = $event,
                              type: "text",
                              required: "",
                              class: ["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500", { "border-red-500": unref(form).errors.company_name }]
                            }, null, 10, ["onUpdate:modelValue"]), [
                              [vModelText, unref(form).company_name]
                            ]),
                            unref(form).errors.company_name ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "text-red-500 text-sm mt-1"
                            }, toDisplayString(unref(form).errors.company_name), 1)) : createCommentVNode("", true)
                          ]),
                          createVNode("div", null, [
                            createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, " اسم الشركة (إنجليزي) "),
                            withDirectives(createVNode("input", {
                              "onUpdate:modelValue": ($event) => unref(form).company_name_en = $event,
                              type: "text",
                              class: ["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500", { "border-red-500": unref(form).errors.company_name_en }]
                            }, null, 10, ["onUpdate:modelValue"]), [
                              [vModelText, unref(form).company_name_en]
                            ]),
                            unref(form).errors.company_name_en ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "text-red-500 text-sm mt-1"
                            }, toDisplayString(unref(form).errors.company_name_en), 1)) : createCommentVNode("", true)
                          ]),
                          createVNode("div", null, [
                            createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, " رقم الترخيص * "),
                            withDirectives(createVNode("input", {
                              "onUpdate:modelValue": ($event) => unref(form).license_number = $event,
                              type: "text",
                              required: "",
                              class: ["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500", { "border-red-500": unref(form).errors.license_number }]
                            }, null, 10, ["onUpdate:modelValue"]), [
                              [vModelText, unref(form).license_number]
                            ]),
                            unref(form).errors.license_number ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "text-red-500 text-sm mt-1"
                            }, toDisplayString(unref(form).errors.license_number), 1)) : createCommentVNode("", true)
                          ]),
                          createVNode("div", null, [
                            createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, " تاريخ انتهاء الترخيص * "),
                            withDirectives(createVNode("input", {
                              "onUpdate:modelValue": ($event) => unref(form).license_expiry = $event,
                              type: "date",
                              required: "",
                              class: ["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500", { "border-red-500": unref(form).errors.license_expiry }]
                            }, null, 10, ["onUpdate:modelValue"]), [
                              [vModelText, unref(form).license_expiry]
                            ]),
                            unref(form).errors.license_expiry ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "text-red-500 text-sm mt-1"
                            }, toDisplayString(unref(form).errors.license_expiry), 1)) : createCommentVNode("", true)
                          ])
                        ])
                      ]),
                      createVNode("div", null, [
                        createVNode("h3", { class: "text-lg font-medium text-gray-900 mb-4" }, "معلومات الاتصال"),
                        createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-6" }, [
                          createVNode("div", null, [
                            createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, " الشخص المسؤول (عربي) * "),
                            withDirectives(createVNode("input", {
                              "onUpdate:modelValue": ($event) => unref(form).contact_person = $event,
                              type: "text",
                              required: "",
                              class: ["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500", { "border-red-500": unref(form).errors.contact_person }]
                            }, null, 10, ["onUpdate:modelValue"]), [
                              [vModelText, unref(form).contact_person]
                            ]),
                            unref(form).errors.contact_person ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "text-red-500 text-sm mt-1"
                            }, toDisplayString(unref(form).errors.contact_person), 1)) : createCommentVNode("", true)
                          ]),
                          createVNode("div", null, [
                            createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, " الشخص المسؤول (إنجليزي) "),
                            withDirectives(createVNode("input", {
                              "onUpdate:modelValue": ($event) => unref(form).contact_person_en = $event,
                              type: "text",
                              class: ["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500", { "border-red-500": unref(form).errors.contact_person_en }]
                            }, null, 10, ["onUpdate:modelValue"]), [
                              [vModelText, unref(form).contact_person_en]
                            ]),
                            unref(form).errors.contact_person_en ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "text-red-500 text-sm mt-1"
                            }, toDisplayString(unref(form).errors.contact_person_en), 1)) : createCommentVNode("", true)
                          ]),
                          createVNode("div", null, [
                            createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, " البريد الإلكتروني * "),
                            withDirectives(createVNode("input", {
                              "onUpdate:modelValue": ($event) => unref(form).email = $event,
                              type: "email",
                              required: "",
                              class: ["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500", { "border-red-500": unref(form).errors.email }]
                            }, null, 10, ["onUpdate:modelValue"]), [
                              [vModelText, unref(form).email]
                            ]),
                            unref(form).errors.email ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "text-red-500 text-sm mt-1"
                            }, toDisplayString(unref(form).errors.email), 1)) : createCommentVNode("", true)
                          ]),
                          createVNode("div", null, [
                            createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, " رقم الهاتف * "),
                            withDirectives(createVNode("input", {
                              "onUpdate:modelValue": ($event) => unref(form).phone = $event,
                              type: "tel",
                              required: "",
                              class: ["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500", { "border-red-500": unref(form).errors.phone }]
                            }, null, 10, ["onUpdate:modelValue"]), [
                              [vModelText, unref(form).phone]
                            ]),
                            unref(form).errors.phone ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "text-red-500 text-sm mt-1"
                            }, toDisplayString(unref(form).errors.phone), 1)) : createCommentVNode("", true)
                          ])
                        ])
                      ]),
                      createVNode("div", null, [
                        createVNode("h3", { class: "text-lg font-medium text-gray-900 mb-4" }, "معلومات العنوان"),
                        createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-6" }, [
                          createVNode("div", null, [
                            createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, " العنوان (عربي) * "),
                            withDirectives(createVNode("textarea", {
                              "onUpdate:modelValue": ($event) => unref(form).address = $event,
                              rows: "3",
                              required: "",
                              class: ["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500", { "border-red-500": unref(form).errors.address }]
                            }, null, 10, ["onUpdate:modelValue"]), [
                              [vModelText, unref(form).address]
                            ]),
                            unref(form).errors.address ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "text-red-500 text-sm mt-1"
                            }, toDisplayString(unref(form).errors.address), 1)) : createCommentVNode("", true)
                          ]),
                          createVNode("div", null, [
                            createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, " العنوان (إنجليزي) "),
                            withDirectives(createVNode("textarea", {
                              "onUpdate:modelValue": ($event) => unref(form).address_en = $event,
                              rows: "3",
                              class: ["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500", { "border-red-500": unref(form).errors.address_en }]
                            }, null, 10, ["onUpdate:modelValue"]), [
                              [vModelText, unref(form).address_en]
                            ]),
                            unref(form).errors.address_en ? (openBlock(), createBlock("div", {
                              key: 0,
                              class: "text-red-500 text-sm mt-1"
                            }, toDisplayString(unref(form).errors.address_en), 1)) : createCommentVNode("", true)
                          ])
                        ])
                      ]),
                      createVNode("div", null, [
                        createVNode("h3", { class: "text-lg font-medium text-gray-900 mb-4" }, "الخدمات المقدمة"),
                        createVNode("div", { class: "grid grid-cols-2 md:grid-cols-4 gap-4" }, [
                          (openBlock(), createBlock(Fragment, null, renderList(availableServices, (service) => {
                            return createVNode("div", {
                              key: service,
                              class: "flex items-center"
                            }, [
                              withDirectives(createVNode("input", {
                                id: service,
                                "onUpdate:modelValue": ($event) => unref(form).services = $event,
                                value: service,
                                type: "checkbox",
                                class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                              }, null, 8, ["id", "onUpdate:modelValue", "value"]), [
                                [vModelCheckbox, unref(form).services]
                              ]),
                              createVNode("label", {
                                for: service,
                                class: "mr-2 text-sm text-gray-700"
                              }, toDisplayString(getServiceName(service)), 9, ["for"])
                            ]);
                          }), 64))
                        ]),
                        unref(form).errors.services ? (openBlock(), createBlock("div", {
                          key: 0,
                          class: "text-red-500 text-sm mt-1"
                        }, toDisplayString(unref(form).errors.services), 1)) : createCommentVNode("", true)
                      ]),
                      createVNode("div", { class: "grid grid-cols-1 md:grid-cols-2 gap-6" }, [
                        createVNode("div", null, [
                          createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, " الحالة "),
                          withDirectives(createVNode("select", {
                            "onUpdate:modelValue": ($event) => unref(form).status = $event,
                            class: ["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500", { "border-red-500": unref(form).errors.status }]
                          }, [
                            createVNode("option", { value: "active" }, "نشط"),
                            createVNode("option", { value: "inactive" }, "غير نشط"),
                            createVNode("option", { value: "suspended" }, "معلق")
                          ], 10, ["onUpdate:modelValue"]), [
                            [vModelSelect, unref(form).status]
                          ]),
                          unref(form).errors.status ? (openBlock(), createBlock("div", {
                            key: 0,
                            class: "text-red-500 text-sm mt-1"
                          }, toDisplayString(unref(form).errors.status), 1)) : createCommentVNode("", true)
                        ]),
                        createVNode("div", null, [
                          createVNode("label", { class: "block text-sm font-medium text-gray-700 mb-2" }, " ملاحظات "),
                          withDirectives(createVNode("textarea", {
                            "onUpdate:modelValue": ($event) => unref(form).notes = $event,
                            rows: "3",
                            class: ["w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500", { "border-red-500": unref(form).errors.notes }]
                          }, null, 10, ["onUpdate:modelValue"]), [
                            [vModelText, unref(form).notes]
                          ]),
                          unref(form).errors.notes ? (openBlock(), createBlock("div", {
                            key: 0,
                            class: "text-red-500 text-sm mt-1"
                          }, toDisplayString(unref(form).errors.notes), 1)) : createCommentVNode("", true)
                        ])
                      ]),
                      createVNode("div", { class: "flex justify-end space-x-3 pt-6 border-t border-gray-200" }, [
                        createVNode(_component_Link, {
                          href: _ctx.route("agents.index"),
                          class: "bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                        }, {
                          default: withCtx(() => [
                            createTextVNode(" إلغاء ")
                          ]),
                          _: 1
                        }, 8, ["href"]),
                        createVNode("button", {
                          type: "submit",
                          disabled: unref(form).processing,
                          class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                        }, [
                          unref(form).processing ? (openBlock(), createBlock("span", { key: 0 }, "جاري الحفظ...")) : (openBlock(), createBlock("span", { key: 1 }, "حفظ"))
                        ], 8, ["disabled"])
                      ])
                    ], 32)
                  ])
                ])
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<!--]-->`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("resources/js/Pages/Agents/Create.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
export {
  _sfc_main as default
};
