import{f as r,D as d,c as t,o as s,d as i,t as l,z as m,H as p,m as _,B as f,v}from"./app-CdA4UbU6.js";const g={class:"text-sm text-red-600"},S={__name:"InputError",props:{message:{type:String}},setup(e){return(a,o)=>r((s(),t("div",null,[i("p",g,l(e.message),1)],512)),[[d,e.message]])}},x={class:"block text-sm font-medium text-gray-700"},h={key:0},y={key:1},V={__name:"InputLabel",props:{value:{type:String}},setup(e){return(a,o)=>(s(),t("label",x,[e.value?(s(),t("span",h,l(e.value),1)):(s(),t("span",y,[m(a.$slots,"default")]))]))}},B={__name:"TextInput",props:{modelValue:{type:String,required:!0},modelModifiers:{}},emits:["update:modelValue"],setup(e,{expose:a}){const o=p(e,"modelValue"),n=_(null);return f(()=>{n.value.hasAttribute("autofocus")&&n.value.focus()}),a({focus:()=>n.value.focus()}),(b,u)=>r((s(),t("input",{class:"rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500","onUpdate:modelValue":u[0]||(u[0]=c=>o.value=c),ref_key:"input",ref:n},null,512)),[[v,o.value]])}};export{V as _,B as a,S as b};
