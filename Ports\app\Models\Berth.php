<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Berth extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'terminal',
        'type',
        'length',
        'width',
        'depth',
        'max_draft',
        'max_loa',
        'max_beam',
        'max_dwt',
        'status',
        'facilities',
        'equipment',
        'hourly_rate',
        'daily_rate',
        'notes',
        'coordinates',
        'metadata',
    ];

    protected $casts = [
        'length' => 'decimal:2',
        'width' => 'decimal:2',
        'depth' => 'decimal:2',
        'max_draft' => 'decimal:2',
        'max_loa' => 'decimal:2',
        'max_beam' => 'decimal:2',
        'max_dwt' => 'decimal:2',
        'hourly_rate' => 'decimal:2',
        'daily_rate' => 'decimal:2',
        'facilities' => 'array',
        'equipment' => 'array',
        'coordinates' => 'array',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'available',
    ];

    // العلاقات
    public function bookings(): HasMany
    {
        return $this->hasMany(BerthBooking::class);
    }

    public function arrivals(): HasMany
    {
        return $this->hasMany(Arrival::class);
    }

    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    // النطاقات
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByTerminal($query, $terminal)
    {
        return $query->where('terminal', $terminal);
    }

    public function scopeCanAccommodateShip($query, $length, $draft, $dwt)
    {
        return $query->where('max_loa', '>=', $length)
            ->where('max_draft', '>=', $draft)
            ->where('max_dwt', '>=', $dwt);
    }

    // الخصائص المحسوبة
    public function getIsAvailableAttribute(): bool
    {
        return $this->status === 'available';
    }

    public function getIsOccupiedAttribute(): bool
    {
        return $this->status === 'occupied';
    }

    public function getCurrentBookingAttribute()
    {
        return $this->bookings()
            ->where('status', 'active')
            ->where('start_time', '<=', now())
            ->where('end_time', '>=', now())
            ->first();
    }

    public function getUpcomingBookingsAttribute()
    {
        return $this->bookings()
            ->where('status', 'confirmed')
            ->where('start_time', '>', now())
            ->orderBy('start_time')
            ->get();
    }

    public function canAccommodateShip(Ship $ship): bool
    {
        return $this->max_loa >= $ship->length &&
               $this->max_draft >= $ship->draft &&
               $this->max_dwt >= $ship->deadweight;
    }

    public function isAvailableForPeriod($startTime, $endTime): bool
    {
        return !$this->bookings()
            ->where('status', '!=', 'cancelled')
            ->where(function ($query) use ($startTime, $endTime) {
                $query->whereBetween('start_time', [$startTime, $endTime])
                    ->orWhereBetween('end_time', [$startTime, $endTime])
                    ->orWhere(function ($q) use ($startTime, $endTime) {
                        $q->where('start_time', '<=', $startTime)
                          ->where('end_time', '>=', $endTime);
                    });
            })
            ->exists();
    }
}
