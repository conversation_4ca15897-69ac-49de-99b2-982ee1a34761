<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, useForm, Link } from '@inertiajs/vue3';

const form = useForm({
    name: '',
    name_en: '',
    imo_number: '',
    call_sign: '',
    flag_country: '',
    ship_type: '',
    length: '',
    width: '',
    draft: '',
    gross_tonnage: '',
    net_tonnage: '',
    deadweight: '',
    year_built: '',
    owner_name: '',
    owner_address: '',
    classification_society: '',
    insurance_company: '',
    status: 'active'
});

const shipTypes = [
    { value: 'container', label: 'حاويات' },
    { value: 'bulk', label: 'بضائع سائبة' },
    { value: 'general', label: 'بضائع عامة' },
    { value: 'liquid', label: 'سوائل' },
    { value: 'passenger', label: 'ركاب' },
    { value: 'ro_ro', label: 'رو رو' }
];

const submit = () => {
    form.post(route('ships.store'));
};
</script>

<template>
    <Head title="إضافة سفينة جديدة" />

    <AuthenticatedLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                إضافة سفينة جديدة
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <form @submit.prevent="submit" class="p-6 space-y-6">
                        <!-- Basic Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">المعلومات الأساسية</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        اسم السفينة (عربي) *
                                    </label>
                                    <input 
                                        v-model="form.name"
                                        type="text" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.name }"
                                    />
                                    <div v-if="form.errors.name" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.name }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        اسم السفينة (إنجليزي)
                                    </label>
                                    <input 
                                        v-model="form.name_en"
                                        type="text" 
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.name_en }"
                                    />
                                    <div v-if="form.errors.name_en" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.name_en }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        رقم IMO *
                                    </label>
                                    <input 
                                        v-model="form.imo_number"
                                        type="text" 
                                        required
                                        placeholder="IMO1234567"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.imo_number }"
                                    />
                                    <div v-if="form.errors.imo_number" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.imo_number }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        إشارة النداء *
                                    </label>
                                    <input 
                                        v-model="form.call_sign"
                                        type="text" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.call_sign }"
                                    />
                                    <div v-if="form.errors.call_sign" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.call_sign }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        دولة العلم *
                                    </label>
                                    <input 
                                        v-model="form.flag_country"
                                        type="text" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.flag_country }"
                                    />
                                    <div v-if="form.errors.flag_country" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.flag_country }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        نوع السفينة *
                                    </label>
                                    <select 
                                        v-model="form.ship_type"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.ship_type }"
                                    >
                                        <option value="">اختر نوع السفينة</option>
                                        <option v-for="type in shipTypes" :key="type.value" :value="type.value">
                                            {{ type.label }}
                                        </option>
                                    </select>
                                    <div v-if="form.errors.ship_type" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.ship_type }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Technical Specifications -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">المواصفات التقنية</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الطول (متر) *
                                    </label>
                                    <input 
                                        v-model="form.length"
                                        type="number" 
                                        step="0.01"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.length }"
                                    />
                                    <div v-if="form.errors.length" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.length }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        العرض (متر) *
                                    </label>
                                    <input 
                                        v-model="form.width"
                                        type="number" 
                                        step="0.01"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.width }"
                                    />
                                    <div v-if="form.errors.width" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.width }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الغاطس (متر) *
                                    </label>
                                    <input 
                                        v-model="form.draft"
                                        type="number" 
                                        step="0.01"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.draft }"
                                    />
                                    <div v-if="form.errors.draft" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.draft }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الحمولة الإجمالية (طن) *
                                    </label>
                                    <input 
                                        v-model="form.gross_tonnage"
                                        type="number" 
                                        step="0.01"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.gross_tonnage }"
                                    />
                                    <div v-if="form.errors.gross_tonnage" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.gross_tonnage }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الحمولة الصافية (طن) *
                                    </label>
                                    <input 
                                        v-model="form.net_tonnage"
                                        type="number" 
                                        step="0.01"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.net_tonnage }"
                                    />
                                    <div v-if="form.errors.net_tonnage" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.net_tonnage }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        الحمولة الميتة (طن) *
                                    </label>
                                    <input 
                                        v-model="form.deadweight"
                                        type="number" 
                                        step="0.01"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.deadweight }"
                                    />
                                    <div v-if="form.errors.deadweight" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.deadweight }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        سنة البناء *
                                    </label>
                                    <input 
                                        v-model="form.year_built"
                                        type="number" 
                                        :min="1900"
                                        :max="new Date().getFullYear()"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.year_built }"
                                    />
                                    <div v-if="form.errors.year_built" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.year_built }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Owner Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات المالك</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        اسم المالك *
                                    </label>
                                    <input 
                                        v-model="form.owner_name"
                                        type="text" 
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.owner_name }"
                                    />
                                    <div v-if="form.errors.owner_name" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.owner_name }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        عنوان المالك
                                    </label>
                                    <input 
                                        v-model="form.owner_address"
                                        type="text" 
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.owner_address }"
                                    />
                                    <div v-if="form.errors.owner_address" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.owner_address }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        جمعية التصنيف
                                    </label>
                                    <input 
                                        v-model="form.classification_society"
                                        type="text" 
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.classification_society }"
                                    />
                                    <div v-if="form.errors.classification_society" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.classification_society }}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        شركة التأمين
                                    </label>
                                    <input 
                                        v-model="form.insurance_company"
                                        type="text" 
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.insurance_company }"
                                    />
                                    <div v-if="form.errors.insurance_company" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.insurance_company }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Status -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                الحالة
                            </label>
                            <select 
                                v-model="form.status"
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                :class="{ 'border-red-500': form.errors.status }"
                            >
                                <option value="active">نشطة</option>
                                <option value="inactive">غير نشطة</option>
                                <option value="blacklisted">محظورة</option>
                            </select>
                            <div v-if="form.errors.status" class="text-red-500 text-sm mt-1">
                                {{ form.errors.status }}
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                            <Link :href="route('ships.index')" 
                                  class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                إلغاء
                            </Link>
                            <button 
                                type="submit" 
                                :disabled="form.processing"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                            >
                                <span v-if="form.processing">جاري الحفظ...</span>
                                <span v-else>حفظ</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
