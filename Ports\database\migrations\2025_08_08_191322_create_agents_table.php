<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('agents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('company_name');
            $table->string('company_name_en')->nullable();
            $table->string('license_number')->unique();
            $table->string('tax_number')->nullable();
            $table->string('commercial_register')->nullable();
            $table->text('address');
            $table->text('address_en')->nullable();
            $table->string('phone');
            $table->string('mobile')->nullable();
            $table->string('fax')->nullable();
            $table->string('email')->unique();
            $table->string('website')->nullable();
            $table->string('contact_person');
            $table->string('contact_person_en')->nullable();
            $table->string('contact_phone')->nullable();
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            $table->date('license_expiry');
            $table->json('services')->nullable(); // خدمات الوكيل
            $table->json('metadata')->nullable(); // بيانات إضافية
            $table->timestamps();

            $table->index(['status', 'license_expiry']);
            $table->index('company_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agents');
    }
};
