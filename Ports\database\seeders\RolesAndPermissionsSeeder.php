<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run(): void
    {
        // إنشاء الصلاحيات
        $permissions = [
            // إدارة النظام
            'manage_system',
            'manage_users',
            'manage_roles',
            'manage_permissions',
            'view_reports',
            'manage_settings',

            // الوكلاء الملاحيين
            'access_agent_dashboard',
            'manage_voyages',
            'view_ships',
            'manage_arrivals',
            'manage_departures',
            'view_invoices',
            'manage_documents',

            // برج المراقبة
            'access_control_tower',
            'view_port_map',
            'track_ships',
            'log_events',
            'manage_weather',
            'emergency_alerts',

            // لجنة حرية المخالطة
            'access_clearance_committee',
            'review_clearances',
            'approve_clearances',
            'reject_clearances',
            'vote_clearances',

            // الإرشاد البحري
            'access_pilotage',
            'manage_pilots',
            'assign_pilots',
            'view_pilot_schedules',
            'manage_pilot_reports',

            // إدارة الأرصفة
            'access_berth_management',
            'manage_berths',
            'book_berths',
            'approve_bookings',
            'view_berth_schedules',

            // التفتيش
            'access_inspection',
            'conduct_inspections',
            'manage_inspection_items',
            'approve_inspections',
            'view_inspection_reports',

            // الإدارة المالية
            'access_financial',
            'manage_fees',
            'generate_invoices',
            'process_payments',
            'view_financial_reports',
            'manage_billing',

            // الإدارة العليا
            'access_executive_dashboard',
            'view_kpis',
            'view_analytics',
            'manage_strategic_reports',
            'approve_major_decisions',

            // الصيانة
            'access_maintenance',
            'create_maintenance_requests',
            'assign_maintenance',
            'approve_maintenance',
            'complete_maintenance',

            // العمليات
            'access_operations',
            'manage_operations',
            'log_operations',
            'supervise_operations',
            'view_operation_reports',

            // التنبيهات
            'manage_alerts',
            'send_notifications',
            'emergency_broadcast',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // إنشاء الأدوار
        $roles = [
            'admin' => [
                'display_name' => 'مدير النظام',
                'description' => 'صلاحيات كاملة على النظام',
                'permissions' => $permissions, // جميع الصلاحيات
            ],
            'port_manager' => [
                'display_name' => 'مدير الميناء',
                'description' => 'إدارة عامة للميناء',
                'permissions' => [
                    'access_executive_dashboard',
                    'view_kpis',
                    'view_analytics',
                    'manage_strategic_reports',
                    'approve_major_decisions',
                    'view_reports',
                    'emergency_alerts',
                    'emergency_broadcast',
                ],
            ],
            'agent' => [
                'display_name' => 'وكيل ملاحي',
                'description' => 'وكيل ملاحي للسفن',
                'permissions' => [
                    'access_agent_dashboard',
                    'manage_voyages',
                    'view_ships',
                    'manage_arrivals',
                    'manage_departures',
                    'view_invoices',
                    'manage_documents',
                ],
            ],
            'control_tower' => [
                'display_name' => 'برج المراقبة',
                'description' => 'مراقبة حركة السفن',
                'permissions' => [
                    'access_control_tower',
                    'view_port_map',
                    'track_ships',
                    'log_events',
                    'manage_weather',
                    'emergency_alerts',
                ],
            ],
            'clearance_officer' => [
                'display_name' => 'موظف تصاريح',
                'description' => 'مراجعة وموافقة التصاريح',
                'permissions' => [
                    'access_clearance_committee',
                    'review_clearances',
                    'approve_clearances',
                    'reject_clearances',
                    'vote_clearances',
                ],
            ],
            'pilot' => [
                'display_name' => 'مرشد بحري',
                'description' => 'إرشاد السفن',
                'permissions' => [
                    'access_pilotage',
                    'view_pilot_schedules',
                    'manage_pilot_reports',
                ],
            ],
            'pilot_coordinator' => [
                'display_name' => 'منسق الإرشاد',
                'description' => 'تنسيق عمليات الإرشاد',
                'permissions' => [
                    'access_pilotage',
                    'manage_pilots',
                    'assign_pilots',
                    'view_pilot_schedules',
                    'manage_pilot_reports',
                ],
            ],
            'berth_manager' => [
                'display_name' => 'مدير الأرصفة',
                'description' => 'إدارة الأرصفة والحجوزات',
                'permissions' => [
                    'access_berth_management',
                    'manage_berths',
                    'book_berths',
                    'approve_bookings',
                    'view_berth_schedules',
                ],
            ],
            'inspector' => [
                'display_name' => 'مفتش',
                'description' => 'تفتيش السفن',
                'permissions' => [
                    'access_inspection',
                    'conduct_inspections',
                    'manage_inspection_items',
                    'view_inspection_reports',
                ],
            ],
            'chief_inspector' => [
                'display_name' => 'كبير المفتشين',
                'description' => 'إدارة عمليات التفتيش',
                'permissions' => [
                    'access_inspection',
                    'conduct_inspections',
                    'manage_inspection_items',
                    'approve_inspections',
                    'view_inspection_reports',
                ],
            ],
            'financial_officer' => [
                'display_name' => 'موظف مالي',
                'description' => 'إدارة الشؤون المالية',
                'permissions' => [
                    'access_financial',
                    'manage_fees',
                    'generate_invoices',
                    'process_payments',
                    'view_financial_reports',
                    'manage_billing',
                ],
            ],
            'maintenance_technician' => [
                'display_name' => 'فني صيانة',
                'description' => 'تنفيذ أعمال الصيانة',
                'permissions' => [
                    'access_maintenance',
                    'create_maintenance_requests',
                    'complete_maintenance',
                ],
            ],
            'maintenance_supervisor' => [
                'display_name' => 'مشرف صيانة',
                'description' => 'إشراف على أعمال الصيانة',
                'permissions' => [
                    'access_maintenance',
                    'create_maintenance_requests',
                    'assign_maintenance',
                    'approve_maintenance',
                    'complete_maintenance',
                ],
            ],
            'operations_supervisor' => [
                'display_name' => 'مشرف عمليات',
                'description' => 'إشراف على العمليات',
                'permissions' => [
                    'access_operations',
                    'manage_operations',
                    'log_operations',
                    'supervise_operations',
                    'view_operation_reports',
                ],
            ],
        ];

        foreach ($roles as $roleName => $roleData) {
            $role = Role::firstOrCreate(['name' => $roleName]);
            $role->syncPermissions($roleData['permissions']);
        }
    }
}
