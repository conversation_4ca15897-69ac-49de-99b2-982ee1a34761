<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Voyage extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'ship_id',
        'agent_id',
        'voyage_number',
        'origin_port',
        'destination_port',
        'last_port',
        'next_port',
        'voyage_type',
        'cargo_type',
        'cargo_quantity',
        'cargo_unit',
        'cargo_description',
        'crew_count',
        'passenger_count',
        'eta',
        'etd',
        'ata',
        'atd',
        'status',
        'purpose',
        'dangerous_goods',
        'crew_list',
        'passenger_list',
        'metadata',
    ];

    protected $casts = [
        'cargo_quantity' => 'decimal:2',
        'crew_count' => 'integer',
        'passenger_count' => 'integer',
        'eta' => 'datetime',
        'etd' => 'datetime',
        'ata' => 'datetime',
        'atd' => 'datetime',
        'dangerous_goods' => 'array',
        'crew_list' => 'array',
        'passenger_list' => 'array',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'planned',
        'passenger_count' => 0,
    ];

    // العلاقات
    public function ship(): BelongsTo
    {
        return $this->belongsTo(Ship::class);
    }

    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

    public function arrival(): HasOne
    {
        return $this->hasOne(Arrival::class);
    }

    public function departure(): HasOne
    {
        return $this->hasOne(Departure::class);
    }

    public function berthBookings(): HasMany
    {
        return $this->hasMany(BerthBooking::class);
    }

    public function operations(): HasMany
    {
        return $this->hasMany(Operation::class);
    }

    public function inspections(): HasMany
    {
        return $this->hasMany(Inspection::class);
    }

    public function pilotAssignments(): HasMany
    {
        return $this->hasMany(PilotAssignment::class);
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    // علاقة للحصول على الرصيف المحجوز من خلال آخر حجز نشط
    public function currentBerth()
    {
        return $this->hasOneThrough(
            Berth::class,
            BerthBooking::class,
            'voyage_id',
            'id',
            'id',
            'berth_id'
        )->where('berth_bookings.status', 'active')
         ->latest('berth_bookings.created_at');
    }

    // Helper method للحصول على الرصيف الحالي
    public function getCurrentBerthAttribute()
    {
        return $this->berthBookings()
            ->where('status', 'active')
            ->with('berth')
            ->latest()
            ->first()?->berth;
    }

    // النطاقات
    public function scopeActive($query)
    {
        return $query->whereNotIn('status', ['completed', 'departed', 'cancelled']);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('voyage_type', $type);
    }

    public function scopeArrivingToday($query)
    {
        return $query->whereDate('eta', today())
            ->where('voyage_type', 'arrival');
    }

    public function scopeDepartingToday($query)
    {
        return $query->whereDate('etd', today())
            ->where('voyage_type', 'departure');
    }

    // الخصائص المحسوبة
    public function getIsActiveAttribute(): bool
    {
        return !in_array($this->status, ['completed', 'departed', 'cancelled']);
    }

    public function getHasDangerousCargoAttribute(): bool
    {
        return !empty($this->dangerous_goods);
    }

    public function getEstimatedDurationAttribute(): ?int
    {
        if ($this->eta && $this->etd) {
            return $this->eta->diffInHours($this->etd);
        }
        return null;
    }

    public function getActualDurationAttribute(): ?int
    {
        if ($this->ata && $this->atd) {
            return $this->ata->diffInHours($this->atd);
        }
        return null;
    }
}
