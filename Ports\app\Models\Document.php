<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class Document extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'document_number',
        'title',
        'title_en',
        'type',
        'category',
        'documentable_type',
        'documentable_id',
        'file_path',
        'file_name',
        'file_type',
        'file_size',
        'mime_type',
        'hash',
        'status',
        'issue_date',
        'expiry_date',
        'issuing_authority',
        'reference_number',
        'is_required',
        'is_verified',
        'verified_at',
        'verified_by',
        'verification_notes',
        'uploaded_by',
        'access_permissions',
        'is_confidential',
        'description',
        'tags',
        'version',
        'parent_document_id',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'issue_date' => 'date',
        'expiry_date' => 'date',
        'verified_at' => 'datetime',
        'is_required' => 'boolean',
        'is_verified' => 'boolean',
        'is_confidential' => 'boolean',
        'access_permissions' => 'array',
        'tags' => 'array',
        'version' => 'integer',
        'file_size' => 'integer',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'draft',
        'is_required' => false,
        'is_verified' => false,
        'is_confidential' => false,
        'version' => 1,
    ];

    // العلاقات
    public function documentable(): MorphTo
    {
        return $this->morphTo();
    }

    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    public function parentDocument(): BelongsTo
    {
        return $this->belongsTo(Document::class, 'parent_document_id');
    }

    public function childDocuments()
    {
        return $this->hasMany(Document::class, 'parent_document_id');
    }

    // النطاقات
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    public function scopeUnverified($query)
    {
        return $query->where('is_verified', false);
    }

    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    public function scopeExpiring($query, $days = 30)
    {
        return $query->whereNotNull('expiry_date')
            ->where('expiry_date', '<=', now()->addDays($days))
            ->where('expiry_date', '>', now());
    }

    public function scopeExpired($query)
    {
        return $query->whereNotNull('expiry_date')
            ->where('expiry_date', '<', now());
    }

    public function scopeConfidential($query)
    {
        return $query->where('is_confidential', true);
    }

    // الخصائص المحسوبة
    public function getIsVerifiedAttribute(): bool
    {
        return $this->attributes['is_verified'] ?? false;
    }

    public function getIsExpiredAttribute(): bool
    {
        return $this->expiry_date && $this->expiry_date < now();
    }

    public function getIsExpiringSoonAttribute(): bool
    {
        return $this->expiry_date &&
               $this->expiry_date <= now()->addDays(30) &&
               $this->expiry_date > now();
    }

    public function getDaysUntilExpiryAttribute(): ?int
    {
        if ($this->expiry_date) {
            return now()->diffInDays($this->expiry_date, false);
        }
        return null;
    }

    public function getFullTitleAttribute(): string
    {
        return $this->title_en ?
            $this->title . ' / ' . $this->title_en :
            $this->title;
    }

    public function getFileSizeFormattedAttribute(): string
    {
        $bytes = $this->file_size;

        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    public function getFileUrlAttribute(): ?string
    {
        if ($this->file_path) {
            return Storage::url($this->file_path);
        }
        return null;
    }

    public function getDownloadUrlAttribute(): string
    {
        return route('documents.download', $this->id);
    }

    public function getIsImageAttribute(): bool
    {
        return str_starts_with($this->mime_type, 'image/');
    }

    public function getIsPdfAttribute(): bool
    {
        return $this->mime_type === 'application/pdf';
    }

    public function getIsDocumentAttribute(): bool
    {
        return in_array($this->mime_type, [
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ]);
    }

    // دوال مساعدة
    public function verify(?User $verifier = null, ?string $notes = null): bool
    {
        return $this->update([
            'is_verified' => true,
            'verified_at' => now(),
            'verified_by' => $verifier?->id,
            'verification_notes' => $notes,
            'status' => 'approved',
        ]);
    }

    public function reject(?User $verifier = null, ?string $notes = null): bool
    {
        return $this->update([
            'is_verified' => false,
            'verified_at' => now(),
            'verified_by' => $verifier?->id,
            'verification_notes' => $notes,
            'status' => 'rejected',
        ]);
    }

    public function canBeVerified(): bool
    {
        return $this->status === 'submitted' && !$this->is_verified;
    }

    public function canBeDownloaded(?User $user = null): bool
    {
        if ($this->is_confidential && !$user) {
            return false;
        }

        if ($this->access_permissions && $user) {
            return in_array($user->id, $this->access_permissions) ||
                   $user->hasRole('admin') ||
                   $user->id === $this->uploaded_by;
        }

        return true;
    }

    public function createNewVersion(array $fileData, ?User $uploader = null): self
    {
        return self::create(array_merge($this->toArray(), [
            'id' => null,
            'file_path' => $fileData['file_path'],
            'file_name' => $fileData['file_name'],
            'file_type' => $fileData['file_type'],
            'file_size' => $fileData['file_size'],
            'mime_type' => $fileData['mime_type'],
            'hash' => $fileData['hash'] ?? null,
            'version' => $this->version + 1,
            'parent_document_id' => $this->parent_document_id ?? $this->id,
            'uploaded_by' => $uploader?->id ?? $this->uploaded_by,
            'status' => 'draft',
            'is_verified' => false,
            'verified_at' => null,
            'verified_by' => null,
            'verification_notes' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ]));
    }

    public function getLatestVersion(): self
    {
        if ($this->parent_document_id) {
            return self::where('parent_document_id', $this->parent_document_id)
                ->orWhere('id', $this->parent_document_id)
                ->orderBy('version', 'desc')
                ->first();
        }

        return $this->childDocuments()
            ->orderBy('version', 'desc')
            ->first() ?? $this;
    }

    public function getAllVersions()
    {
        if ($this->parent_document_id) {
            return self::where('parent_document_id', $this->parent_document_id)
                ->orWhere('id', $this->parent_document_id)
                ->orderBy('version', 'desc')
                ->get();
        }

        return collect([$this])->merge(
            $this->childDocuments()->orderBy('version', 'desc')->get()
        );
    }

    public static function generateDocumentNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');

        $lastDocument = self::whereYear('created_at', $year)
            ->whereMonth('created_at', now()->month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastDocument ?
            (int) substr($lastDocument->document_number, -4) + 1 :
            1;

        return sprintf('DOC-%s%s-%04d', $year, $month, $sequence);
    }

    public function deleteFile(): bool
    {
        if ($this->file_path && Storage::exists($this->file_path)) {
            return Storage::delete($this->file_path);
        }
        return true;
    }

    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($document) {
            $document->deleteFile();
        });
    }
}
