<?php

namespace App\Repositories;

use App\Models\Berth;

class BerthRepository extends BaseRepository
{
    public function __construct(Berth $model)
    {
        parent::__construct($model);
    }

    /**
     * Get berths with their current bookings
     */
    public function getWithCurrentBookings()
    {
        return $this->newQuery()
            ->with(['berthBookings' => function ($query) {
                $query->where('start_time', '<=', now())
                      ->where('end_time', '>=', now())
                      ->with(['voyage.ship']);
            }])
            ->get();
    }

    /**
     * Get available berths for a specific time period
     */
    public function getAvailableForPeriod(\DateTime $startTime, \DateTime $endTime)
    {
        return $this->newQuery()
            ->where('status', 'available')
            ->whereDoesntHave('berthBookings', function ($query) use ($startTime, $endTime) {
                $query->where(function ($q) use ($startTime, $endTime) {
                    $q->whereBetween('start_time', [$startTime, $endTime])
                      ->orWhereBetween('end_time', [$startTime, $endTime])
                      ->orWhere(function ($subQ) use ($startTime, $endTime) {
                          $subQ->where('start_time', '<=', $startTime)
                               ->where('end_time', '>=', $endTime);
                      });
                });
            })
            ->get();
    }

    /**
     * Get berths by type
     */
    public function getByType(string $type)
    {
        return $this->newQuery()
            ->where('type', $type)
            ->orderBy('name')
            ->get();
    }

    /**
     * Get berths suitable for ship dimensions
     */
    public function getSuitableForShip(float $length, float $width, float $draft)
    {
        return $this->newQuery()
            ->where('max_length', '>=', $length)
            ->where('max_width', '>=', $width)
            ->where('max_draft', '>=', $draft)
            ->where('status', 'available')
            ->orderBy('name')
            ->get();
    }

    /**
     * Get berth statistics by status
     */
    public function getStatisticsByStatus()
    {
        return $this->newQuery()
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();
    }

    /**
     * Get berth utilization data
     */
    public function getUtilizationData(\DateTime $startDate, \DateTime $endDate)
    {
        return $this->newQuery()
            ->with(['berthBookings' => function ($query) use ($startDate, $endDate) {
                $query->whereBetween('start_time', [$startDate, $endDate])
                      ->orWhereBetween('end_time', [$startDate, $endDate]);
            }])
            ->get();
    }
}
