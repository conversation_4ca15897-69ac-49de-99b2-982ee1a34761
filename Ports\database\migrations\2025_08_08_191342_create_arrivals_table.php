<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('arrivals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('voyage_id')->constrained()->onDelete('cascade');
            $table->foreignId('berth_id')->nullable()->constrained()->onDelete('set null');
            $table->string('arrival_number')->unique();
            $table->datetime('reported_eta');
            $table->datetime('confirmed_eta')->nullable();
            $table->datetime('actual_arrival')->nullable();
            $table->datetime('pilot_boarding_time')->nullable();
            $table->datetime('berthing_time')->nullable();
            $table->enum('status', ['reported', 'confirmed', 'pilot_assigned', 'approaching', 'arrived', 'berthed'])
                ->default('reported');
            $table->text('arrival_reason');
            $table->boolean('requires_pilot')->default(true);
            $table->boolean('requires_tugboat')->default(false);
            $table->boolean('requires_quarantine')->default(false);
            $table->boolean('has_dangerous_cargo')->default(false);
            $table->boolean('has_waste')->default(false);
            $table->text('special_requirements')->nullable();
            $table->json('port_clearances')->nullable(); // تصاريح الجهات
            $table->json('documents_submitted')->nullable(); // الوثائق المقدمة
            $table->json('services_requested')->nullable(); // الخدمات المطلوبة
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['status', 'confirmed_eta']);
            $table->index('arrival_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('arrivals');
    }
};
