<?php

namespace App\Services;

use App\Models\Voyage;
use App\Repositories\VoyageRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class VoyageService
{
    public function __construct(
        private VoyageRepository $voyageRepository
    ) {}

    /**
     * Get all voyages with pagination
     */
    public function getAllVoyages(int $perPage = 15): LengthAwarePaginator
    {
        return $this->voyageRepository
            ->with(['ship', 'agent', 'arrival', 'departure'])
            ->latest()
            ->paginate($perPage);
    }

    /**
     * Get active voyages
     */
    public function getActiveVoyages(): Collection
    {
        return $this->voyageRepository->getActive();
    }

    /**
     * Find voyage by ID
     */
    public function findVoyage(int $id): ?Voyage
    {
        return $this->voyageRepository
            ->with(['ship', 'agent', 'arrival', 'departure', 'berthBookings', 'operations', 'inspections'])
            ->find($id);
    }

    /**
     * Create new voyage
     */
    public function createVoyage(array $data): Voyage
    {
        $this->validateVoyageData($data);
        
        // Generate voyage number if not provided
        if (!isset($data['voyage_number'])) {
            $data['voyage_number'] = $this->voyageRepository->generateVoyageNumber();
        }

        return $this->voyageRepository->create($data);
    }

    /**
     * Update voyage
     */
    public function updateVoyage(int $id, array $data): bool
    {
        $this->validateVoyageData($data, $id);
        return $this->voyageRepository->update($id, $data);
    }

    /**
     * Delete voyage
     */
    public function deleteVoyage(int $id): bool
    {
        $voyage = $this->voyageRepository->findOrFail($id);
        
        // Check if voyage can be deleted
        if (in_array($voyage->status, ['arrived', 'berthed', 'working'])) {
            throw new \Exception('لا يمكن حذف الرحلة بعد وصول السفينة');
        }

        return $this->voyageRepository->delete($id);
    }

    /**
     * Update voyage status
     */
    public function updateVoyageStatus(int $id, string $status): bool
    {
        $validStatuses = ['planned', 'arrived', 'berthed', 'working', 'completed', 'departed', 'cancelled'];
        
        if (!in_array($status, $validStatuses)) {
            throw new \InvalidArgumentException('حالة غير صحيحة');
        }

        return $this->voyageRepository->updateStatus($id, $status);
    }

    /**
     * Get voyages for agent
     */
    public function getVoyagesForAgent(int $agentId): Collection
    {
        return $this->voyageRepository->getForAgent($agentId);
    }

    /**
     * Get voyages for ship
     */
    public function getVoyagesForShip(int $shipId): Collection
    {
        return $this->voyageRepository->getForShip($shipId);
    }

    /**
     * Get voyages arriving today
     */
    public function getArrivingToday(): Collection
    {
        return $this->voyageRepository->getArrivingToday();
    }

    /**
     * Get voyages departing today
     */
    public function getDepartingToday(): Collection
    {
        return $this->voyageRepository->getDepartingToday();
    }

    /**
     * Get voyages statistics
     */
    public function getStatistics(): array
    {
        return $this->voyageRepository->getStatistics();
    }

    /**
     * Get dashboard data
     */
    public function getDashboardData(): array
    {
        return $this->voyageRepository->getDashboardData();
    }

    /**
     * Get voyage timeline
     */
    public function getVoyageTimeline(int $voyageId): array
    {
        return $this->voyageRepository->getTimeline($voyageId);
    }

    /**
     * Get overdue voyages
     */
    public function getOverdueVoyages(): Collection
    {
        return $this->voyageRepository->getOverdue();
    }

    /**
     * Get voyages requiring attention
     */
    public function getVoyagesRequiringAttention(): Collection
    {
        return $this->voyageRepository->getRequiringAttention();
    }

    /**
     * Get voyages with dangerous cargo
     */
    public function getVoyagesWithDangerousCargo(): Collection
    {
        return $this->voyageRepository->getWithDangerousCargo();
    }

    /**
     * Validate voyage data
     */
    private function validateVoyageData(array $data, ?int $voyageId = null): void
    {
        $rules = [
            'ship_id' => 'required|exists:ships,id',
            'agent_id' => 'required|exists:agents,id',
            'voyage_number' => 'sometimes|string|unique:voyages,voyage_number' . ($voyageId ? ",$voyageId" : ''),
            'origin_port' => 'required|string|max:255',
            'destination_port' => 'required|string|max:255',
            'voyage_type' => 'required|in:arrival,departure,transit',
            'cargo_type' => 'required|in:container,bulk,general,liquid,passenger,empty',
            'cargo_quantity' => 'nullable|numeric|min:0',
            'crew_count' => 'required|integer|min:1',
            'passenger_count' => 'integer|min:0',
            'eta' => 'required|date|after:now',
            'etd' => 'nullable|date|after:eta',
            'purpose' => 'nullable|string',
            'status' => 'in:planned,arrived,berthed,working,completed,departed,cancelled',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    /**
     * Generate voyage report
     */
    public function generateReport(array $filters = []): array
    {
        $query = $this->voyageRepository->query;

        // Apply filters
        if (isset($filters['agent_id'])) {
            $query->where('agent_id', $filters['agent_id']);
        }

        if (isset($filters['ship_id'])) {
            $query->where('ship_id', $filters['ship_id']);
        }

        if (isset($filters['voyage_type'])) {
            $query->where('voyage_type', $filters['voyage_type']);
        }

        if (isset($filters['cargo_type'])) {
            $query->where('cargo_type', $filters['cargo_type']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['date_from'])) {
            $query->where('eta', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('eta', '<=', $filters['date_to']);
        }

        $voyages = $query->with(['ship', 'agent', 'arrival', 'departure'])->get();

        return [
            'voyages' => $voyages,
            'total_count' => $voyages->count(),
            'by_status' => $voyages->groupBy('status')->map->count(),
            'by_type' => $voyages->groupBy('voyage_type')->map->count(),
            'by_cargo_type' => $voyages->groupBy('cargo_type')->map->count(),
            'total_cargo' => $voyages->sum('cargo_quantity'),
            'average_crew' => $voyages->avg('crew_count'),
            'total_passengers' => $voyages->sum('passenger_count'),
        ];
    }

    /**
     * Get voyage performance metrics
     */
    public function getPerformanceMetrics(int $voyageId): array
    {
        $voyage = $this->voyageRepository->findOrFail($voyageId);
        
        $metrics = [
            'voyage' => $voyage,
            'planned_duration' => null,
            'actual_duration' => null,
            'delay_hours' => null,
            'operations_count' => $voyage->operations->count(),
            'completed_operations' => $voyage->operations->where('status', 'completed')->count(),
            'inspections_count' => $voyage->inspections->count(),
            'passed_inspections' => $voyage->inspections->where('result', 'passed')->count(),
        ];

        if ($voyage->eta && $voyage->etd) {
            $metrics['planned_duration'] = $voyage->eta->diffInHours($voyage->etd);
        }

        if ($voyage->arrival && $voyage->departure && 
            $voyage->arrival->actual_arrival && $voyage->departure->actual_departure) {
            $metrics['actual_duration'] = $voyage->arrival->actual_arrival
                ->diffInHours($voyage->departure->actual_departure);
        }

        if ($voyage->arrival && $voyage->arrival->actual_arrival && $voyage->arrival->confirmed_eta) {
            $metrics['delay_hours'] = $voyage->arrival->confirmed_eta
                ->diffInHours($voyage->arrival->actual_arrival, false);
        }

        return $metrics;
    }

    /**
     * Schedule voyage operations
     */
    public function scheduleOperations(int $voyageId, array $operations): bool
    {
        $voyage = $this->voyageRepository->findOrFail($voyageId);
        
        return $this->voyageRepository->transaction(function () use ($voyage, $operations) {
            foreach ($operations as $operationData) {
                $operationData['voyage_id'] = $voyage->id;
                $operationData['operation_number'] = $this->generateOperationNumber();
                
                \App\Models\Operation::create($operationData);
            }
            
            return true;
        });
    }

    /**
     * Generate operation number
     */
    private function generateOperationNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');
        
        $lastOperation = \App\Models\Operation::whereYear('created_at', $year)
            ->whereMonth('created_at', now()->month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastOperation ? 
            (int) substr($lastOperation->operation_number, -4) + 1 : 
            1;

        return sprintf('OP-%s%s-%04d', $year, $month, $sequence);
    }

    /**
     * Get voyages in date range
     */
    public function getVoyagesInDateRange(\DateTime $startDate, \DateTime $endDate): Collection
    {
        return $this->voyageRepository->getInDateRange($startDate, $endDate);
    }

    /**
     * Get port traffic summary
     */
    public function getPortTrafficSummary(\DateTime $date = null): array
    {
        $date = $date ?? now();
        
        $arrivals = $this->voyageRepository->query
            ->whereDate('eta', $date)
            ->where('voyage_type', 'arrival')
            ->with(['ship', 'agent'])
            ->get();

        $departures = $this->voyageRepository->query
            ->whereDate('etd', $date)
            ->where('voyage_type', 'departure')
            ->with(['ship', 'agent'])
            ->get();

        $currentlyInPort = $this->voyageRepository->getByStatus('berthed');

        return [
            'date' => $date,
            'arrivals' => $arrivals,
            'departures' => $departures,
            'currently_in_port' => $currentlyInPort,
            'total_movements' => $arrivals->count() + $departures->count(),
        ];
    }
}
