<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Payment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'invoice_id',
        'payment_number',
        'payment_date',
        'amount',
        'currency',
        'exchange_rate',
        'method',
        'status',
        'reference_number',
        'transaction_id',
        'bank_name',
        'account_number',
        'check_date',
        'check_number',
        'payment_details',
        'gateway_response',
        'processed_at',
        'processed_by',
        'notes',
        'attachments',
        'metadata',
    ];

    protected $casts = [
        'payment_date' => 'date',
        'check_date' => 'date',
        'processed_at' => 'datetime',
        'amount' => 'decimal:2',
        'exchange_rate' => 'decimal:4',
        'gateway_response' => 'array',
        'attachments' => 'array',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'pending',
        'currency' => 'SYP',
        'exchange_rate' => 1,
    ];

    // العلاقات
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    // النطاقات
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByMethod($query, $method)
    {
        return $query->where('method', $method);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeToday($query)
    {
        return $query->whereDate('payment_date', today());
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('payment_date', now()->month)
            ->whereYear('payment_date', now()->year);
    }

    // الخصائص المحسوبة
    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'completed';
    }

    public function getIsPendingAttribute(): bool
    {
        return $this->status === 'pending';
    }

    public function getIsFailedAttribute(): bool
    {
        return $this->status === 'failed';
    }

    public function getIsProcessingAttribute(): bool
    {
        return $this->status === 'processing';
    }

    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 2) . ' ' . $this->currency;
    }

    public function getIsElectronicAttribute(): bool
    {
        return in_array($this->method, ['credit_card', 'online', 'mobile_payment']);
    }

    public function getIsBankTransferAttribute(): bool
    {
        return $this->method === 'bank_transfer';
    }

    public function getIsCheckAttribute(): bool
    {
        return $this->method === 'check';
    }

    public function getIsCashAttribute(): bool
    {
        return $this->method === 'cash';
    }

    public function getHasAttachmentsAttribute(): bool
    {
        return !empty($this->attachments);
    }

    public function getProcessingTimeAttribute(): ?int
    {
        if ($this->processed_at && $this->created_at) {
            return $this->created_at->diffInMinutes($this->processed_at);
        }
        return null;
    }

    // دوال مساعدة
    public function markAsCompleted(?User $processedBy = null): bool
    {
        $data = [
            'status' => 'completed',
            'processed_at' => now(),
        ];

        if ($processedBy) {
            $data['processed_by'] = $processedBy->id;
        }

        $result = $this->update($data);

        if ($result) {
            // تحديث حالة الفاتورة إذا تم دفعها بالكامل
            $this->invoice->refresh();
            if ($this->invoice->is_fully_paid) {
                $this->invoice->markAsPaid();
            }
        }

        return $result;
    }

    public function markAsFailed(?string $reason = null): bool
    {
        return $this->update([
            'status' => 'failed',
            'processed_at' => now(),
            'notes' => $reason ? $this->notes . "\nFailure reason: " . $reason : $this->notes,
        ]);
    }

    public function markAsProcessing(): bool
    {
        return $this->update(['status' => 'processing']);
    }

    public function canBeProcessed(): bool
    {
        return $this->status === 'pending';
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'processing']);
    }

    public function canBeRefunded(): bool
    {
        return $this->status === 'completed';
    }

    public function refund(?string $reason = null): bool
    {
        if (!$this->canBeRefunded()) {
            return false;
        }

        return $this->update([
            'status' => 'refunded',
            'processed_at' => now(),
            'notes' => $reason ? $this->notes . "\nRefund reason: " . $reason : $this->notes,
        ]);
    }

    public static function generatePaymentNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');

        $lastPayment = self::whereYear('created_at', $year)
            ->whereMonth('created_at', now()->month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastPayment ?
            (int) substr($lastPayment->payment_number, -4) + 1 :
            1;

        return sprintf('PAY-%s%s-%04d', $year, $month, $sequence);
    }

    public function validatePaymentData(): array
    {
        $errors = [];

        if ($this->amount <= 0) {
            $errors[] = 'Payment amount must be greater than zero';
        }

        if ($this->method === 'check' && !$this->check_number) {
            $errors[] = 'Check number is required for check payments';
        }

        if ($this->method === 'bank_transfer' && !$this->reference_number) {
            $errors[] = 'Reference number is required for bank transfers';
        }

        if ($this->amount > $this->invoice->amount_due) {
            $errors[] = 'Payment amount cannot exceed the amount due';
        }

        return $errors;
    }
}
