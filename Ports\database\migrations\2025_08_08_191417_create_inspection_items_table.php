<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inspection_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('inspection_id')->constrained()->onDelete('cascade');
            $table->string('item_code');
            $table->string('category');
            $table->string('description');
            $table->text('requirement');
            $table->enum('result', ['compliant', 'non_compliant', 'not_applicable', 'observation'])
                ->nullable();
            $table->text('findings')->nullable();
            $table->text('deficiency_description')->nullable();
            $table->enum('severity', ['minor', 'major', 'critical'])->nullable();
            $table->text('corrective_action')->nullable();
            $table->datetime('correction_deadline')->nullable();
            $table->boolean('corrected')->default(false);
            $table->datetime('corrected_at')->nullable();
            $table->text('correction_evidence')->nullable();
            $table->json('photos')->nullable(); // صور التفتيش
            $table->json('documents')->nullable(); // وثائق مرتبطة
            $table->text('inspector_notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['inspection_id', 'category']);
            $table->index(['result', 'severity']);
            $table->index('corrected');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inspection_items');
    }
};
