{"version": 3, "sources": ["../../laravel-vite-plugin/inertia-helpers/index.js"], "sourcesContent": ["export async function resolvePageComponent(path, pages) {\n    for (const p of (Array.isArray(path) ? path : [path])) {\n        const page = pages[p];\n        if (typeof page === 'undefined') {\n            continue;\n        }\n        return typeof page === 'function' ? page() : page;\n    }\n    throw new Error(`Page not found: ${path}`);\n}\n"], "mappings": ";;;AAAA,eAAsB,qBAAqB,MAAM,OAAO;AACpD,aAAW,KAAM,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI,GAAI;AACnD,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,OAAO,SAAS,aAAa;AAC7B;AAAA,IACJ;AACA,WAAO,OAAO,SAAS,aAAa,KAAK,IAAI;AAAA,EACjD;AACA,QAAM,IAAI,MAAM,mBAAmB,IAAI,EAAE;AAC7C;", "names": []}