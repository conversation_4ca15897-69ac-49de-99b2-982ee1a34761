<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Ship extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'name_en',
        'imo_number',
        'call_sign',
        'mmsi_number',
        'flag_country',
        'ship_type',
        'length',
        'width',
        'draft',
        'gross_tonnage',
        'net_tonnage',
        'deadweight',
        'year_built',
        'classification_society',
        'owner_name',
        'owner_address',
        'operator_name',
        'operator_address',
        'status',
        'certificates',
        'equipment',
        'metadata',
    ];

    protected $casts = [
        'length' => 'decimal:2',
        'width' => 'decimal:2',
        'draft' => 'decimal:2',
        'gross_tonnage' => 'decimal:2',
        'net_tonnage' => 'decimal:2',
        'deadweight' => 'decimal:2',
        'year_built' => 'integer',
        'certificates' => 'array',
        'equipment' => 'array',
        'metadata' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'status' => 'active',
    ];

    // العلاقات
    public function voyages(): HasMany
    {
        return $this->hasMany(Voyage::class);
    }

    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    // النطاقات
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('ship_type', $type);
    }

    public function scopeByFlag($query, $country)
    {
        return $query->where('flag_country', $country);
    }

    // الخصائص المحسوبة
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active';
    }

    public function getFullNameAttribute(): string
    {
        return $this->name_en ?
            $this->name . ' / ' . $this->name_en :
            $this->name;
    }

    public function getAgeAttribute(): int
    {
        return now()->year - $this->year_built;
    }

    public function getCurrentVoyageAttribute()
    {
        return $this->voyages()
            ->whereIn('status', ['planned', 'arrived', 'berthed', 'working'])
            ->latest()
            ->first();
    }
}
