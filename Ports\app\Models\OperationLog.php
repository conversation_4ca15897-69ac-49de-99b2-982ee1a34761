<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class OperationLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'operation_id',
        'user_id',
        'logged_at',
        'event_type',
        'title',
        'description',
        'data_before',
        'data_after',
        'quantity_processed',
        'location',
        'weather_conditions',
        'equipment_status',
        'severity',
        'requires_attention',
        'attachments',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'logged_at' => 'datetime',
        'data_before' => 'array',
        'data_after' => 'array',
        'quantity_processed' => 'decimal:2',
        'weather_conditions' => 'array',
        'equipment_status' => 'array',
        'requires_attention' => 'boolean',
        'attachments' => 'array',
        'metadata' => 'array',
    ];

    protected $attributes = [
        'severity' => 'info',
        'requires_attention' => false,
    ];

    // العلاقات
    public function operation(): BelongsTo
    {
        return $this->belongsTo(Operation::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // النطاقات
    public function scopeBySeverity($query, $severity)
    {
        return $query->where('severity', $severity);
    }

    public function scopeByEventType($query, $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    public function scopeRequiringAttention($query)
    {
        return $query->where('requires_attention', true);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('logged_at', today());
    }

    public function scopeCritical($query)
    {
        return $query->where('severity', 'critical');
    }

    // الخصائص المحسوبة
    public function getIsCriticalAttribute(): bool
    {
        return $this->severity === 'critical';
    }

    public function getIsIncidentAttribute(): bool
    {
        return $this->event_type === 'incident';
    }

    public function getHasAttachmentsAttribute(): bool
    {
        return !empty($this->attachments);
    }

    public function getFormattedLoggedAtAttribute(): string
    {
        return $this->logged_at->format('Y-m-d H:i:s');
    }

    // دوال مساعدة
    public static function logEvent(
        Operation $operation,
        User $user,
        string $eventType,
        string $title,
        ?string $description = null,
        array $additionalData = []
    ): self {
        return self::create(array_merge([
            'operation_id' => $operation->id,
            'user_id' => $user->id,
            'logged_at' => now(),
            'event_type' => $eventType,
            'title' => $title,
            'description' => $description,
        ], $additionalData));
    }

    public static function logStart(Operation $operation, User $user, array $data = []): self
    {
        return self::logEvent(
            $operation,
            $user,
            'start',
            'Operation Started',
            'Operation has been started',
            $data
        );
    }

    public static function logComplete(Operation $operation, User $user, array $data = []): self
    {
        return self::logEvent(
            $operation,
            $user,
            'complete',
            'Operation Completed',
            'Operation has been completed',
            $data
        );
    }

    public static function logIncident(
        Operation $operation,
        User $user,
        string $title,
        string $description,
        string $severity = 'warning',
        array $data = []
    ): self {
        return self::logEvent(
            $operation,
            $user,
            'incident',
            $title,
            $description,
            array_merge($data, [
                'severity' => $severity,
                'requires_attention' => in_array($severity, ['error', 'critical']),
            ])
        );
    }
}
