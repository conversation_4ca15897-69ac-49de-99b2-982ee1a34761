import {
  Axios,
  AxiosError,
  AxiosHeaders,
  Cancel,
  CancelToken,
  CanceledError,
  HttpStatusCode,
  VERSION,
  all,
  axios_default,
  formToJSON,
  getAdapter,
  isAxiosError,
  isCancel,
  mergeConfig,
  spread,
  toFormData
} from "./chunk-IAPECSFJ.js";
import "./chunk-G3PMV62Z.js";
export {
  Axios,
  AxiosError,
  AxiosHeaders,
  Cancel,
  CancelToken,
  CanceledError,
  HttpStatusCode,
  VERSION,
  all,
  axios_default as default,
  formToJSON,
  getAdapter,
  isAxiosError,
  isCancel,
  mergeConfig,
  spread,
  toFormData
};
